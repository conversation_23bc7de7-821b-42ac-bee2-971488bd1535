<?php
/**
 * صفحة استعادة كلمة المرور
 * Forgot Password Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// إعادة توجيه المستخدمين المسجلين
if (isLoggedIn()) {
    redirect('index.php');
}

$error = '';
$success = '';
$step = 'email'; // email, code, reset

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!validateCSRF($csrf)) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        if (isset($_POST['email'])) {
            // الخطوة الأولى: إرسال رمز التحقق
            $email = cleanInput($_POST['email']);
            
            if (empty($email)) {
                $error = 'البريد الإلكتروني مطلوب';
            } elseif (!isValidEmail($email)) {
                $error = 'البريد الإلكتروني غير صحيح';
            } else {
                $db = getDB();
                $user = $db->selectOne("SELECT id, username FROM users WHERE email = ?", [$email]);
                
                if ($user) {
                    // إنشاء رمز التحقق
                    $resetCode = sprintf('%06d', mt_rand(100000, 999999));
                    $resetExpiry = date('Y-m-d H:i:s', time() + 3600); // ساعة واحدة
                    
                    // حفظ رمز التحقق في الجلسة (في التطبيق الحقيقي يُحفظ في قاعدة البيانات)
                    $_SESSION['reset_code'] = $resetCode;
                    $_SESSION['reset_email'] = $email;
                    $_SESSION['reset_expiry'] = $resetExpiry;
                    $_SESSION['reset_user_id'] = $user['id'];
                    
                    // في التطبيق الحقيقي، يتم إرسال الرمز عبر البريد الإلكتروني
                    // هنا سنعرضه للمستخدم مباشرة للاختبار
                    $success = "تم إرسال رمز التحقق إلى بريدك الإلكتروني. رمز التحقق هو: <strong>$resetCode</strong>";
                    $step = 'code';
                } else {
                    $error = 'البريد الإلكتروني غير مسجل في النظام';
                }
            }
        } elseif (isset($_POST['reset_code'])) {
            // الخطوة الثانية: التحقق من الرمز
            $inputCode = cleanInput($_POST['reset_code']);
            
            if (empty($inputCode)) {
                $error = 'رمز التحقق مطلوب';
            } elseif (!isset($_SESSION['reset_code']) || !isset($_SESSION['reset_expiry'])) {
                $error = 'انتهت صلاحية رمز التحقق. حاول مرة أخرى';
                $step = 'email';
            } elseif (time() > strtotime($_SESSION['reset_expiry'])) {
                $error = 'انتهت صلاحية رمز التحقق. حاول مرة أخرى';
                unset($_SESSION['reset_code'], $_SESSION['reset_email'], $_SESSION['reset_expiry'], $_SESSION['reset_user_id']);
                $step = 'email';
            } elseif ($inputCode !== $_SESSION['reset_code']) {
                $error = 'رمز التحقق غير صحيح';
                $step = 'code';
            } else {
                $success = 'تم التحقق من الرمز بنجاح. يمكنك الآن تعيين كلمة مرور جديدة';
                $step = 'reset';
            }
        } elseif (isset($_POST['new_password'])) {
            // الخطوة الثالثة: تعيين كلمة المرور الجديدة
            $newPassword = $_POST['new_password'];
            $confirmPassword = $_POST['confirm_password'];
            
            if (empty($newPassword) || empty($confirmPassword)) {
                $error = 'كلمة المرور وتأكيدها مطلوبان';
                $step = 'reset';
            } elseif (!isStrongPassword($newPassword)) {
                $error = 'كلمة المرور يجب أن تحتوي على 6 أحرف على الأقل مع أرقام وحروف';
                $step = 'reset';
            } elseif ($newPassword !== $confirmPassword) {
                $error = 'كلمات المرور غير متطابقة';
                $step = 'reset';
            } elseif (!isset($_SESSION['reset_user_id'])) {
                $error = 'انتهت صلاحية الجلسة. حاول مرة أخرى';
                $step = 'email';
            } else {
                // تحديث كلمة المرور
                $db = getDB();
                $hashedPassword = hashPassword($newPassword);
                $updated = $db->update("UPDATE users SET password = ? WHERE id = ?", 
                                     [$hashedPassword, $_SESSION['reset_user_id']]);
                
                if ($updated) {
                    // تنظيف الجلسة
                    unset($_SESSION['reset_code'], $_SESSION['reset_email'], $_SESSION['reset_expiry'], $_SESSION['reset_user_id']);
                    
                    $success = 'تم تغيير كلمة المرور بنجاح! يمكنك الآن تسجيل الدخول';
                    $step = 'complete';
                } else {
                    $error = 'حدث خطأ أثناء تحديث كلمة المرور. حاول مرة أخرى';
                    $step = 'reset';
                }
            }
        }
    }
}

// تحديد الخطوة الحالية من الجلسة
if (isset($_SESSION['reset_code']) && isset($_SESSION['reset_expiry'])) {
    if (time() <= strtotime($_SESSION['reset_expiry'])) {
        if ($step === 'email') {
            $step = 'code';
        }
    } else {
        unset($_SESSION['reset_code'], $_SESSION['reset_email'], $_SESSION['reset_expiry'], $_SESSION['reset_user_id']);
        $step = 'email';
    }
}

$csrfToken = generateCSRF();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استعادة كلمة المرور - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-header">
            <h1><?php echo SITE_NAME; ?></h1>
            <p>استعادة كلمة المرور</p>
        </div>
        
        <div class="auth-form">
            <?php if ($step === 'email'): ?>
                <h2>استعادة كلمة المرور</h2>
                <p>أدخل بريدك الإلكتروني وسنرسل لك رمز التحقق</p>
                
                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">إرسال رمز التحقق</button>
                </form>
                
            <?php elseif ($step === 'code'): ?>
                <h2>رمز التحقق</h2>
                <p>أدخل رمز التحقق المرسل إلى بريدك الإلكتروني</p>
                
                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?php echo $success; ?></div>
                <?php endif; ?>
                
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    
                    <div class="form-group">
                        <label for="reset_code">رمز التحقق</label>
                        <input type="text" id="reset_code" name="reset_code" maxlength="6" required>
                        <small>أدخل الرمز المكون من 6 أرقام</small>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">تحقق من الرمز</button>
                </form>
                
            <?php elseif ($step === 'reset'): ?>
                <h2>كلمة المرور الجديدة</h2>
                <p>أدخل كلمة المرور الجديدة</p>
                
                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?php echo $success; ?></div>
                <?php endif; ?>
                
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    
                    <div class="form-group">
                        <label for="new_password">كلمة المرور الجديدة</label>
                        <input type="password" id="new_password" name="new_password" required>
                        <small>6 أحرف على الأقل مع أرقام وحروف</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">تأكيد كلمة المرور</label>
                        <input type="password" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">تحديث كلمة المرور</button>
                </form>
                
            <?php elseif ($step === 'complete'): ?>
                <h2>تم بنجاح!</h2>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?php echo $success; ?></div>
                <?php endif; ?>
                
                <a href="login.php" class="btn btn-primary">تسجيل الدخول</a>
            <?php endif; ?>
            
            <div class="auth-links">
                <p><a href="login.php">العودة إلى تسجيل الدخول</a></p>
                <p>ليس لديك حساب؟ <a href="register.php">إنشاء حساب جديد</a></p>
            </div>
        </div>
    </div>
    
    <script>
        // تركيز تلقائي على الحقل المناسب
        <?php if ($step === 'email'): ?>
            document.getElementById('email').focus();
        <?php elseif ($step === 'code'): ?>
            document.getElementById('reset_code').focus();
        <?php elseif ($step === 'reset'): ?>
            document.getElementById('new_password').focus();
        <?php endif; ?>
        
        // التحقق من قوة كلمة المرور
        <?php if ($step === 'reset'): ?>
        document.getElementById('new_password').addEventListener('input', function() {
            const password = this.value;
            const strength = document.getElementById('password-strength') || document.createElement('div');
            strength.id = 'password-strength';
            
            if (password.length < 6) {
                strength.textContent = 'ضعيفة - يجب أن تكون 6 أحرف على الأقل';
                strength.className = 'password-weak';
            } else if (!/[A-Za-z]/.test(password) || !/[0-9]/.test(password)) {
                strength.textContent = 'متوسطة - أضف أرقام وحروف';
                strength.className = 'password-medium';
            } else {
                strength.textContent = 'قوية';
                strength.className = 'password-strong';
            }
            
            if (!document.getElementById('password-strength')) {
                this.parentNode.appendChild(strength);
            }
        });
        <?php endif; ?>
    </script>
</body>
</html>
