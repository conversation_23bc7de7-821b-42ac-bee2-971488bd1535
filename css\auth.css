/* ملف CSS لصفحات المصادقة */
/* Authentication Pages CSS */

.auth-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.auth-container {
    max-width: 500px;
    width: 100%;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.auth-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.auth-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.auth-form {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 20px;
}

.auth-form h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-size: 1.8rem;
}

.auth-form .form-group {
    margin-bottom: 25px;
}

.auth-form .form-group label {
    color: #555;
    font-weight: 600;
    margin-bottom: 8px;
}

.auth-form .form-group input,
.auth-form .form-group select {
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.auth-form .form-group input:focus,
.auth-form .form-group select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.auth-form .form-group small {
    color: #6c757d;
    font-size: 13px;
    margin-top: 5px;
}

.auth-form .btn {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.auth-form .btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.auth-form .btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.auth-links {
    text-align: center;
    margin-top: 25px;
}

.auth-links p {
    margin-bottom: 10px;
    color: #6c757d;
}

.auth-links a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.auth-links a:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* معلومات القبائل */
.tribe-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.tribe-description {
    display: none;
}

.tribe-description.active {
    display: block;
}

.tribe-description h4 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.tribe-description p {
    color: #6c757d;
    margin-bottom: 10px;
    line-height: 1.5;
}

.tribe-description ul {
    list-style: none;
    padding: 0;
}

.tribe-description li {
    padding: 5px 0;
    color: #495057;
    position: relative;
    padding-right: 20px;
}

.tribe-description li:before {
    content: "✓";
    position: absolute;
    right: 0;
    color: #28a745;
    font-weight: bold;
}

/* مؤشر قوة كلمة المرور */
.password-strength {
    margin-top: 5px;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.password-weak {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.password-medium {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.password-strong {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

/* زر إظهار/إخفاء كلمة المرور */
.toggle-password {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 12px;
    padding: 5px;
}

.form-group {
    position: relative;
}

/* تحذير Caps Lock */
.caps-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    margin-top: 5px;
}

/* مميزات اللعبة */
.game-features {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    color: white;
}

.game-features h3 {
    text-align: center;
    margin-bottom: 25px;
    font-size: 1.5rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.feature {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.feature:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 15px;
    opacity: 0.9;
}

.feature h4 {
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.feature p {
    font-size: 0.9rem;
    opacity: 0.8;
    line-height: 1.4;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .auth-page {
        padding: 10px;
    }
    
    .auth-form {
        padding: 30px 20px;
    }
    
    .auth-header h1 {
        font-size: 2rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .game-features {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .auth-form {
        padding: 25px 15px;
    }
    
    .auth-header h1 {
        font-size: 1.8rem;
    }
    
    .auth-form h2 {
        font-size: 1.5rem;
    }
    
    .feature {
        padding: 15px;
    }
    
    .feature-icon {
        width: 40px;
        height: 40px;
    }
}
