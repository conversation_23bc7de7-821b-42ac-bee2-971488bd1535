/* ملف CSS للعبة */
/* Game CSS File */

.game-page {
    background-color: #f0f2f5;
    min-height: 100vh;
}

/* شريط التنقل العلوي */
.top-nav {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 10px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.top-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-left .logo {
    display: flex;
    align-items: center;
}

.nav-left .logo img {
    width: 40px;
    height: 40px;
    margin-left: 10px;
}

.nav-left .logo span {
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-center .village-selector select {
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    background-color: white;
    color: #333;
    font-size: 14px;
    min-width: 200px;
}

.nav-right .user-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-right .username {
    font-weight: bold;
}

.nav-right .gold {
    display: flex;
    align-items: center;
    background-color: rgba(255,255,255,0.1);
    padding: 5px 10px;
    border-radius: 15px;
}

.nav-right .gold img {
    width: 20px;
    height: 20px;
    margin-left: 5px;
}

.nav-right .messages-link {
    position: relative;
    color: white;
    text-decoration: none;
}

.nav-right .messages-link img {
    width: 24px;
    height: 24px;
}

.nav-right .messages-link .badge {
    position: absolute;
    top: -5px;
    left: -5px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-right .logout-link {
    color: white;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.nav-right .logout-link:hover {
    background-color: rgba(255,255,255,0.1);
}

/* شريط الموارد */
.resources-bar {
    background-color: white;
    padding: 15px 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    border-bottom: 1px solid #e9ecef;
}

.resources-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.resources-bar .resource {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.resources-bar .resource img {
    width: 24px;
    height: 24px;
}

.resources-bar .resource .amount {
    font-weight: bold;
    color: #495057;
    min-width: 60px;
}

.resources-bar .resource .production {
    font-size: 12px;
    color: #28a745;
}

.resources-bar .population {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #e3f2fd;
    padding: 10px 15px;
    border-radius: 8px;
    border: 1px solid #bbdefb;
}

.resources-bar .population img {
    width: 24px;
    height: 24px;
}

.resources-bar .population span {
    font-weight: bold;
    color: #1976d2;
}

/* التنبيهات */
.alerts {
    max-width: 1200px;
    margin: 0 auto;
    padding: 10px 20px;
}

/* المحتوى الرئيسي */
.main-content {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    gap: 20px;
    padding: 20px;
}

/* القائمة الجانبية */
.sidebar {
    width: 250px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: fit-content;
    position: sticky;
    top: 120px;
}

.game-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.game-nav li {
    border-bottom: 1px solid #e9ecef;
}

.game-nav li:last-child {
    border-bottom: none;
}

.game-nav a {
    display: block;
    padding: 15px 20px;
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
}

.game-nav a:hover,
.game-nav a.active {
    background-color: #007bff;
    color: white;
}

/* المحتوى */
.content {
    flex: 1;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 30px;
}

.page-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.page-header h1 {
    color: #2c3e50;
    margin-bottom: 5px;
}

.page-header p {
    color: #6c757d;
    margin: 0;
}

/* نظرة عامة */
.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.overview-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.overview-section h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

/* المباني في النظرة العامة */
.buildings-overview {
    margin-bottom: 15px;
}

.building-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.building-item:last-child {
    border-bottom: none;
}

.building-item img {
    width: 32px;
    height: 32px;
}

.building-item span {
    flex: 1;
}

.building-item .level {
    font-weight: bold;
    color: #007bff;
}

/* الجيش في النظرة العامة */
.army-overview {
    margin-bottom: 15px;
}

.unit-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.unit-item:last-child {
    border-bottom: none;
}

.unit-item img {
    width: 32px;
    height: 32px;
}

.unit-item span {
    flex: 1;
}

.unit-item .count {
    font-weight: bold;
    color: #28a745;
}

/* الأحداث في النظرة العامة */
.events-overview {
    margin-bottom: 15px;
}

.event-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.event-item:last-child {
    border-bottom: none;
}

.event-item .time {
    font-size: 12px;
    color: #6c757d;
    min-width: 60px;
}

.event-item .message {
    flex: 1;
    margin-right: 10px;
}

/* الإحصائيات في النظرة العامة */
.stats-overview {
    margin-bottom: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-item .label {
    color: #6c757d;
}

.stat-item .value {
    font-weight: bold;
    color: #495057;
}

/* ملخص الموارد */
.resources-summary {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.resources-summary .resource {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.resources-summary .resource img {
    width: 24px;
    height: 24px;
}

.resources-summary .resource span {
    font-weight: bold;
    color: #495057;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
        padding: 10px;
    }
    
    .sidebar {
        width: 100%;
        position: static;
    }
    
    .game-nav {
        display: flex;
        overflow-x: auto;
    }
    
    .game-nav ul {
        display: flex;
        min-width: max-content;
    }
    
    .game-nav li {
        border-bottom: none;
        border-left: 1px solid #e9ecef;
    }
    
    .game-nav li:first-child {
        border-left: none;
    }
    
    .game-nav a {
        white-space: nowrap;
        padding: 10px 15px;
    }
    
    .resources-bar {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .resources-bar .resource,
    .resources-bar .population {
        padding: 8px 12px;
    }
    
    .overview-grid {
        grid-template-columns: 1fr;
    }
    
    .content {
        padding: 20px 15px;
    }
    
    .top-nav {
        flex-direction: column;
        gap: 10px;
        padding: 15px 20px;
    }
    
    .nav-center .village-selector select {
        min-width: 150px;
    }
    
    .nav-right .user-info {
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .resources-summary {
        flex-direction: column;
        gap: 10px;
    }
    
    .resources-summary .resource {
        justify-content: center;
    }
    
    .nav-right .user-info {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}
