/* ملف CSS للثكنة */
/* Barracks CSS */

/* استيراد ملف الجيش */
@import url('army.css');

/* تخصيصات إضافية للثكنة */
.barracks-info {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    border: 2px solid #007bff;
}

.barracks-info h3 {
    color: #007bff;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.barracks-info h3::before {
    content: "🏛️";
    font-size: 1.5rem;
}

.barracks-level {
    display: inline-block;
    background-color: #007bff;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
    margin-right: 10px;
}

.barracks-description {
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.6;
}

.barracks-benefits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.benefit-item .icon {
    width: 24px;
    height: 24px;
    background-color: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.8rem;
}

.benefit-item .text {
    color: #495057;
    font-size: 0.9rem;
}

/* تحسينات للوحدات في الثكنة */
.training-units .unit-card {
    position: relative;
    overflow: hidden;
}

.training-units .unit-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #0056b3);
}

.training-units .unit-card.training::before {
    background: linear-gradient(90deg, #ffc107, #e0a800);
    animation: pulse 2s infinite;
}

/* تحسينات لنموذج التدريب */
.training-form {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.training-form .form-group {
    margin-bottom: 15px;
}

.training-form label {
    display: block;
    color: #495057;
    margin-bottom: 5px;
    font-weight: 600;
}

.training-form input[type="number"] {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #ced4da;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.training-form input[type="number"]:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.training-form .btn {
    width: 100%;
    padding: 12px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.training-form .btn:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

.training-form .btn:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* مؤشر التقدم للتدريب */
.training-progress {
    position: relative;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffc107;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.training-progress::before {
    content: "⚡";
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #ffc107;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    animation: bounce 1.5s infinite;
}

.training-progress .status {
    color: #856404;
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 10px;
}

.training-progress .training-timer {
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
    font-family: 'Courier New', monospace;
}

/* تحسينات للجدول */
.units-table {
    border: none;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.units-table thead {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.units-table th {
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.units-table tbody tr {
    transition: background-color 0.3s ease;
}

.units-table tbody tr:hover {
    background-color: #f8f9fa;
}

.units-table td {
    vertical-align: middle;
}

.units-table .unit-name {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.units-table .unit-name img {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

/* مؤشرات الحالة المحسنة */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator.ready {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-indicator.ready::before {
    content: "✓";
    color: #28a745;
}

.status-indicator.training {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-indicator.training::before {
    content: "⏳";
    animation: spin 2s linear infinite;
}

/* تأثيرات الحركة */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-5px); }
    60% { transform: translateX(-50%) translateY(-3px); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* تحسينات للتكلفة الإجمالية */
.total-cost {
    background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
    border: 2px solid #007bff;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.total-cost .cost-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.total-cost .cost-item {
    background-color: white;
    border: 1px solid #007bff;
    border-radius: 6px;
    padding: 8px;
    text-align: center;
    font-weight: 500;
}

/* تحسينات للموارد */
.resources-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #28a745;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

.resources-summary .resource {
    background: linear-gradient(135deg, white 0%, #f8f9fa 100%);
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.resources-summary .resource:hover {
    border-color: #28a745;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.resources-summary .resource img {
    width: 28px;
    height: 28px;
}

.resources-summary .resource span {
    font-size: 1.1rem;
    font-weight: 600;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .barracks-benefits {
        grid-template-columns: 1fr;
    }
    
    .training-progress::before {
        display: none;
    }
    
    .training-progress {
        padding: 15px;
    }
    
    .units-table {
        font-size: 0.8rem;
    }
    
    .units-table th,
    .units-table td {
        padding: 8px 6px;
    }
    
    .units-table .unit-name img {
        width: 24px;
        height: 24px;
    }
    
    .total-cost .cost-breakdown {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .barracks-info {
        padding: 15px;
    }
    
    .training-form {
        padding: 12px;
    }
    
    .training-progress .training-timer {
        font-size: 1.2rem;
    }
    
    .resources-summary {
        padding: 15px;
    }
    
    .resources-summary .resource {
        padding: 10px 12px;
    }
    
    .total-cost .cost-breakdown {
        grid-template-columns: 1fr;
    }
    
    .status-indicator {
        font-size: 0.7rem;
        padding: 4px 8px;
    }
}

/* تحسينات للطباعة */
@media print {
    .training-form,
    .quick-actions {
        display: none;
    }
    
    .units-table {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .units-table th {
        background-color: #f0f0f0 !important;
        color: #000 !important;
    }
}
