<?php
/**
 * ملف الإعدادات العامة للعبة
 * General Game Configuration
 */

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// إعدادات قاعدة البيانات
define('DB_HOST', 'srv1513.hstgr.io');
define('DB_NAME', 'u302460181_hoasb');
define('DB_USER', 'u302460181_hoasb');
define('DB_PASS', '10743211uU@');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الموقع
define('SITE_URL', 'http://localhost/agricultural-game/');
define('SITE_NAME', 'اللعبة الزراعية');
define('SITE_DESCRIPTION', 'لعبة زراعية استراتيجية مشابهة لترافيان');
define('ADMIN_EMAIL', '<EMAIL>');

// إعدادات الأمان
define('SALT', 'your_unique_salt_here_2024');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_TIMEOUT', 900); // 15 دقيقة

// إعدادات اللعبة
define('GAME_SPEED', 1); // سرعة اللعبة (1 = عادي)
define('MAX_VILLAGES_PER_USER', 10);
define('PROTECTION_TIME_HOURS', 72); // وقت الحماية للاعبين الجدد
define('STARTING_RESOURCES', 750); // الموارد الابتدائية
define('STARTING_POPULATION', 2);
define('STARTING_GOLD', 100);

// إعدادات الموارد
define('RESOURCE_TYPES', ['wood', 'clay', 'iron', 'crop']);
define('BASE_PRODUCTION_PER_HOUR', 30);
define('WAREHOUSE_CAPACITY_BASE', 800);
define('GRANARY_CAPACITY_BASE', 800);
define('CRANNY_CAPACITY_BASE', 100);

// إعدادات المباني
define('BUILDING_TYPES', [
    'main_building' => 'المبنى الرئيسي',
    'barracks' => 'الثكنة',
    'stable' => 'الإسطبل',
    'workshop' => 'الورشة',
    'academy' => 'الأكاديمية',
    'smithy' => 'الحداد',
    'rally_point' => 'نقطة التجمع',
    'marketplace' => 'السوق',
    'embassy' => 'السفارة',
    'residence' => 'المقر',
    'palace' => 'القصر',
    'treasury' => 'الخزانة',
    'trade_office' => 'مكتب التجارة',
    'great_barracks' => 'الثكنة الكبرى',
    'great_stable' => 'الإسطبل الكبير',
    'city_wall' => 'سور المدينة',
    'earth_wall' => 'السور الترابي',
    'palisade' => 'السياج',
    'stonemason' => 'البناء',
    'brewery' => 'المخمرة',
    'granary' => 'المخزن',
    'warehouse' => 'المستودع',
    'cranny' => 'المخبأ',
    'trapper' => 'الفخاخ',
    'hero_mansion' => 'قصر البطل',
    'wonder' => 'عجائب الدنيا'
]);

// إعدادات الوحدات العسكرية
define('UNIT_TYPES', [
    'phalanx' => 'الكتيبة',
    'swordsman' => 'المحارب',
    'pathfinder' => 'الكشاف',
    'theutates_thunder' => 'رعد ثيوتاتس',
    'druidrider' => 'فارس الكاهن',
    'haeduan' => 'الهايدوان',
    'ram' => 'الكبش',
    'trebuchet' => 'المنجنيق',
    'chieftain' => 'الزعيم',
    'settler' => 'المستوطن'
]);

// إعدادات القبائل
define('TRIBES', [
    1 => 'الغال',
    2 => 'الرومان',
    3 => 'الجرمان'
]);

// إعدادات الرسائل
define('MESSAGE_TYPES', [
    'normal' => 'عادية',
    'system' => 'النظام',
    'battle_report' => 'تقرير معركة',
    'trade_report' => 'تقرير تجارة'
]);

// إعدادات الملفات والمجلدات
define('UPLOAD_DIR', 'uploads/');
define('IMAGES_DIR', 'images/');
define('LOGS_DIR', 'logs/');
define('TEMP_DIR', 'temp/');

// إعدادات الصور
define('MAX_FILE_SIZE', 2097152); // 2MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);

// إعدادات التوقيت
date_default_timezone_set('Asia/Riyadh');
define('DATE_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd/m/Y H:i');

// إعدادات التطوير
define('DEBUG_MODE', true);
define('SHOW_ERRORS', true);

if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// دوال مساعدة
function formatNumber($number) {
    return number_format($number, 0, '.', ',');
}

function formatTime($seconds) {
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $seconds = $seconds % 60;
    
    if ($hours > 0) {
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    } else {
        return sprintf('%02d:%02d', $minutes, $seconds);
    }
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    return floor($time/31536000) . ' سنة';
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function generateToken() {
    return bin2hex(random_bytes(32));
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

function isAdmin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == true;
}

function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header('Location: index.php');
        exit();
    }
}

function redirect($url) {
    header("Location: $url");
    exit();
}

function showError($message) {
    $_SESSION['error'] = $message;
}

function showSuccess($message) {
    $_SESSION['success'] = $message;
}

function getError() {
    if (isset($_SESSION['error'])) {
        $error = $_SESSION['error'];
        unset($_SESSION['error']);
        return $error;
    }
    return null;
}

function getSuccess() {
    if (isset($_SESSION['success'])) {
        $success = $_SESSION['success'];
        unset($_SESSION['success']);
        return $success;
    }
    return null;
}

// تضمين ملف قاعدة البيانات
require_once 'database.php';
?>
