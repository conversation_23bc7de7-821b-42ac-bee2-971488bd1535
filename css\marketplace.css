/* ملف CSS للسوق */
/* Marketplace CSS */

/* معلومات السوق */
.marketplace-info {
    margin-bottom: 30px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.info-card {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #ffc107;
}

.info-card h3 {
    color: #856404;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(133, 100, 4, 0.2);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item .label {
    color: #856404;
    font-weight: 500;
}

.info-item .value {
    font-weight: bold;
    color: #495057;
}

.info-item .value.available {
    color: #28a745;
}

.info-item .value.unavailable {
    color: #dc3545;
}

/* عرض الموارد */
.resources-display {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.resource-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    border: 1px solid rgba(133, 100, 4, 0.3);
}

.resource-item img {
    width: 24px;
    height: 24px;
}

.resource-item span {
    font-weight: bold;
    color: #495057;
}

/* إنشاء عرض جديد */
.create-offer {
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    border: 2px solid #28a745;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.1);
}

.create-offer h3 {
    color: #28a745;
    margin-bottom: 20px;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.create-offer h3::before {
    content: "➕";
    font-size: 1.5rem;
}

.offer-sections {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 30px;
    align-items: center;
    margin-bottom: 25px;
}

.offer-section,
.request-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 2px solid #e9ecef;
}

.offer-section h4,
.request-section h4 {
    color: #495057;
    margin-bottom: 15px;
    text-align: center;
    font-size: 1.1rem;
}

.resources-input {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.resource-input {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #ced4da;
}

.resource-input img {
    width: 28px;
    height: 28px;
}

.resource-input input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
}

.exchange-arrow {
    font-size: 2rem;
    color: #28a745;
    text-align: center;
    font-weight: bold;
}

.offer-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.offer-actions .btn {
    padding: 12px 25px;
    font-size: 1rem;
    font-weight: 600;
}

/* عروض السوق */
.market-offers {
    margin-bottom: 30px;
}

.market-offers h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.offers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.offer-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.offer-card:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.offer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.trader-info strong {
    color: #495057;
    display: block;
}

.trader-info span {
    color: #6c757d;
    font-size: 0.9rem;
}

.offer-time {
    color: #6c757d;
    font-size: 0.8rem;
}

.offer-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
}

.offer-resources,
.request-resources {
    text-align: center;
}

.offer-resources h5,
.request-resources h5 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.resources-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.resources-list .resource {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #e9ecef;
    font-size: 0.9rem;
    font-weight: 500;
}

.resources-list .resource img {
    width: 20px;
    height: 20px;
}

.exchange-symbol {
    font-size: 1.5rem;
    color: #007bff;
    font-weight: bold;
}

.offer-actions {
    text-align: center;
}

.offer-actions .btn {
    padding: 10px 20px;
    font-weight: 600;
}

.insufficient-resources {
    display: block;
    color: #dc3545;
    font-size: 0.8rem;
    margin-top: 5px;
}

/* عدم وجود عروض */
.no-offers {
    text-align: center;
    padding: 40px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
}

.no-offers p {
    color: #6c757d;
    font-size: 1.1rem;
}

/* عروضي */
.my-offers {
    margin-bottom: 30px;
}

.my-offers h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.offers-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.my-offer-item {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    border: 2px solid #17a2b8;
    border-left: 5px solid #17a2b8;
}

.offer-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.offer-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.offering {
    color: #28a745;
    font-weight: 500;
}

.requesting {
    color: #dc3545;
    font-weight: 500;
}

.offer-status {
    text-align: right;
}

.expires {
    color: #6c757d;
    font-size: 0.9rem;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .resources-display {
        grid-template-columns: 1fr;
    }
    
    .offer-sections {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .exchange-arrow {
        transform: rotate(90deg);
        font-size: 1.5rem;
    }
    
    .resources-input {
        grid-template-columns: 1fr;
    }
    
    .offers-grid {
        grid-template-columns: 1fr;
    }
    
    .offer-content {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .exchange-symbol {
        transform: rotate(90deg);
        font-size: 1.2rem;
    }
    
    .offer-summary {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .offer-status {
        text-align: left;
    }
}

@media (max-width: 480px) {
    .create-offer {
        padding: 20px;
    }
    
    .offer-section,
    .request-section {
        padding: 15px;
    }
    
    .offer-card {
        padding: 15px;
    }
    
    .offer-actions .btn {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
    
    .resource-input {
        padding: 8px;
    }
    
    .resource-input img {
        width: 24px;
        height: 24px;
    }
    
    .resources-list .resource {
        padding: 4px 8px;
        font-size: 0.8rem;
    }
    
    .resources-list .resource img {
        width: 16px;
        height: 16px;
    }
    
    .my-offer-item {
        padding: 12px;
    }
    
    .offering,
    .requesting {
        font-size: 0.9rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .create-offer,
    .offer-actions {
        display: none;
    }
    
    .offer-card {
        border: 1px solid #000;
        box-shadow: none;
        margin-bottom: 20px;
        break-inside: avoid;
    }
    
    .market-offers h3,
    .my-offers h3 {
        color: #000;
    }
}

/* تأثيرات الحركة */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.offer-card {
    animation: slideIn 0.3s ease-out;
}

.resource-input:focus-within {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.offer-card:hover .exchange-symbol {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* تحسينات للوضع المظلم (إذا تم تطبيقه لاحقاً) */
@media (prefers-color-scheme: dark) {
    .create-offer,
    .offer-card,
    .my-offer-item {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .offer-section,
    .request-section {
        background-color: #3d3d3d;
        border-color: #555;
    }
    
    .resource-input {
        background-color: #2d2d2d;
        border-color: #555;
        color: #fff;
    }
    
    .resources-list .resource {
        background-color: #3d3d3d;
        border-color: #555;
        color: #fff;
    }
    
    .no-offers {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
}
