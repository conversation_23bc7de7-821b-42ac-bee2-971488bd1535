/* ملف CSS الأساسي للعبة الزراعية */
/* Main CSS File for Agricultural Game */

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
    direction: rtl;
    text-align: right;
}

/* الحاويات */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* الأزرار */
.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #0056b3;
}

.btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-success {
    background-color: #28a745;
}

.btn-success:hover {
    background-color: #1e7e34;
}

.btn-danger {
    background-color: #dc3545;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-small {
    padding: 5px 10px;
    font-size: 12px;
}

.btn-large {
    padding: 15px 30px;
    font-size: 16px;
}

/* النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: #6c757d;
    font-size: 12px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin-left: 10px;
}

/* التنبيهات */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 5px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-error,
.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* الجداول */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background-color: white;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

table th,
table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #ddd;
}

table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #495057;
}

table tr:hover {
    background-color: #f5f5f5;
}

/* البطاقات */
.card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
}

.card-header h3 {
    margin: 0;
    color: #495057;
}

.card-body {
    padding: 20px;
}

.card-footer {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
}

/* الشبكات */
.grid {
    display: grid;
    gap: 20px;
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* الأيقونات */
.icon {
    width: 20px;
    height: 20px;
    vertical-align: middle;
    margin-left: 5px;
}

.icon-small {
    width: 16px;
    height: 16px;
}

.icon-large {
    width: 32px;
    height: 32px;
}

/* الشارات */
.badge {
    display: inline-block;
    padding: 3px 8px;
    font-size: 12px;
    font-weight: bold;
    color: white;
    background-color: #dc3545;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.badge-primary {
    background-color: #007bff;
}

.badge-success {
    background-color: #28a745;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-info {
    background-color: #17a2b8;
}

/* المساعدات */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-muted {
    color: #6c757d;
}

.text-primary {
    color: #007bff;
}

.text-success {
    color: #28a745;
}

.text-danger {
    color: #dc3545;
}

.text-warning {
    color: #ffc107;
}

.d-none {
    display: none;
}

.d-block {
    display: block;
}

.d-inline {
    display: inline;
}

.d-inline-block {
    display: inline-block;
}

.d-flex {
    display: flex;
}

.justify-content-center {
    justify-content: center;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-center {
    align-items: center;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 5px; }
.mb-2 { margin-bottom: 10px; }
.mb-3 { margin-bottom: 15px; }
.mb-4 { margin-bottom: 20px; }
.mb-5 { margin-bottom: 25px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 5px; }
.mt-2 { margin-top: 10px; }
.mt-3 { margin-top: 15px; }
.mt-4 { margin-top: 20px; }
.mt-5 { margin-top: 25px; }

.p-0 { padding: 0; }
.p-1 { padding: 5px; }
.p-2 { padding: 10px; }
.p-3 { padding: 15px; }
.p-4 { padding: 20px; }
.p-5 { padding: 25px; }

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
    
    table {
        font-size: 12px;
    }
    
    table th,
    table td {
        padding: 8px;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 12px;
    }
    
    .card-body {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 5px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* منع التكبير في iOS */
    }
}
