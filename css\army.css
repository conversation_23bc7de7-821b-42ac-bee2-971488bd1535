/* ملف CSS للجيش والثكنة */
/* Army and Barracks CSS */

/* ملخص الجيش */
.army-summary {
    margin-bottom: 30px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.summary-card h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
}

.stat-value.attack {
    color: #dc3545;
}

.stat-value.defense {
    color: #28a745;
}

.stat-value .ready {
    color: #28a745;
    font-size: 1rem;
}

.stat-value .not-ready {
    color: #ffc107;
    font-size: 1rem;
}

/* الوحدات العسكرية */
.army-units {
    margin-bottom: 30px;
}

.units-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.unit-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.unit-card:hover {
    border-color: #007bff;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.unit-card.training {
    border-color: #ffc107;
    background-color: #fff3cd;
}

.unit-image {
    position: relative;
    text-align: center;
    margin-bottom: 15px;
}

.unit-image img {
    width: 64px;
    height: 64px;
    border-radius: 8px;
    border: 2px solid #dee2e6;
}

.unit-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
}

.unit-info h4 {
    text-align: center;
    color: #495057;
    margin-bottom: 15px;
}

.unit-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.unit-stats .stat {
    display: flex;
    justify-content: space-between;
    padding: 5px 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.unit-stats .label {
    color: #6c757d;
    font-size: 0.9rem;
}

.unit-stats .value {
    font-weight: bold;
    color: #495057;
}

.unit-total-power {
    text-align: center;
    padding: 10px;
    background-color: #e7f3ff;
    border-radius: 5px;
    color: #0056b3;
    font-weight: bold;
}

/* عدم وجود وحدات */
.no-units {
    text-align: center;
    padding: 40px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
}

.no-units img {
    width: 64px;
    height: 64px;
    opacity: 0.5;
    margin-bottom: 15px;
}

.no-units h4 {
    color: #6c757d;
    margin-bottom: 10px;
}

.no-units p {
    color: #6c757d;
    margin-bottom: 20px;
}

.training-links {
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* الهجمات */
.incoming-attacks,
.outgoing-attacks {
    margin-bottom: 30px;
}

.attacks-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.attack-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    background-color: white;
}

.attack-item.incoming {
    border-color: #dc3545;
    background-color: #f8d7da;
}

.attack-item.outgoing {
    border-color: #007bff;
    background-color: #d1ecf1;
}

.attack-info {
    flex: 1;
    display: flex;
    gap: 20px;
    align-items: center;
}

.attacker,
.target {
    min-width: 150px;
}

.attacker strong,
.target strong {
    display: block;
    color: #495057;
}

.attacker span,
.target span {
    color: #6c757d;
    font-size: 0.9rem;
}

.attack-type {
    padding: 5px 15px;
    border-radius: 20px;
    background-color: #007bff;
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
}

.arrival-time {
    text-align: center;
}

.arrival-time .label {
    display: block;
    color: #6c757d;
    font-size: 0.8rem;
    margin-bottom: 5px;
}

.timer {
    font-weight: bold;
    color: #495057;
}

.timer.completed {
    color: #28a745;
}

/* التجارات */
.trades-section {
    margin-bottom: 30px;
}

.trades-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.trade-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    background-color: white;
}

.trade-item.incoming {
    border-color: #28a745;
    background-color: #d4edda;
}

.trade-item.outgoing {
    border-color: #ffc107;
    background-color: #fff3cd;
}

.trade-info {
    flex: 1;
    display: flex;
    gap: 20px;
    align-items: center;
}

.trader {
    min-width: 150px;
}

.trader strong {
    display: block;
    color: #495057;
}

.trader span {
    color: #6c757d;
    font-size: 0.9rem;
}

.trade-resources {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.trade-resources .resource {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    background-color: white;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    font-size: 0.9rem;
}

.trade-resources .resource img {
    width: 16px;
    height: 16px;
}

/* شريط الموارد */
.resources-summary {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.resources-summary .resource {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.resources-summary .resource img {
    width: 24px;
    height: 24px;
}

.resources-summary .resource span {
    font-weight: bold;
    color: #495057;
}

/* وحدات التدريب */
.training-units {
    margin-bottom: 30px;
}

.training-cost {
    margin: 15px 0;
}

.training-cost h5 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.cost-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.cost-item {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    border: 1px solid #dee2e6;
}

.cost-item.affordable {
    background-color: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.cost-item.expensive {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.cost-item img {
    width: 16px;
    height: 16px;
}

.training-time {
    text-align: center;
    color: #6c757d;
    font-size: 0.9rem;
    margin: 10px 0;
}

.training-progress {
    text-align: center;
    padding: 15px;
    background-color: #fff3cd;
    border-radius: 6px;
    border: 1px solid #ffc107;
}

.training-progress .status {
    display: block;
    color: #856404;
    font-weight: bold;
    margin-bottom: 10px;
}

.training-form {
    margin-top: 15px;
}

.quantity-input {
    margin-bottom: 15px;
}

.quantity-input label {
    display: block;
    color: #495057;
    margin-bottom: 5px;
    font-weight: 500;
}

.quantity-input input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
}

.total-cost {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #e7f3ff;
    border-radius: 4px;
    font-size: 0.9rem;
    color: #0056b3;
}

/* جدول الوحدات الحالية */
.current-units {
    margin-bottom: 30px;
}

.units-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.units-table th,
.units-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.units-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
}

.units-table td {
    color: #495057;
}

.units-table td img {
    width: 24px;
    height: 24px;
    margin-left: 8px;
    vertical-align: middle;
}

.units-table .status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.units-table .status.ready {
    background-color: #d4edda;
    color: #155724;
}

.units-table .status.training {
    background-color: #fff3cd;
    color: #856404;
}

/* الإجراءات السريعة */
.quick-actions {
    margin-bottom: 30px;
}

.quick-actions h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
    background-color: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
}

.action-btn:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    color: #007bff;
}

.action-btn img {
    width: 32px;
    height: 32px;
}

.action-btn span {
    font-weight: 500;
    text-align: center;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .summary-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .units-grid {
        grid-template-columns: 1fr;
    }
    
    .attack-info,
    .trade-info {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
    
    .resources-summary {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .cost-grid {
        grid-template-columns: 1fr;
    }
    
    .units-table {
        font-size: 0.9rem;
    }
    
    .units-table th,
    .units-table td {
        padding: 8px 10px;
    }
}

@media (max-width: 480px) {
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .attack-item,
    .trade-item {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .resources-summary {
        flex-direction: column;
    }
    
    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .action-btn {
        padding: 15px;
    }
    
    .action-btn img {
        width: 24px;
        height: 24px;
    }
}
