<?php
/**
 * صفحة إدارة المباني
 * Buildings Management Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];
$villageId = $_SESSION['current_village_id'];

// التحقق من القرية
$village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$villageId, $userId]);
if (!$village) {
    redirect('index.php');
}

// تحديث الموارد
updateVillageResources($villageId);

// الحصول على الموارد
$resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);

// الحصول على المباني
$buildings = $db->select("SELECT * FROM buildings WHERE village_id = ?", [$villageId]);
$buildingsArray = [];
foreach ($buildings as $building) {
    $buildingsArray[$building['building_type']] = $building;
}

// معالجة طلب الترقية
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['upgrade_building'])) {
    $buildingType = $_POST['building_type'] ?? '';
    $csrf = $_POST['csrf_token'] ?? '';
    
    if (!validateCSRF($csrf)) {
        $error = 'رمز الأمان غير صحيح';
    } elseif (!array_key_exists($buildingType, BUILDING_TYPES)) {
        $error = 'نوع المبنى غير صحيح';
    } else {
        $currentBuilding = $buildingsArray[$buildingType] ?? null;
        $currentLevel = $currentBuilding ? $currentBuilding['level'] : 0;
        
        // التحقق من إمكانية الترقية
        if ($currentBuilding && $currentBuilding['is_upgrading']) {
            $error = 'المبنى قيد الترقية بالفعل';
        } else {
            $upgradeCost = getBuildingUpgradeCost($buildingType, $currentLevel);
            
            if (!$upgradeCost) {
                $error = 'لا يمكن ترقية هذا المبنى';
            } elseif ($resources['wood'] < $upgradeCost['wood'] || 
                     $resources['clay'] < $upgradeCost['clay'] || 
                     $resources['iron'] < $upgradeCost['iron'] || 
                     $resources['crop'] < $upgradeCost['crop']) {
                $error = 'الموارد غير كافية للترقية';
            } else {
                // حساب وقت البناء
                $mainBuildingLevel = $buildingsArray['main_building']['level'] ?? 1;
                $buildTime = getBuildingTime($buildingType, $currentLevel, $mainBuildingLevel);
                $finishTime = date('Y-m-d H:i:s', time() + $buildTime);
                
                $db->beginTransaction();
                try {
                    // خصم الموارد
                    $db->update("UPDATE resources SET wood = wood - ?, clay = clay - ?, iron = iron - ?, crop = crop - ? WHERE village_id = ?", 
                               [$upgradeCost['wood'], $upgradeCost['clay'], $upgradeCost['iron'], $upgradeCost['crop'], $villageId]);
                    
                    if ($currentBuilding) {
                        // تحديث المبنى الموجود
                        $db->update("UPDATE buildings SET is_upgrading = 1, upgrade_finish_time = ? WHERE id = ?", 
                                   [$finishTime, $currentBuilding['id']]);
                    } else {
                        // إنشاء مبنى جديد
                        $db->insert("INSERT INTO buildings (village_id, building_type, level, is_upgrading, upgrade_finish_time) VALUES (?, ?, ?, ?, ?)", 
                                   [$villageId, $buildingType, 0, 1, $finishTime]);
                    }
                    
                    $db->commit();
                    $success = 'تم بدء ترقية ' . BUILDING_TYPES[$buildingType] . ' بنجاح!';
                    
                    // تحديث البيانات
                    $resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);
                    $buildings = $db->select("SELECT * FROM buildings WHERE village_id = ?", [$villageId]);
                    $buildingsArray = [];
                    foreach ($buildings as $building) {
                        $buildingsArray[$building['building_type']] = $building;
                    }
                } catch (Exception $e) {
                    $db->rollback();
                    $error = 'حدث خطأ أثناء الترقية';
                }
            }
        }
    }
}

// التحقق من اكتمال الترقيات
foreach ($buildings as $building) {
    if ($building['is_upgrading'] && strtotime($building['upgrade_finish_time']) <= time()) {
        $db->update("UPDATE buildings SET level = level + 1, is_upgrading = 0, upgrade_finish_time = NULL WHERE id = ?", 
                   [$building['id']]);
        
        // تحديث البيانات
        $buildings = $db->select("SELECT * FROM buildings WHERE village_id = ?", [$villageId]);
        $buildingsArray = [];
        foreach ($buildings as $building) {
            $buildingsArray[$building['building_type']] = $building;
        }
    }
}

$csrfToken = generateCSRF();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المباني - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/buildings.css">
</head>
<body class="game-page">
    <?php include 'includes/header.php'; ?>
    
    <div class="main-content">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="content">
            <div class="page-header">
                <h1>المباني</h1>
                <p>إدارة وترقية مباني القرية</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <!-- شريط الموارد -->
            <div class="resources-summary">
                <div class="resource">
                    <img src="images/resources/wood.png" alt="خشب">
                    <span><?php echo formatNumber($resources['wood']); ?></span>
                </div>
                <div class="resource">
                    <img src="images/resources/clay.png" alt="طين">
                    <span><?php echo formatNumber($resources['clay']); ?></span>
                </div>
                <div class="resource">
                    <img src="images/resources/iron.png" alt="حديد">
                    <span><?php echo formatNumber($resources['iron']); ?></span>
                </div>
                <div class="resource">
                    <img src="images/resources/crop.png" alt="قمح">
                    <span><?php echo formatNumber($resources['crop']); ?></span>
                </div>
            </div>
            
            <!-- قائمة المباني -->
            <div class="buildings-grid">
                <?php foreach (BUILDING_TYPES as $buildingType => $buildingName): ?>
                    <?php
                    $building = $buildingsArray[$buildingType] ?? null;
                    $currentLevel = $building ? $building['level'] : 0;
                    $isUpgrading = $building && $building['is_upgrading'];
                    $upgradeCost = getBuildingUpgradeCost($buildingType, $currentLevel);
                    $canAfford = $upgradeCost && 
                                $resources['wood'] >= $upgradeCost['wood'] && 
                                $resources['clay'] >= $upgradeCost['clay'] && 
                                $resources['iron'] >= $upgradeCost['iron'] && 
                                $resources['crop'] >= $upgradeCost['crop'];
                    ?>
                    
                    <div class="building-card <?php echo $isUpgrading ? 'upgrading' : ''; ?>">
                        <div class="building-image">
                            <img src="images/buildings/<?php echo $buildingType; ?>.png" alt="<?php echo $buildingName; ?>">
                            <?php if ($currentLevel > 0): ?>
                                <span class="level-badge"><?php echo $currentLevel; ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="building-info">
                            <h3><?php echo $buildingName; ?></h3>
                            
                            <?php if ($currentLevel == 0): ?>
                                <p class="status">غير مبني</p>
                            <?php elseif ($isUpgrading): ?>
                                <p class="status upgrading">قيد الترقية إلى المستوى <?php echo $currentLevel + 1; ?></p>
                                <div class="upgrade-timer" data-finish-time="<?php echo strtotime($building['upgrade_finish_time']); ?>">
                                    <span class="time-remaining"></span>
                                </div>
                            <?php else: ?>
                                <p class="status">المستوى <?php echo $currentLevel; ?></p>
                            <?php endif; ?>
                            
                            <!-- وصف المبنى -->
                            <div class="building-description">
                                <?php echo getBuildingDescription($buildingType, $currentLevel); ?>
                            </div>
                            
                            <?php if ($upgradeCost && !$isUpgrading): ?>
                                <div class="upgrade-section">
                                    <h4>تكلفة الترقية إلى المستوى <?php echo $currentLevel + 1; ?>:</h4>
                                    <div class="upgrade-cost">
                                        <span class="cost-item <?php echo ($resources['wood'] >= $upgradeCost['wood']) ? 'affordable' : 'expensive'; ?>">
                                            <img src="images/resources/wood.png" alt="خشب">
                                            <?php echo formatNumber($upgradeCost['wood']); ?>
                                        </span>
                                        <span class="cost-item <?php echo ($resources['clay'] >= $upgradeCost['clay']) ? 'affordable' : 'expensive'; ?>">
                                            <img src="images/resources/clay.png" alt="طين">
                                            <?php echo formatNumber($upgradeCost['clay']); ?>
                                        </span>
                                        <span class="cost-item <?php echo ($resources['iron'] >= $upgradeCost['iron']) ? 'affordable' : 'expensive'; ?>">
                                            <img src="images/resources/iron.png" alt="حديد">
                                            <?php echo formatNumber($upgradeCost['iron']); ?>
                                        </span>
                                        <span class="cost-item <?php echo ($resources['crop'] >= $upgradeCost['crop']) ? 'affordable' : 'expensive'; ?>">
                                            <img src="images/resources/crop.png" alt="قمح">
                                            <?php echo formatNumber($upgradeCost['crop']); ?>
                                        </span>
                                    </div>
                                    
                                    <?php
                                    $mainBuildingLevel = $buildingsArray['main_building']['level'] ?? 1;
                                    $buildTime = getBuildingTime($buildingType, $currentLevel, $mainBuildingLevel);
                                    ?>
                                    <p class="build-time">وقت البناء: <?php echo formatDuration($buildTime); ?></p>
                                    
                                    <form method="POST" class="upgrade-form">
                                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                        <input type="hidden" name="building_type" value="<?php echo $buildingType; ?>">
                                        <button type="submit" name="upgrade_building" class="btn btn-primary" 
                                                <?php echo $canAfford ? '' : 'disabled'; ?>>
                                            <?php echo $currentLevel == 0 ? 'بناء' : 'ترقية'; ?>
                                        </button>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </main>
    </div>
    
    <script>
        // تحديث مؤقتات الترقية
        function updateUpgradeTimers() {
            const timers = document.querySelectorAll('.upgrade-timer');
            
            timers.forEach(timer => {
                const finishTime = parseInt(timer.dataset.finishTime);
                const now = Math.floor(Date.now() / 1000);
                const remaining = finishTime - now;
                
                if (remaining <= 0) {
                    timer.querySelector('.time-remaining').textContent = 'اكتمل!';
                    setTimeout(() => location.reload(), 1000);
                } else {
                    timer.querySelector('.time-remaining').textContent = formatDuration(remaining);
                }
            });
        }
        
        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            if (hours > 0) {
                return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
        }
        
        // تحديث المؤقتات كل ثانية
        setInterval(updateUpgradeTimers, 1000);
        updateUpgradeTimers();
    </script>
</body>
</html>

<?php
function getBuildingDescription($buildingType, $level) {
    $descriptions = [
        'main_building' => 'المبنى الرئيسي يسرع البناء ويوفر مساحة للسكان',
        'warehouse' => 'يخزن الخشب والطين والحديد',
        'granary' => 'يخزن القمح',
        'barracks' => 'يدرب المشاة',
        'stable' => 'يدرب الفرسان',
        'workshop' => 'ينتج آلات الحصار',
        'academy' => 'يبحث في التقنيات الجديدة',
        'smithy' => 'يحسن الأسلحة والدروع',
        'rally_point' => 'نقطة تجمع الجيوش',
        'marketplace' => 'يسمح بالتجارة مع القرى الأخرى'
    ];
    
    return $descriptions[$buildingType] ?? 'مبنى مهم للقرية';
}
?>
