<?php
session_start();
require_once 'config/database.php';
require_once 'classes/Building.php';
require_once 'classes/User.php';

if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$db = new Database();
$building = new Building($db->getConnection());
$user = new User($db->getConnection());

$villageId = $_GET['village_id'] ?? 1;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['upgrade_building'])) {
        $result = $building->upgradeBuilding($_POST['building_id'], $_SESSION['user_id']);
        if ($result) {
            $success = "بدأ ترقية المبنى!";
        } else {
            $error = "فشل في ترقية المبنى";
        }
    } elseif (isset($_POST['build_new'])) {
        $result = $building->constructBuilding($villageId, $_POST['building_type'], $_POST['x_pos'], $_POST['y_pos'], $_SESSION['user_id']);
        if ($result) {
            $success = "بدأ بناء المبنى!";
        } else {
            $error = "فشل في بناء المبنى";
        }
    }
}

$buildings = $building->getVillageBuildings($villageId);
$userData = $user->getUserById($_SESSION['user_id']);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>المباني - المزرعة الذهبية</title>
    <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
    <div class="game-container">
        <?php include 'templates/header.php'; ?>
        
        <div class="buildings-container">
            <h2>إدارة المباني</h2>
            
            <div class="village-map">
                <div class="map-grid">
                    <?php for ($x = 1; $x <= 15; $x++): ?>
                        <?php for ($y = 1; $y <= 15; $y++): ?>
                            <div class="map-cell" data-x="<?php echo $x; ?>" data-y="<?php echo $y; ?>">
                                <?php
                                $cellBuilding = null;
                                foreach ($buildings as $b) {
                                    if ($b['x_position'] == $x && $b['y_position'] == $y) {
                                        $cellBuilding = $b;
                                        break;
                                    }
                                }
                                ?>
                                
                                <?php if ($cellBuilding): ?>
                                    <div class="building" onclick="showBuildingInfo(<?php echo $cellBuilding['id']; ?>)">
                                        <img src="assets/images/buildings/<?php echo $cellBuilding['building_type']; ?>.png" 
                                             alt="<?php echo $cellBuilding['building_type']; ?>">
                                        <span class="building-level">Lv.<?php echo $cellBuilding['level']; ?></span>
                                    </div>
                                <?php else: ?>
                                    <div class="empty-cell" onclick="showBuildMenu(<?php echo $x; ?>, <?php echo $y; ?>)">
                                        <img src="assets/images/empty_plot.png" alt="أرض فارغة">
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endfor; ?>
                    <?php endfor; ?>
                </div>
            </div>
            
            <div class="buildings-list">
                <h3>مبانيك الحالية</h3>
                <?php foreach ($buildings as $b): ?>
                    <div class="building-card">
                        <img src="assets/images/buildings/<?php echo $b['building_type']; ?>.png" alt="<?php echo $b['building_type']; ?>">
                        <div class="building-info">
                            <h4><?php echo $this->getBuildingName($b['building_type']); ?></h4>
                            <p>المستوى: <?php echo $b['level']; ?></p>
                            <p>الموقع: (<?php echo $b['x_position']; ?>, <?php echo $b['y_position']; ?>)</p>
                            
                            <?php if ($b['upgrade_end_time'] && strtotime($b['upgrade_end_time']) > time()): ?>
                                <p class="upgrading">جاري الترقية...</p>
                                <div class="progress-bar">
                                    <div class="progress" style="width: 60%"></div>
                                </div>
                            <?php else: ?>
                                <form method="POST" class="inline-form">
                                    <input type="hidden" name="building_id" value="<?php echo $b['id']; ?>">
                                    <button type="submit" name="upgrade_building" class="btn-upgrade">ترقية</button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- نافذة بناء مبنى جديد -->
        <div id="buildDialog" class="modal">
            <div class="modal-content">
                <h3>اختر نوع المبنى</h3>
                <form method="POST">
                    <input type="hidden" id="build_x" name="x_pos">
                    <input type="hidden" id="build_y" name="y_pos">
                    
                    <div class="building-options">
                        <label>
                            <input type="radio" name="building_type" value="house">
                            <img src="assets/images/buildings/house.png" alt="منزل">
                            <span>منزل (100 ذهب، 50 خشب)</span>
                        </label>
                        <label>
                            <input type="radio" name="building_type" value="farm">
                            <img src="assets/images/buildings/farm.png" alt="مزرعة">
                            <span>مزرعة (150 ذهب، 75 خشب)</span>
                        </label>
                        <label>
                            <input type="radio" name="building_type" value="warehouse">
                            <img src="assets/images/buildings/warehouse.png" alt="مخزن">
                            <span>مخزن (200 ذهب، 100 خشب، 50 حجر)</span>
                        </label>
                        <label>
                            <input type="radio" name="building_type" value="barracks">
                            <img src="assets/images/buildings/barracks.png" alt="ثكنة">
                            <span>ثكنة (300 ذهب، 150 خشب، 100 حجر)</span>
                        </label>
                    </div>
                    
                    <button type="submit" name="build_new">بناء</button>
                    <button type="button" onclick="closeBuildDialog()">إلغاء</button>
                </form>
            </div>
        </div>
    </div>
    
    <script src="assets/js/buildings.js"></script>
</body>
</html>