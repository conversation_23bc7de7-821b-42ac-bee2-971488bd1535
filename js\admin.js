/**
 * ملف JavaScript للوحة الإدارة
 * Admin Panel JavaScript
 */

// متغيرات عامة
let refreshInterval;
let notificationTimeout;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeAdminPanel();
    setupEventListeners();
    startAutoRefresh();
});

// تهيئة لوحة الإدارة
function initializeAdminPanel() {
    // تحديث الوقت
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
    
    // تحديث الإحصائيات
    updateDashboardStats();
    
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة المخططات
    initializeCharts();
    
    // تحديث حالة الخادم
    updateServerStatus();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // أزرار الإجراءات
    document.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('click', handleActionClick);
    });
    
    // نماذج البحث
    document.querySelectorAll('.search-form').forEach(form => {
        form.addEventListener('submit', handleSearchSubmit);
    });
    
    // أزرار التصفية
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', handleFilterClick);
    });
    
    // نماذج التحديث
    document.querySelectorAll('.update-form').forEach(form => {
        form.addEventListener('submit', handleUpdateSubmit);
    });
    
    // أزرار الحذف
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', handleDeleteClick);
    });
    
    // أزرار الحظر/إلغاء الحظر
    document.querySelectorAll('.ban-btn, .unban-btn').forEach(btn => {
        btn.addEventListener('click', handleBanClick);
    });
}

// تحديث الوقت الحالي
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// تحديث إحصائيات لوحة التحكم
function updateDashboardStats() {
    fetch('ajax/dashboard-stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatCards(data.stats);
                updateRecentActivity(data.activity);
                updateServerInfo(data.server);
            }
        })
        .catch(error => {
            console.error('Error updating dashboard stats:', error);
        });
}

// تحديث بطاقات الإحصائيات
function updateStatCards(stats) {
    Object.keys(stats).forEach(key => {
        const element = document.getElementById(`stat-${key}`);
        if (element) {
            element.textContent = formatNumber(stats[key]);
            
            // إضافة تأثير التحديث
            element.classList.add('updated');
            setTimeout(() => {
                element.classList.remove('updated');
            }, 1000);
        }
    });
}

// تحديث الأنشطة الحديثة
function updateRecentActivity(activities) {
    const container = document.getElementById('recentActivity');
    if (!container || !activities) return;
    
    container.innerHTML = '';
    
    activities.forEach(activity => {
        const activityElement = createActivityElement(activity);
        container.appendChild(activityElement);
    });
}

// إنشاء عنصر نشاط
function createActivityElement(activity) {
    const div = document.createElement('div');
    div.className = 'activity-item';
    
    div.innerHTML = `
        <div class="activity-icon">${getActivityIcon(activity.type)}</div>
        <div class="activity-details">
            <div class="activity-description">${activity.description}</div>
            <div class="activity-time">${formatTimeAgo(activity.time)}</div>
        </div>
    `;
    
    return div;
}

// الحصول على أيقونة النشاط
function getActivityIcon(type) {
    const icons = {
        'user_registration': '👤',
        'user_login': '🔑',
        'attack': '⚔️',
        'trade': '🚚',
        'building': '🏗️',
        'alliance': '🤝',
        'report': '📊',
        'admin_action': '⚙️'
    };
    
    return icons[type] || '📝';
}

// تحديث معلومات الخادم
function updateServerInfo(serverInfo) {
    if (!serverInfo) return;
    
    const elements = {
        'serverLoad': serverInfo.load,
        'memoryUsage': formatBytes(serverInfo.memory),
        'diskSpace': formatBytes(serverInfo.disk),
        'uptime': formatUptime(serverInfo.uptime)
    };
    
    Object.keys(elements).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
            element.textContent = elements[key];
        }
    });
}

// تهيئة الجداول
function initializeTables() {
    // إضافة وظائف الترتيب
    document.querySelectorAll('.sortable-table th').forEach(header => {
        header.addEventListener('click', function() {
            sortTable(this);
        });
    });
    
    // إضافة وظائف التحديد المتعدد
    document.querySelectorAll('.select-all').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            toggleSelectAll(this);
        });
    });
    
    // إضافة وظائف التصفية السريعة
    document.querySelectorAll('.quick-filter').forEach(filter => {
        filter.addEventListener('change', function() {
            applyQuickFilter(this);
        });
    });
}

// ترتيب الجدول
function sortTable(header) {
    const table = header.closest('table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const isAscending = !header.classList.contains('sort-asc');
    
    // إزالة فئات الترتيب من جميع الرؤوس
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    
    // إضافة فئة الترتيب للرأس الحالي
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    
    // ترتيب الصفوف
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();
        
        // محاولة تحويل إلى رقم
        const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
        const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        } else {
            return isAscending ? 
                aValue.localeCompare(bValue, 'ar') : 
                bValue.localeCompare(aValue, 'ar');
        }
    });
    
    // إعادة ترتيب الصفوف في الجدول
    rows.forEach(row => tbody.appendChild(row));
}

// تحديد/إلغاء تحديد الكل
function toggleSelectAll(checkbox) {
    const table = checkbox.closest('table');
    const checkboxes = table.querySelectorAll('tbody input[type="checkbox"]');
    
    checkboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    
    updateBulkActions();
}

// تحديث إجراءات التحديد المتعدد
function updateBulkActions() {
    const selectedCount = document.querySelectorAll('tbody input[type="checkbox"]:checked').length;
    const bulkActions = document.querySelector('.bulk-actions');
    
    if (bulkActions) {
        if (selectedCount > 0) {
            bulkActions.style.display = 'block';
            bulkActions.querySelector('.selected-count').textContent = selectedCount;
        } else {
            bulkActions.style.display = 'none';
        }
    }
}

// تطبيق التصفية السريعة
function applyQuickFilter(filter) {
    const table = filter.closest('.table-container').querySelector('table');
    const rows = table.querySelectorAll('tbody tr');
    const filterValue = filter.value.toLowerCase();
    const filterColumn = filter.dataset.column;
    
    rows.forEach(row => {
        if (filterValue === '' || filterValue === 'all') {
            row.style.display = '';
        } else {
            const cellValue = row.children[filterColumn].textContent.toLowerCase();
            row.style.display = cellValue.includes(filterValue) ? '' : 'none';
        }
    });
}

// معالجة نقرات الإجراءات
function handleActionClick(event) {
    const button = event.target.closest('.action-btn');
    const action = button.dataset.action;
    const id = button.dataset.id;
    
    switch (action) {
        case 'refresh':
            refreshPage();
            break;
        case 'export':
            exportData(button.dataset.type);
            break;
        case 'backup':
            createBackup();
            break;
        case 'clear-cache':
            clearCache();
            break;
        default:
            console.log('Unknown action:', action);
    }
}

// معالجة إرسال البحث
function handleSearchSubmit(event) {
    event.preventDefault();
    const form = event.target;
    const formData = new FormData(form);
    const searchParams = new URLSearchParams(formData);
    
    // تحديث URL مع معاملات البحث
    const newUrl = `${window.location.pathname}?${searchParams.toString()}`;
    window.history.pushState({}, '', newUrl);
    
    // إعادة تحميل الصفحة مع المعاملات الجديدة
    window.location.reload();
}

// معالجة نقرات التصفية
function handleFilterClick(event) {
    const button = event.target.closest('.filter-btn');
    const filter = button.dataset.filter;
    
    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // إضافة الفئة النشطة للزر المحدد
    button.classList.add('active');
    
    // تطبيق التصفية
    applyFilter(filter);
}

// معالجة إرسال التحديث
function handleUpdateSubmit(event) {
    event.preventDefault();
    const form = event.target;
    const formData = new FormData(form);
    
    // إظهار مؤشر التحميل
    showLoadingIndicator(form);
    
    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingIndicator(form);
        
        if (data.success) {
            showNotification('تم التحديث بنجاح', 'success');
            if (data.redirect) {
                window.location.href = data.redirect;
            }
        } else {
            showNotification(data.message || 'حدث خطأ أثناء التحديث', 'error');
        }
    })
    .catch(error => {
        hideLoadingIndicator(form);
        showNotification('حدث خطأ أثناء التحديث', 'error');
        console.error('Error:', error);
    });
}

// معالجة نقرات الحذف
function handleDeleteClick(event) {
    const button = event.target.closest('.delete-btn');
    const id = button.dataset.id;
    const type = button.dataset.type;
    
    if (confirm(`هل أنت متأكد من حذف هذا ${type}؟`)) {
        deleteItem(id, type, button);
    }
}

// معالجة نقرات الحظر
function handleBanClick(event) {
    const button = event.target.closest('.ban-btn, .unban-btn');
    const id = button.dataset.id;
    const action = button.classList.contains('ban-btn') ? 'ban' : 'unban';
    
    const message = action === 'ban' ? 
        'هل أنت متأكد من حظر هذا المستخدم؟' : 
        'هل أنت متأكد من إلغاء حظر هذا المستخدم؟';
    
    if (confirm(message)) {
        toggleUserBan(id, action, button);
    }
}

// حذف عنصر
function deleteItem(id, type, button) {
    showLoadingIndicator(button);
    
    fetch('ajax/delete-item.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, type })
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingIndicator(button);
        
        if (data.success) {
            showNotification('تم الحذف بنجاح', 'success');
            button.closest('tr').remove();
        } else {
            showNotification(data.message || 'حدث خطأ أثناء الحذف', 'error');
        }
    })
    .catch(error => {
        hideLoadingIndicator(button);
        showNotification('حدث خطأ أثناء الحذف', 'error');
        console.error('Error:', error);
    });
}

// تبديل حظر المستخدم
function toggleUserBan(id, action, button) {
    showLoadingIndicator(button);
    
    fetch('ajax/toggle-ban.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, action })
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingIndicator(button);
        
        if (data.success) {
            const message = action === 'ban' ? 'تم حظر المستخدم' : 'تم إلغاء حظر المستخدم';
            showNotification(message, 'success');
            
            // تحديث واجهة المستخدم
            updateBanButton(button, action);
        } else {
            showNotification(data.message || 'حدث خطأ', 'error');
        }
    })
    .catch(error => {
        hideLoadingIndicator(button);
        showNotification('حدث خطأ', 'error');
        console.error('Error:', error);
    });
}

// تحديث زر الحظر
function updateBanButton(button, action) {
    if (action === 'ban') {
        button.classList.remove('ban-btn');
        button.classList.add('unban-btn', 'btn-success');
        button.textContent = 'إلغاء الحظر';
    } else {
        button.classList.remove('unban-btn', 'btn-success');
        button.classList.add('ban-btn', 'btn-danger');
        button.textContent = 'حظر';
    }
}

// إظهار مؤشر التحميل
function showLoadingIndicator(element) {
    element.classList.add('loading');
    element.disabled = true;
    
    const originalText = element.textContent;
    element.dataset.originalText = originalText;
    element.textContent = 'جاري التحميل...';
}

// إخفاء مؤشر التحميل
function hideLoadingIndicator(element) {
    element.classList.remove('loading');
    element.disabled = false;
    
    if (element.dataset.originalText) {
        element.textContent = element.dataset.originalText;
        delete element.dataset.originalText;
    }
}

// إظهار الإشعار
function showNotification(message, type = 'info') {
    // إزالة الإشعارات السابقة
    const existingNotifications = document.querySelectorAll('.admin-notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // إنشاء إشعار جديد
    const notification = document.createElement('div');
    notification.className = `admin-notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">${getNotificationIcon(type)}</span>
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;
    
    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);
    
    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// الحصول على أيقونة الإشعار
function getNotificationIcon(type) {
    const icons = {
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️'
    };
    
    return icons[type] || 'ℹ️';
}

// بدء التحديث التلقائي
function startAutoRefresh() {
    // تحديث الإحصائيات كل 30 ثانية
    refreshInterval = setInterval(() => {
        updateDashboardStats();
    }, 30000);
}

// إيقاف التحديث التلقائي
function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
}

// تحديث الصفحة
function refreshPage() {
    window.location.reload();
}

// تصدير البيانات
function exportData(type) {
    const url = `ajax/export-data.php?type=${type}`;
    window.open(url, '_blank');
}

// إنشاء نسخة احتياطية
function createBackup() {
    if (confirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟')) {
        fetch('ajax/create-backup.php', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            } else {
                showNotification('حدث خطأ أثناء إنشاء النسخة الاحتياطية', 'error');
            }
        })
        .catch(error => {
            showNotification('حدث خطأ أثناء إنشاء النسخة الاحتياطية', 'error');
            console.error('Error:', error);
        });
    }
}

// مسح التخزين المؤقت
function clearCache() {
    if (confirm('هل تريد مسح التخزين المؤقت؟')) {
        fetch('ajax/clear-cache.php', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم مسح التخزين المؤقت بنجاح', 'success');
            } else {
                showNotification('حدث خطأ أثناء مسح التخزين المؤقت', 'error');
            }
        })
        .catch(error => {
            showNotification('حدث خطأ أثناء مسح التخزين المؤقت', 'error');
            console.error('Error:', error);
        });
    }
}

// وظائف مساعدة
function formatNumber(num) {
    return new Intl.NumberFormat('ar-SA').format(num);
}

function formatBytes(bytes) {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
}

function formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    return `${days}د ${hours}س ${minutes}ق`;
}

function formatTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = Math.floor((now - time) / 1000);
    
    if (diff < 60) {
        return 'منذ لحظات';
    } else if (diff < 3600) {
        const minutes = Math.floor(diff / 60);
        return `منذ ${minutes} دقيقة`;
    } else if (diff < 86400) {
        const hours = Math.floor(diff / 3600);
        return `منذ ${hours} ساعة`;
    } else {
        const days = Math.floor(diff / 86400);
        return `منذ ${days} يوم`;
    }
}

// تنظيف الموارد عند مغادرة الصفحة
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
});
