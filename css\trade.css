/* ملف CSS للتجارة المباشرة */
/* Direct Trade CSS */

/* اختيار الهدف */
.target-selection {
    background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    border: 2px solid #007bff;
    margin-bottom: 30px;
}

.target-selection h3 {
    color: #0056b3;
    margin-bottom: 15px;
    font-size: 1.4rem;
}

.target-selection p {
    color: #495057;
    margin-bottom: 25px;
    font-size: 1.1rem;
}

.selection-options {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.selection-options .btn {
    padding: 12px 25px;
    font-size: 1rem;
    font-weight: 600;
}

/* معلومات التجارة */
.trade-info {
    margin-bottom: 30px;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 30px;
    align-items: center;
}

.info-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.info-card.source {
    border-color: #28a745;
}

.info-card.target {
    border-color: #dc3545;
}

.info-card h3 {
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.info-card.source h3 {
    color: #28a745;
}

.info-card.target h3 {
    color: #dc3545;
}

.village-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.village-name {
    font-weight: bold;
    font-size: 1.1rem;
    color: #495057;
}

.player-name {
    color: #007bff;
    font-weight: 500;
}

.coordinates {
    color: #6c757d;
    font-size: 0.9rem;
}

.population {
    color: #6c757d;
    font-size: 0.9rem;
}

.resources {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-top: 10px;
}

.resource {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.resource img {
    width: 20px;
    height: 20px;
}

.resource span {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

/* سهم التجارة */
.trade-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    text-align: center;
}

.arrow-icon {
    font-size: 2rem;
    color: #007bff;
    animation: pulse 2s infinite;
}

.trade-details {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.distance,
.travel-time {
    color: #495057;
    font-size: 0.9rem;
    font-weight: 500;
}

/* نموذج التجارة */
.trade-form-container {
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    border: 2px solid #007bff;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.trade-form-container h3 {
    color: #007bff;
    margin-bottom: 20px;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.trade-form-container h3::before {
    content: "📦";
    font-size: 1.5rem;
}

.resources-section {
    margin-bottom: 25px;
}

.resources-section h4 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.resource-input {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.resource-input:focus-within {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.resource-input img {
    width: 32px;
    height: 32px;
    margin-bottom: 8px;
}

.resource-input label {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.resource-input input {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #ced4da;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
}

.resource-input input:focus {
    outline: none;
    border-color: #007bff;
}

.max-available {
    display: block;
    color: #6c757d;
    font-size: 0.8rem;
    margin-top: 5px;
    text-align: center;
}

/* حاسبة التجارة */
.trade-calculator {
    background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    border: 2px solid #007bff;
}

.trade-calculator h4 {
    color: #0056b3;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.calculation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.calc-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #007bff;
}

.calc-item .label {
    color: #495057;
    font-weight: 500;
}

.calc-item .value {
    color: #0056b3;
    font-weight: bold;
}

/* أزرار التحكم */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.form-actions .btn {
    padding: 12px 25px;
    font-size: 1rem;
    font-weight: 600;
}

.form-actions .btn:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

/* نصائح التجارة */
.trade-tips {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #ffc107;
    margin-bottom: 30px;
}

.trade-tips h4 {
    color: #856404;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.trade-tips ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.trade-tips li {
    color: #856404;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.trade-tips li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .trade-arrow {
        order: 2;
    }
    
    .info-card.target {
        order: 3;
    }
    
    .arrow-icon {
        transform: rotate(90deg);
        font-size: 1.5rem;
    }
    
    .resources-grid {
        grid-template-columns: 1fr;
    }
    
    .calculation-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .resources {
        grid-template-columns: 1fr;
    }
    
    .selection-options {
        flex-direction: column;
        align-items: center;
    }
    
    .selection-options .btn {
        width: 200px;
    }
}

@media (max-width: 480px) {
    .trade-form-container {
        padding: 20px;
    }
    
    .resource-input {
        padding: 12px;
    }
    
    .resource-input img {
        width: 28px;
        height: 28px;
    }
    
    .trade-calculator {
        padding: 15px;
    }
    
    .calc-item {
        padding: 8px 12px;
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
    
    .trade-tips {
        padding: 15px;
    }
    
    .target-selection {
        padding: 20px;
    }
    
    .info-card {
        padding: 15px;
    }
}

/* تأثيرات الحركة */
@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.05); }
}

.resource-input:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.calc-item:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* تحسينات للطباعة */
@media print {
    .form-actions,
    .trade-tips {
        display: none;
    }
    
    .trade-form-container {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .info-card {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .trade-calculator {
        background: #f0f0f0 !important;
        border: 1px solid #000;
    }
}

/* تحسينات للوضع المظلم (إذا تم تطبيقه لاحقاً) */
@media (prefers-color-scheme: dark) {
    .trade-form-container,
    .info-card {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .resource-input {
        background-color: #3d3d3d;
        border-color: #555;
    }
    
    .resource-input input {
        background-color: #2d2d2d;
        border-color: #555;
        color: #fff;
    }
    
    .resource {
        background-color: #3d3d3d;
        border-color: #555;
        color: #fff;
    }
    
    .calc-item {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .trade-details {
        background-color: #3d3d3d;
        border-color: #555;
        color: #fff;
    }
    
    .target-selection {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .trade-calculator {
        background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
        border-color: #444;
    }
    
    .trade-tips {
        background: linear-gradient(135deg, #3d3d00 0%, #4d4d00 100%);
        border-color: #ffc107;
    }
}
