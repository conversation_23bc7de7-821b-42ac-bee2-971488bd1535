<?php
/**
 * ملف الاتصال بقاعدة البيانات
 * Database Connection Configuration
 */

class Database {
    private $pdo;
    private static $instance = null;

    /**
     * إنشاء اتصال واحد بقاعدة البيانات (Singleton Pattern)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * إنشاء الاتصال
     */
    private function __construct() {
        // استخدام SQLite إذا كان محدد، وإلا MySQL
        if (defined('DB_TYPE') && DB_TYPE === 'sqlite') {
            $dsn = "sqlite:" . DB_PATH;
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
            ];

            try {
                // إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
                $dbDir = dirname(DB_PATH);
                if (!is_dir($dbDir)) {
                    mkdir($dbDir, 0755, true);
                }

                $this->pdo = new PDO($dsn, null, null, $options);

                // تفعيل المفاتيح الخارجية في SQLite
                $this->pdo->exec("PRAGMA foreign_keys = ON");

            } catch (PDOException $e) {
                error_log("Database connection failed: " . $e->getMessage());
                die("خطأ في الاتصال بقاعدة البيانات SQLite: " . $e->getMessage());
            }
        } else {
            // MySQL التقليدي
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;

            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];

            try {
                $this->pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
            } catch (PDOException $e) {
                error_log("Database connection failed: " . $e->getMessage());
                die("خطأ في الاتصال بقاعدة البيانات MySQL: " . $e->getMessage());
            }
        }
    }

    /**
     * الحصول على كائن PDO
     */
    public function getConnection() {
        return $this->pdo;
    }

    /**
     * تنفيذ استعلام SELECT
     */
    public function select($query, $params = []) {
        try {
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Select query failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تنفيذ استعلام SELECT لصف واحد
     */
    public function selectOne($query, $params = []) {
        try {
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("SelectOne query failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تنفيذ استعلام INSERT
     */
    public function insert($query, $params = []) {
        try {
            $stmt = $this->pdo->prepare($query);
            $result = $stmt->execute($params);
            return $result ? $this->pdo->lastInsertId() : false;
        } catch (PDOException $e) {
            error_log("Insert query failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تنفيذ استعلام UPDATE
     */
    public function update($query, $params = []) {
        try {
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Update query failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تنفيذ استعلام DELETE
     */
    public function delete($query, $params = []) {
        try {
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Delete query failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }

    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->pdo->commit();
    }

    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->pdo->rollback();
    }

    /**
     * منع النسخ
     */
    private function __clone() {}

    /**
     * منع إلغاء التسلسل
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * دالة مساعدة للحصول على قاعدة البيانات
 */
function getDB() {
    return Database::getInstance();
}

/**
 * دالة مساعدة للحصول على الاتصال
 */
function getConnection() {
    return Database::getInstance()->getConnection();
}
?>
