<?php
/**
 * صفحة إدارة الحقول
 * Fields Management Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];
$villageId = $_SESSION['current_village_id'];

// التحقق من القرية
$village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$villageId, $userId]);
if (!$village) {
    redirect('index.php');
}

// تحديث الموارد
updateVillageResources($villageId);

// الحصول على الموارد
$resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);

// الحصول على الحقول
$fields = $db->select("SELECT * FROM fields WHERE village_id = ? ORDER BY position", [$villageId]);

// معالجة طلب ترقية الحقل
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['upgrade_field'])) {
    $fieldId = intval($_POST['field_id'] ?? 0);
    $csrf = $_POST['csrf_token'] ?? '';
    
    if (!validateCSRF($csrf)) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        $field = $db->selectOne("SELECT * FROM fields WHERE id = ? AND village_id = ?", [$fieldId, $villageId]);
        
        if (!$field) {
            $error = 'الحقل غير موجود';
        } elseif ($field['is_upgrading']) {
            $error = 'الحقل قيد الترقية بالفعل';
        } else {
            $upgradeCost = getFieldUpgradeCost($field['field_type'], $field['level']);
            
            if (!$upgradeCost) {
                $error = 'لا يمكن ترقية هذا الحقل أكثر';
            } elseif ($resources['wood'] < $upgradeCost['wood'] || 
                     $resources['clay'] < $upgradeCost['clay'] || 
                     $resources['iron'] < $upgradeCost['iron'] || 
                     $resources['crop'] < $upgradeCost['crop']) {
                $error = 'الموارد غير كافية للترقية';
            } else {
                // حساب وقت الترقية
                $upgradeTime = getFieldUpgradeTime($field['field_type'], $field['level']);
                $finishTime = date('Y-m-d H:i:s', time() + $upgradeTime);
                
                $db->beginTransaction();
                try {
                    // خصم الموارد
                    $db->update("UPDATE resources SET wood = wood - ?, clay = clay - ?, iron = iron - ?, crop = crop - ? WHERE village_id = ?", 
                               [$upgradeCost['wood'], $upgradeCost['clay'], $upgradeCost['iron'], $upgradeCost['crop'], $villageId]);
                    
                    // تحديث الحقل
                    $db->update("UPDATE fields SET is_upgrading = 1, upgrade_finish_time = ? WHERE id = ?", 
                               [$finishTime, $fieldId]);
                    
                    $db->commit();
                    $success = 'تم بدء ترقية الحقل بنجاح!';
                    
                    // تحديث البيانات
                    $resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);
                    $fields = $db->select("SELECT * FROM fields WHERE village_id = ? ORDER BY position", [$villageId]);
                } catch (Exception $e) {
                    $db->rollback();
                    $error = 'حدث خطأ أثناء الترقية';
                }
            }
        }
    }
}

// التحقق من اكتمال ترقيات الحقول
foreach ($fields as $field) {
    if ($field['is_upgrading'] && strtotime($field['upgrade_finish_time']) <= time()) {
        $db->update("UPDATE fields SET level = level + 1, is_upgrading = 0, upgrade_finish_time = NULL WHERE id = ?", 
                   [$field['id']]);
        
        // تحديث البيانات
        $fields = $db->select("SELECT * FROM fields WHERE village_id = ? ORDER BY position", [$villageId]);
    }
}

// حساب الإنتاج الحالي
$production = calculateResourceProduction($villageId);

$csrfToken = generateCSRF();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحقول - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/fields.css">
</head>
<body class="game-page">
    <?php include 'includes/header.php'; ?>
    
    <div class="main-content">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="content">
            <div class="page-header">
                <h1>الحقول</h1>
                <p>إدارة وترقية حقول الموارد</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <!-- ملخص الإنتاج -->
            <div class="production-summary">
                <h3>الإنتاج الحالي</h3>
                <div class="production-grid">
                    <div class="production-item">
                        <img src="images/resources/wood.png" alt="خشب">
                        <span class="resource-name">خشب</span>
                        <span class="production-amount">+<?php echo formatNumber($production['wood']); ?>/ساعة</span>
                        <span class="current-amount"><?php echo formatNumber($resources['wood']); ?></span>
                    </div>
                    <div class="production-item">
                        <img src="images/resources/clay.png" alt="طين">
                        <span class="resource-name">طين</span>
                        <span class="production-amount">+<?php echo formatNumber($production['clay']); ?>/ساعة</span>
                        <span class="current-amount"><?php echo formatNumber($resources['clay']); ?></span>
                    </div>
                    <div class="production-item">
                        <img src="images/resources/iron.png" alt="حديد">
                        <span class="resource-name">حديد</span>
                        <span class="production-amount">+<?php echo formatNumber($production['iron']); ?>/ساعة</span>
                        <span class="current-amount"><?php echo formatNumber($resources['iron']); ?></span>
                    </div>
                    <div class="production-item">
                        <img src="images/resources/crop.png" alt="قمح">
                        <span class="resource-name">قمح</span>
                        <span class="production-amount">+<?php echo formatNumber($production['crop']); ?>/ساعة</span>
                        <span class="current-amount"><?php echo formatNumber($resources['crop']); ?></span>
                    </div>
                </div>
            </div>
            
            <!-- خريطة الحقول -->
            <div class="fields-map">
                <h3>خريطة الحقول</h3>
                <div class="village-layout">
                    <?php
                    // ترتيب الحقول في شكل دائري حول القرية
                    $fieldPositions = [
                        1 => ['top' => '10%', 'left' => '45%'],
                        2 => ['top' => '15%', 'left' => '60%'],
                        3 => ['top' => '25%', 'left' => '70%'],
                        4 => ['top' => '40%', 'left' => '75%'],
                        5 => ['top' => '55%', 'left' => '70%'],
                        6 => ['top' => '70%', 'left' => '60%'],
                        7 => ['top' => '80%', 'left' => '45%'],
                        8 => ['top' => '85%', 'left' => '30%'],
                        9 => ['top' => '80%', 'left' => '15%'],
                        10 => ['top' => '70%', 'left' => '5%'],
                        11 => ['top' => '55%', 'left' => '0%'],
                        12 => ['top' => '40%', 'left' => '5%'],
                        13 => ['top' => '25%', 'left' => '15%'],
                        14 => ['top' => '15%', 'left' => '30%'],
                        15 => ['top' => '20%', 'left' => '20%'],
                        16 => ['top' => '30%', 'left' => '35%'],
                        17 => ['top' => '50%', 'left' => '25%'],
                        18 => ['top' => '65%', 'left' => '35%']
                    ];
                    ?>
                    
                    <!-- مركز القرية -->
                    <div class="village-center">
                        <img src="images/village-center.png" alt="مركز القرية">
                    </div>
                    
                    <!-- الحقول -->
                    <?php foreach ($fields as $field): ?>
                        <?php
                        $position = $fieldPositions[$field['position']] ?? ['top' => '50%', 'left' => '50%'];
                        $isUpgrading = $field['is_upgrading'];
                        $upgradeCost = getFieldUpgradeCost($field['field_type'], $field['level']);
                        $canAfford = $upgradeCost && 
                                    $resources['wood'] >= $upgradeCost['wood'] && 
                                    $resources['clay'] >= $upgradeCost['clay'] && 
                                    $resources['iron'] >= $upgradeCost['iron'] && 
                                    $resources['crop'] >= $upgradeCost['crop'];
                        ?>
                        
                        <div class="field-slot <?php echo $isUpgrading ? 'upgrading' : ''; ?>" 
                             style="top: <?php echo $position['top']; ?>; left: <?php echo $position['left']; ?>;"
                             data-field-id="<?php echo $field['id']; ?>">
                            
                            <div class="field-image">
                                <img src="images/fields/<?php echo $field['field_type']; ?>_<?php echo min($field['level'], 10); ?>.png" 
                                     alt="<?php echo getResourceName($field['field_type']); ?>">
                                <span class="field-level"><?php echo $field['level']; ?></span>
                            </div>
                            
                            <div class="field-tooltip">
                                <h4><?php echo getResourceName($field['field_type']); ?></h4>
                                <p>المستوى: <?php echo $field['level']; ?></p>
                                <p>الإنتاج: +<?php echo formatNumber(calculateFieldProduction($field['field_type'], $field['level'])); ?>/ساعة</p>
                                
                                <?php if ($isUpgrading): ?>
                                    <p class="upgrading-status">قيد الترقية إلى المستوى <?php echo $field['level'] + 1; ?></p>
                                    <div class="upgrade-timer" data-finish-time="<?php echo strtotime($field['upgrade_finish_time']); ?>">
                                        <span class="time-remaining"></span>
                                    </div>
                                <?php elseif ($upgradeCost): ?>
                                    <div class="upgrade-info">
                                        <p>ترقية إلى المستوى <?php echo $field['level'] + 1; ?>:</p>
                                        <div class="upgrade-cost">
                                            <span class="<?php echo ($resources['wood'] >= $upgradeCost['wood']) ? 'affordable' : 'expensive'; ?>">
                                                <img src="images/resources/wood.png"> <?php echo formatNumber($upgradeCost['wood']); ?>
                                            </span>
                                            <span class="<?php echo ($resources['clay'] >= $upgradeCost['clay']) ? 'affordable' : 'expensive'; ?>">
                                                <img src="images/resources/clay.png"> <?php echo formatNumber($upgradeCost['clay']); ?>
                                            </span>
                                            <span class="<?php echo ($resources['iron'] >= $upgradeCost['iron']) ? 'affordable' : 'expensive'; ?>">
                                                <img src="images/resources/iron.png"> <?php echo formatNumber($upgradeCost['iron']); ?>
                                            </span>
                                            <span class="<?php echo ($resources['crop'] >= $upgradeCost['crop']) ? 'affordable' : 'expensive'; ?>">
                                                <img src="images/resources/crop.png"> <?php echo formatNumber($upgradeCost['crop']); ?>
                                            </span>
                                        </div>
                                        <p>وقت الترقية: <?php echo formatDuration(getFieldUpgradeTime($field['field_type'], $field['level'])); ?></p>
                                        
                                        <form method="POST" class="upgrade-form">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                            <input type="hidden" name="field_id" value="<?php echo $field['id']; ?>">
                                            <button type="submit" name="upgrade_field" class="btn btn-small" 
                                                    <?php echo $canAfford ? '' : 'disabled'; ?>>
                                                ترقية
                                            </button>
                                        </form>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- قائمة الحقول -->
            <div class="fields-list">
                <h3>قائمة الحقول</h3>
                <table class="fields-table">
                    <thead>
                        <tr>
                            <th>الموقع</th>
                            <th>النوع</th>
                            <th>المستوى</th>
                            <th>الإنتاج/ساعة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($fields as $field): ?>
                            <?php
                            $isUpgrading = $field['is_upgrading'];
                            $production = calculateFieldProduction($field['field_type'], $field['level']);
                            $upgradeCost = getFieldUpgradeCost($field['field_type'], $field['level']);
                            $canAfford = $upgradeCost && 
                                        $resources['wood'] >= $upgradeCost['wood'] && 
                                        $resources['clay'] >= $upgradeCost['clay'] && 
                                        $resources['iron'] >= $upgradeCost['iron'] && 
                                        $resources['crop'] >= $upgradeCost['crop'];
                            ?>
                            <tr>
                                <td><?php echo $field['position']; ?></td>
                                <td>
                                    <img src="images/resources/<?php echo $field['field_type']; ?>.png" alt="<?php echo getResourceName($field['field_type']); ?>">
                                    <?php echo getResourceName($field['field_type']); ?>
                                </td>
                                <td><?php echo $field['level']; ?></td>
                                <td>+<?php echo formatNumber($production); ?></td>
                                <td>
                                    <?php if ($isUpgrading): ?>
                                        <span class="status upgrading">قيد الترقية</span>
                                        <div class="upgrade-timer" data-finish-time="<?php echo strtotime($field['upgrade_finish_time']); ?>">
                                            <span class="time-remaining"></span>
                                        </div>
                                    <?php else: ?>
                                        <span class="status ready">جاهز</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!$isUpgrading && $upgradeCost): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                            <input type="hidden" name="field_id" value="<?php echo $field['id']; ?>">
                                            <button type="submit" name="upgrade_field" class="btn btn-small" 
                                                    <?php echo $canAfford ? '' : 'disabled'; ?>>
                                                ترقية
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </main>
    </div>
    
    <script>
        // تحديث مؤقتات الترقية
        function updateUpgradeTimers() {
            const timers = document.querySelectorAll('.upgrade-timer');
            
            timers.forEach(timer => {
                const finishTime = parseInt(timer.dataset.finishTime);
                const now = Math.floor(Date.now() / 1000);
                const remaining = finishTime - now;
                
                if (remaining <= 0) {
                    timer.querySelector('.time-remaining').textContent = 'اكتمل!';
                    setTimeout(() => location.reload(), 1000);
                } else {
                    timer.querySelector('.time-remaining').textContent = formatDuration(remaining);
                }
            });
        }
        
        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            if (hours > 0) {
                return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
        }
        
        // تحديث المؤقتات كل ثانية
        setInterval(updateUpgradeTimers, 1000);
        updateUpgradeTimers();
    </script>
</body>
</html>

<?php
function getResourceName($type) {
    $names = [
        'wood' => 'خشب',
        'clay' => 'طين',
        'iron' => 'حديد',
        'crop' => 'قمح'
    ];
    return $names[$type] ?? $type;
}

function getFieldUpgradeCost($fieldType, $currentLevel) {
    if ($currentLevel >= 20) return false; // الحد الأقصى للمستوى
    
    $baseCosts = [
        'wood' => ['wood' => 40, 'clay' => 100, 'iron' => 50, 'crop' => 60],
        'clay' => ['wood' => 80, 'clay' => 40, 'iron' => 80, 'crop' => 50],
        'iron' => ['wood' => 100, 'clay' => 80, 'iron' => 30, 'crop' => 60],
        'crop' => ['wood' => 70, 'clay' => 90, 'iron' => 70, 'crop' => 20]
    ];
    
    if (!isset($baseCosts[$fieldType])) {
        return false;
    }
    
    $cost = [];
    $multiplier = pow(1.5, $currentLevel);
    
    foreach ($baseCosts[$fieldType] as $resource => $amount) {
        $cost[$resource] = ceil($amount * $multiplier);
    }
    
    return $cost;
}

function getFieldUpgradeTime($fieldType, $currentLevel) {
    $baseTime = 900; // 15 دقيقة
    return ceil($baseTime * pow(1.2, $currentLevel));
}

function calculateFieldProduction($fieldType, $level) {
    $baseProduction = BASE_PRODUCTION_PER_HOUR;
    $levelMultiplier = 1 + ($level * 0.5);
    return $baseProduction * $levelMultiplier * GAME_SPEED;
}
?>
