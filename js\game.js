/**
 * ملف JavaScript الرئيسي للعبة
 * Main Game JavaScript File
 */

// متغيرات عامة
let gameData = {
    currentVillageId: null,
    resources: {},
    production: {},
    lastUpdate: null
};

// تهيئة اللعبة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeGame();
});

/**
 * تهيئة اللعبة
 */
function initializeGame() {
    // تحديث الموارد كل دقيقة
    setInterval(updateResources, 60000);
    
    // تحديث المؤقتات كل ثانية
    setInterval(updateTimers, 1000);
    
    // تهيئة الأحداث
    initializeEvents();
    
    // تحديث الموارد عند التحميل
    updateResources();
    
    console.log('Game initialized successfully');
}

/**
 * تهيئة الأحداث
 */
function initializeEvents() {
    // تأكيد الحذف
    document.querySelectorAll('.confirm-delete').forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من هذا الإجراء؟')) {
                e.preventDefault();
            }
        });
    });
    
    // تأكيد الهجوم
    document.querySelectorAll('.confirm-attack').forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من شن الهجوم؟')) {
                e.preventDefault();
            }
        });
    });
    
    // تحديث تلقائي للنماذج
    document.querySelectorAll('.auto-submit').forEach(form => {
        form.addEventListener('change', function() {
            this.submit();
        });
    });
}

/**
 * تحديث الموارد
 */
function updateResources() {
    if (!gameData.currentVillageId) {
        // محاولة الحصول على معرف القرية من الصفحة
        const villageSelect = document.getElementById('village-select');
        if (villageSelect) {
            gameData.currentVillageId = villageSelect.value;
        }
    }
    
    if (!gameData.currentVillageId) {
        return;
    }
    
    fetch(`ajax/update-resources.php?village_id=${gameData.currentVillageId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                gameData.resources = data.resources;
                gameData.production = data.production;
                gameData.lastUpdate = data.last_update;
                
                updateResourceDisplay();
            } else {
                console.error('Failed to update resources:', data.error);
            }
        })
        .catch(error => {
            console.error('Error updating resources:', error);
        });
}

/**
 * تحديث عرض الموارد
 */
function updateResourceDisplay() {
    const resourceTypes = ['wood', 'clay', 'iron', 'crop'];
    
    resourceTypes.forEach(type => {
        const amountElement = document.getElementById(`${type}-amount`);
        if (amountElement && gameData.resources[type] !== undefined) {
            amountElement.textContent = formatNumber(gameData.resources[type]);
        }
    });
}

/**
 * تحديث المؤقتات
 */
function updateTimers() {
    document.querySelectorAll('.timer, .upgrade-timer, .training-timer, .attack-timer').forEach(timer => {
        const finishTime = parseInt(timer.dataset.finishTime);
        if (!finishTime) return;
        
        const now = Math.floor(Date.now() / 1000);
        const remaining = finishTime - now;
        
        const timeElement = timer.querySelector('.time-remaining, .timer-display');
        if (!timeElement) return;
        
        if (remaining <= 0) {
            timeElement.textContent = 'اكتمل!';
            timer.classList.add('completed');
            
            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            timeElement.textContent = formatDuration(remaining);
            timer.classList.remove('completed');
        }
    });
}

/**
 * تنسيق الأرقام
 */
function formatNumber(num) {
    if (typeof num !== 'number') {
        num = parseInt(num) || 0;
    }
    return new Intl.NumberFormat('ar-SA').format(num);
}

/**
 * تنسيق المدة الزمنية
 */
function formatDuration(seconds) {
    if (seconds <= 0) return '00:00:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
}

/**
 * تغيير القرية
 */
function changeVillage(villageId) {
    if (villageId && villageId !== gameData.currentVillageId) {
        gameData.currentVillageId = villageId;
        const currentPath = window.location.pathname;
        window.location.href = `change-village.php?id=${villageId}&redirect=${encodeURIComponent(currentPath)}`;
    }
}

/**
 * إظهار رسالة تأكيد
 */
function confirmAction(message, callback) {
    if (confirm(message)) {
        if (typeof callback === 'function') {
            callback();
        }
        return true;
    }
    return false;
}

/**
 * إظهار رسالة تنبيه
 */
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="close" onclick="this.parentElement.remove()">×</button>
    `;
    
    // إدراج التنبيه في أعلى المحتوى
    const content = document.querySelector('.content, .main-content');
    if (content) {
        content.insertBefore(alertDiv, content.firstChild);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentElement) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

/**
 * تحميل محتوى عبر AJAX
 */
function loadContent(url, targetElement, callback) {
    fetch(url)
        .then(response => response.text())
        .then(html => {
            if (targetElement) {
                targetElement.innerHTML = html;
            }
            if (typeof callback === 'function') {
                callback();
            }
        })
        .catch(error => {
            console.error('Error loading content:', error);
            showAlert('حدث خطأ أثناء تحميل المحتوى', 'error');
        });
}

/**
 * إرسال نموذج عبر AJAX
 */
function submitForm(form, callback) {
    const formData = new FormData(form);
    
    fetch(form.action || window.location.href, {
        method: form.method || 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message || 'تم الحفظ بنجاح', 'success');
            if (typeof callback === 'function') {
                callback(data);
            }
        } else {
            showAlert(data.error || 'حدث خطأ', 'error');
        }
    })
    .catch(error => {
        console.error('Error submitting form:', error);
        showAlert('حدث خطأ أثناء الإرسال', 'error');
    });
}

/**
 * تحديث الصفحة تلقائياً
 */
function autoRefresh(interval = 300000) { // 5 دقائق افتراضياً
    setInterval(() => {
        location.reload();
    }, interval);
}

/**
 * التحقق من الاتصال بالإنترنت
 */
function checkConnection() {
    if (!navigator.onLine) {
        showAlert('لا يوجد اتصال بالإنترنت', 'warning');
        return false;
    }
    return true;
}

/**
 * حفظ البيانات في التخزين المحلي
 */
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
        console.error('Error saving to localStorage:', error);
    }
}

/**
 * استرجاع البيانات من التخزين المحلي
 */
function loadFromLocalStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (error) {
        console.error('Error loading from localStorage:', error);
        return null;
    }
}

/**
 * تنظيف التخزين المحلي
 */
function clearLocalStorage() {
    try {
        localStorage.clear();
    } catch (error) {
        console.error('Error clearing localStorage:', error);
    }
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * التحقق من قوة كلمة المرور
 */
function checkPasswordStrength(password) {
    let strength = 0;
    
    if (password.length >= 6) strength++;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    if (strength < 3) return 'ضعيفة';
    if (strength < 5) return 'متوسطة';
    return 'قوية';
}

/**
 * تحديث شريط التقدم
 */
function updateProgressBar(element, percentage) {
    if (element) {
        element.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
        element.setAttribute('aria-valuenow', percentage);
    }
}

/**
 * تحريك العنصر بسلاسة
 */
function smoothScrollTo(element) {
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

/**
 * إضافة تأثير التحميل
 */
function showLoading(element) {
    if (element) {
        element.classList.add('loading');
        element.disabled = true;
    }
}

/**
 * إزالة تأثير التحميل
 */
function hideLoading(element) {
    if (element) {
        element.classList.remove('loading');
        element.disabled = false;
    }
}

// تصدير الدوال للاستخدام العام
window.gameUtils = {
    formatNumber,
    formatDuration,
    changeVillage,
    confirmAction,
    showAlert,
    loadContent,
    submitForm,
    validateEmail,
    checkPasswordStrength,
    updateProgressBar,
    smoothScrollTo,
    showLoading,
    hideLoading
};
