<?php
/**
 * صفحة الإسطبل
 * Stable Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];
$villageId = $_SESSION['current_village_id'];

// التحقق من القرية
$village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$villageId, $userId]);
if (!$village) {
    redirect('index.php');
}

// التحقق من وجود الإسطبل
$stable = $db->selectOne("SELECT * FROM buildings WHERE village_id = ? AND building_type = 'stable'", [$villageId]);
if (!$stable || $stable['level'] < 1) {
    showError('يجب بناء الإسطبل أولاً');
    redirect('buildings.php');
}

// تحديث الموارد
updateVillageResources($villageId);

// الحصول على الموارد
$resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);

// الحصول على الوحدات الحالية
$units = $db->select("SELECT * FROM units WHERE village_id = ?", [$villageId]);
$unitsArray = [];
foreach ($units as $unit) {
    $unitsArray[$unit['unit_type']] = $unit;
}

// وحدات الفرسان المتاحة في الإسطبل
$cavalryUnits = ['theutates_thunder', 'druidrider', 'haeduan'];

// معالجة طلب التدريب
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['train_units'])) {
    $unitType = $_POST['unit_type'] ?? '';
    $quantity = intval($_POST['quantity'] ?? 0);
    $csrf = $_POST['csrf_token'] ?? '';
    
    if (!validateCSRF($csrf)) {
        $error = 'رمز الأمان غير صحيح';
    } elseif (!in_array($unitType, $cavalryUnits)) {
        $error = 'نوع الوحدة غير صحيح';
    } elseif ($quantity <= 0 || $quantity > 500) {
        $error = 'الكمية يجب أن تكون بين 1 و 500';
    } else {
        $unitCost = getCavalryUnitCost($unitType);
        $totalCost = [
            'wood' => $unitCost['wood'] * $quantity,
            'clay' => $unitCost['clay'] * $quantity,
            'iron' => $unitCost['iron'] * $quantity,
            'crop' => $unitCost['crop'] * $quantity
        ];
        
        // التحقق من توفر الموارد
        if ($resources['wood'] < $totalCost['wood'] || 
            $resources['clay'] < $totalCost['clay'] || 
            $resources['iron'] < $totalCost['iron'] || 
            $resources['crop'] < $totalCost['crop']) {
            $error = 'الموارد غير كافية للتدريب';
        } else {
            // حساب وقت التدريب
            $trainingTime = getCavalryTrainingTime($unitType, $stable['level']) * $quantity;
            $finishTime = date('Y-m-d H:i:s', time() + $trainingTime);
            
            $db->beginTransaction();
            try {
                // خصم الموارد
                $db->update("UPDATE resources SET wood = wood - ?, clay = clay - ?, iron = iron - ?, crop = crop - ? WHERE village_id = ?", 
                           [$totalCost['wood'], $totalCost['clay'], $totalCost['iron'], $totalCost['crop'], $villageId]);
                
                // إضافة أو تحديث الوحدة
                $existingUnit = $unitsArray[$unitType] ?? null;
                if ($existingUnit) {
                    if ($existingUnit['is_training']) {
                        $error = 'هذا النوع من الوحدات قيد التدريب بالفعل';
                        $db->rollback();
                    } else {
                        $db->update("UPDATE units SET is_training = 1, training_finish_time = ? WHERE id = ?", 
                                   [$finishTime, $existingUnit['id']]);
                        $success = "تم بدء تدريب {$quantity} من " . UNIT_TYPES[$unitType] . " بنجاح!";
                        $db->commit();
                    }
                } else {
                    $db->insert("INSERT INTO units (village_id, unit_type, count, is_training, training_finish_time) VALUES (?, ?, ?, ?, ?)", 
                               [$villageId, $unitType, 0, 1, $finishTime]);
                    $success = "تم بدء تدريب {$quantity} من " . UNIT_TYPES[$unitType] . " بنجاح!";
                    $db->commit();
                }
                
                if ($success) {
                    // تحديث البيانات
                    $resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);
                    $units = $db->select("SELECT * FROM units WHERE village_id = ?", [$villageId]);
                    $unitsArray = [];
                    foreach ($units as $unit) {
                        $unitsArray[$unit['unit_type']] = $unit;
                    }
                }
            } catch (Exception $e) {
                $db->rollback();
                $error = 'حدث خطأ أثناء التدريب';
            }
        }
    }
}

// التحقق من اكتمال التدريب
foreach ($units as $unit) {
    if ($unit['is_training'] && strtotime($unit['training_finish_time']) <= time()) {
        // إضافة الوحدات المدربة
        $db->update("UPDATE units SET count = count + 1, is_training = 0, training_finish_time = NULL WHERE id = ?", 
                   [$unit['id']]);
        
        // تحديث البيانات
        $units = $db->select("SELECT * FROM units WHERE village_id = ?", [$villageId]);
        $unitsArray = [];
        foreach ($units as $unit) {
            $unitsArray[$unit['unit_type']] = $unit;
        }
    }
}

$csrfToken = generateCSRF();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإسطبل - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/army.css">
    <link rel="stylesheet" href="css/stable.css">
</head>
<body class="game-page">
    <?php include 'includes/header.php'; ?>
    
    <div class="main-content">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="content">
            <div class="page-header">
                <h1>الإسطبل</h1>
                <p>تدريب وحدات الفرسان - المستوى <?php echo $stable['level']; ?></p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <!-- معلومات الإسطبل -->
            <div class="stable-info">
                <h3>🐎 الإسطبل <span class="stable-level">المستوى <?php echo $stable['level']; ?></span></h3>
                <p class="stable-description">
                    الإسطبل هو المكان الذي يتم فيه تدريب وحدات الفرسان السريعة والقوية. 
                    كلما ارتفع مستوى الإسطبل، قل وقت التدريب وزادت كفاءة الوحدات.
                </p>
                <div class="stable-benefits">
                    <div class="benefit-item">
                        <div class="icon">⚡</div>
                        <div class="text">تقليل وقت التدريب بنسبة <?php echo ($stable['level'] * 10); ?>%</div>
                    </div>
                    <div class="benefit-item">
                        <div class="icon">🏇</div>
                        <div class="text">إمكانية تدريب وحدات فرسان متقدمة</div>
                    </div>
                    <div class="benefit-item">
                        <div class="icon">🛡️</div>
                        <div class="text">زيادة قوة الوحدات المدربة</div>
                    </div>
                </div>
            </div>
            
            <!-- شريط الموارد -->
            <div class="resources-summary">
                <div class="resource">
                    <img src="images/resources/wood.png" alt="خشب">
                    <span><?php echo formatNumber($resources['wood']); ?></span>
                </div>
                <div class="resource">
                    <img src="images/resources/clay.png" alt="طين">
                    <span><?php echo formatNumber($resources['clay']); ?></span>
                </div>
                <div class="resource">
                    <img src="images/resources/iron.png" alt="حديد">
                    <span><?php echo formatNumber($resources['iron']); ?></span>
                </div>
                <div class="resource">
                    <img src="images/resources/crop.png" alt="قمح">
                    <span><?php echo formatNumber($resources['crop']); ?></span>
                </div>
            </div>
            
            <!-- وحدات الفرسان المتاحة للتدريب -->
            <div class="training-units">
                <h3>وحدات الفرسان المتاحة</h3>
                <div class="units-grid">
                    <?php foreach ($cavalryUnits as $unitType): ?>
                        <?php
                        $unitCost = getCavalryUnitCost($unitType);
                        $unitStats = getCavalryUnitStats($unitType);
                        $currentUnit = $unitsArray[$unitType] ?? null;
                        $currentCount = $currentUnit ? $currentUnit['count'] : 0;
                        $isTraining = $currentUnit && $currentUnit['is_training'];
                        $canAfford = $resources['wood'] >= $unitCost['wood'] && 
                                    $resources['clay'] >= $unitCost['clay'] && 
                                    $resources['iron'] >= $unitCost['iron'] && 
                                    $resources['crop'] >= $unitCost['crop'];
                        ?>
                        
                        <div class="unit-card cavalry-unit <?php echo $isTraining ? 'training' : ''; ?>">
                            <div class="unit-image">
                                <img src="images/units/<?php echo $unitType; ?>.png" alt="<?php echo UNIT_TYPES[$unitType]; ?>">
                                <?php if ($currentCount > 0): ?>
                                    <span class="unit-count"><?php echo formatNumber($currentCount); ?></span>
                                <?php endif; ?>
                                <div class="cavalry-badge">🐎</div>
                            </div>
                            
                            <div class="unit-info">
                                <h4><?php echo UNIT_TYPES[$unitType]; ?></h4>
                                
                                <!-- إحصائيات الوحدة -->
                                <div class="unit-stats">
                                    <div class="stat attack">
                                        <span class="label">هجوم:</span>
                                        <span class="value"><?php echo $unitStats['attack']; ?></span>
                                    </div>
                                    <div class="stat defense">
                                        <span class="label">دفاع:</span>
                                        <span class="value"><?php echo $unitStats['defense']; ?></span>
                                    </div>
                                    <div class="stat speed">
                                        <span class="label">سرعة:</span>
                                        <span class="value"><?php echo $unitStats['speed']; ?></span>
                                    </div>
                                    <div class="stat carry">
                                        <span class="label">حمولة:</span>
                                        <span class="value"><?php echo $unitStats['carry']; ?></span>
                                    </div>
                                </div>
                                
                                <!-- وصف الوحدة -->
                                <div class="unit-description">
                                    <?php echo getCavalryUnitDescription($unitType); ?>
                                </div>
                                
                                <!-- تكلفة التدريب -->
                                <div class="training-cost">
                                    <h5>تكلفة التدريب:</h5>
                                    <div class="cost-grid">
                                        <span class="cost-item <?php echo ($resources['wood'] >= $unitCost['wood']) ? 'affordable' : 'expensive'; ?>">
                                            <img src="images/resources/wood.png" alt="خشب">
                                            <?php echo formatNumber($unitCost['wood']); ?>
                                        </span>
                                        <span class="cost-item <?php echo ($resources['clay'] >= $unitCost['clay']) ? 'affordable' : 'expensive'; ?>">
                                            <img src="images/resources/clay.png" alt="طين">
                                            <?php echo formatNumber($unitCost['clay']); ?>
                                        </span>
                                        <span class="cost-item <?php echo ($resources['iron'] >= $unitCost['iron']) ? 'affordable' : 'expensive'; ?>">
                                            <img src="images/resources/iron.png" alt="حديد">
                                            <?php echo formatNumber($unitCost['iron']); ?>
                                        </span>
                                        <span class="cost-item <?php echo ($resources['crop'] >= $unitCost['crop']) ? 'affordable' : 'expensive'; ?>">
                                            <img src="images/resources/crop.png" alt="قمح">
                                            <?php echo formatNumber($unitCost['crop']); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- وقت التدريب -->
                                <div class="training-time">
                                    <span>وقت التدريب: <?php echo formatDuration(getCavalryTrainingTime($unitType, $stable['level'])); ?></span>
                                </div>
                                
                                <?php if ($isTraining): ?>
                                    <!-- مؤقت التدريب -->
                                    <div class="training-progress">
                                        <span class="status">قيد التدريب...</span>
                                        <div class="training-timer" data-finish-time="<?php echo strtotime($currentUnit['training_finish_time']); ?>">
                                            <span class="time-remaining"></span>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <!-- نموذج التدريب -->
                                    <form method="POST" class="training-form">
                                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                        <input type="hidden" name="unit_type" value="<?php echo $unitType; ?>">
                                        
                                        <div class="quantity-input">
                                            <label for="quantity_<?php echo $unitType; ?>">الكمية:</label>
                                            <input type="number" id="quantity_<?php echo $unitType; ?>" name="quantity" 
                                                   min="1" max="500" value="1" 
                                                   onchange="updateCavalryTrainingCost('<?php echo $unitType; ?>', this.value)">
                                        </div>
                                        
                                        <div class="total-cost" id="total_cost_<?php echo $unitType; ?>">
                                            <span>التكلفة الإجمالية: نفس التكلفة أعلاه</span>
                                        </div>
                                        
                                        <button type="submit" name="train_units" class="btn btn-primary cavalry-btn" 
                                                <?php echo $canAfford ? '' : 'disabled'; ?>>
                                            🐎 بدء التدريب
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- وحدات الفرسان الحالية -->
            <?php 
            $cavalryUnitsExist = false;
            foreach ($units as $unit) {
                if (in_array($unit['unit_type'], $cavalryUnits)) {
                    $cavalryUnitsExist = true;
                    break;
                }
            }
            ?>
            
            <?php if ($cavalryUnitsExist): ?>
                <div class="current-units">
                    <h3>وحدات الفرسان الحالية</h3>
                    <table class="units-table cavalry-table">
                        <thead>
                            <tr>
                                <th>الوحدة</th>
                                <th>العدد</th>
                                <th>القوة الهجومية</th>
                                <th>القوة الدفاعية</th>
                                <th>السرعة الإجمالية</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($units as $unit): ?>
                                <?php if (in_array($unit['unit_type'], $cavalryUnits)): ?>
                                    <?php $unitStats = getCavalryUnitStats($unit['unit_type']); ?>
                                    <tr>
                                        <td class="unit-name">
                                            <img src="images/units/<?php echo $unit['unit_type']; ?>.png" alt="<?php echo UNIT_TYPES[$unit['unit_type']]; ?>">
                                            <?php echo UNIT_TYPES[$unit['unit_type']]; ?>
                                        </td>
                                        <td><?php echo formatNumber($unit['count']); ?></td>
                                        <td class="attack-power"><?php echo formatNumber($unit['count'] * $unitStats['attack']); ?></td>
                                        <td class="defense-power"><?php echo formatNumber($unit['count'] * $unitStats['defense']); ?></td>
                                        <td class="speed-value"><?php echo $unitStats['speed']; ?></td>
                                        <td>
                                            <?php if ($unit['is_training']): ?>
                                                <span class="status-indicator training">قيد التدريب</span>
                                                <div class="training-timer" data-finish-time="<?php echo strtotime($unit['training_finish_time']); ?>">
                                                    <span class="time-remaining"></span>
                                                </div>
                                            <?php else: ?>
                                                <span class="status-indicator ready">جاهز</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
            
            <!-- الإجراءات السريعة -->
            <div class="quick-actions">
                <h3>الإجراءات العسكرية</h3>
                <div class="actions-grid">
                    <a href="barracks.php" class="action-btn">
                        <img src="images/icons/barracks.png" alt="الثكنة">
                        <span>تدريب المشاة</span>
                    </a>
                    <a href="army.php" class="action-btn">
                        <img src="images/icons/army.png" alt="الجيش">
                        <span>إدارة الجيش</span>
                    </a>
                    <a href="map.php" class="action-btn">
                        <img src="images/icons/map.png" alt="الخريطة">
                        <span>البحث عن أهداف</span>
                    </a>
                    <a href="buildings.php" class="action-btn">
                        <img src="images/icons/buildings.png" alt="المباني">
                        <span>ترقية الإسطبل</span>
                    </a>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // تحديث مؤقتات التدريب
        function updateTrainingTimers() {
            document.querySelectorAll('.training-timer').forEach(timer => {
                const finishTime = parseInt(timer.dataset.finishTime);
                const now = Math.floor(Date.now() / 1000);
                const remaining = finishTime - now;
                
                const timeElement = timer.querySelector('.time-remaining');
                if (remaining <= 0) {
                    timeElement.textContent = 'اكتمل!';
                    setTimeout(() => location.reload(), 1000);
                } else {
                    timeElement.textContent = formatDuration(remaining);
                }
            });
        }
        
        // تحديث تكلفة التدريب للفرسان
        function updateCavalryTrainingCost(unitType, quantity) {
            const costs = {
                'theutates_thunder': {wood: 350, clay: 450, iron: 230, crop: 60},
                'druidrider': {wood: 360, clay: 330, iron: 280, crop: 120},
                'haeduan': {wood: 500, clay: 620, iron: 675, crop: 240}
            };
            
            const unitCost = costs[unitType];
            if (!unitCost) return;
            
            const totalCost = {
                wood: unitCost.wood * quantity,
                clay: unitCost.clay * quantity,
                iron: unitCost.iron * quantity,
                crop: unitCost.crop * quantity
            };
            
            const costElement = document.getElementById(`total_cost_${unitType}`);
            if (costElement) {
                costElement.innerHTML = `
                    <span>التكلفة الإجمالية:</span>
                    <div class="cost-breakdown">
                        <span class="cost-item">
                            <img src="images/resources/wood.png" alt="خشب">
                            ${formatNumber(totalCost.wood)}
                        </span>
                        <span class="cost-item">
                            <img src="images/resources/clay.png" alt="طين">
                            ${formatNumber(totalCost.clay)}
                        </span>
                        <span class="cost-item">
                            <img src="images/resources/iron.png" alt="حديد">
                            ${formatNumber(totalCost.iron)}
                        </span>
                        <span class="cost-item">
                            <img src="images/resources/crop.png" alt="قمح">
                            ${formatNumber(totalCost.crop)}
                        </span>
                    </div>
                `;
            }
        }
        
        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            if (hours > 0) {
                return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
        }
        
        function formatNumber(num) {
            return new Intl.NumberFormat('ar-SA').format(num);
        }
        
        // تحديث المؤقتات كل ثانية
        setInterval(updateTrainingTimers, 1000);
        updateTrainingTimers();
    </script>
</body>
</html>

<?php
function getCavalryUnitCost($unitType) {
    $costs = [
        'theutates_thunder' => ['wood' => 350, 'clay' => 450, 'iron' => 230, 'crop' => 60],
        'druidrider' => ['wood' => 360, 'clay' => 330, 'iron' => 280, 'crop' => 120],
        'haeduan' => ['wood' => 500, 'clay' => 620, 'iron' => 675, 'crop' => 240]
    ];
    
    return $costs[$unitType] ?? ['wood' => 0, 'clay' => 0, 'iron' => 0, 'crop' => 0];
}

function getCavalryTrainingTime($unitType, $stableLevel) {
    $baseTimes = [
        'theutates_thunder' => 2400,  // 40 دقيقة
        'druidrider' => 3000,         // 50 دقيقة
        'haeduan' => 3600             // 60 دقيقة
    ];
    
    $baseTime = $baseTimes[$unitType] ?? 3600;
    $levelBonus = 1 - ($stableLevel * 0.1); // تقليل 10% لكل مستوى
    
    return ceil($baseTime * $levelBonus * BUILDING_TIME_FACTOR);
}

function getCavalryUnitStats($unitType) {
    $stats = [
        'theutates_thunder' => ['attack' => 90, 'defense' => 25, 'speed' => 19, 'carry' => 75],
        'druidrider' => ['attack' => 45, 'defense' => 115, 'speed' => 16, 'carry' => 35],
        'haeduan' => ['attack' => 140, 'defense' => 60, 'speed' => 13, 'carry' => 65]
    ];
    
    return $stats[$unitType] ?? ['attack' => 0, 'defense' => 0, 'speed' => 0, 'carry' => 0];
}

function getCavalryUnitDescription($unitType) {
    $descriptions = [
        'theutates_thunder' => 'وحدة فرسان سريعة ومتوازنة، مثالية للغارات السريعة ونقل الموارد.',
        'druidrider' => 'وحدة دفاعية قوية من الفرسان، ممتازة لحماية القرية والدفاع ضد الهجمات.',
        'haeduan' => 'أقوى وحدات الفرسان هجومياً، مثالية للهجمات الكبيرة والحاسمة.'
    ];
    
    return $descriptions[$unitType] ?? '';
}
?>
