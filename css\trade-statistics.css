/* ملف CSS لإحصائيات التجارة */
/* Trade Statistics CSS */

/* فلتر الفترة الزمنية */
.period-filter {
    background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    border: 2px solid #007bff;
}

.period-filter h3 {
    color: #0056b3;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.filter-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    border: 2px solid #007bff;
    border-radius: 6px;
    text-decoration: none;
    color: #007bff;
    font-weight: 500;
    transition: all 0.3s ease;
    background-color: white;
}

.filter-btn:hover {
    background-color: #007bff;
    color: white;
}

.filter-btn.active {
    background-color: #007bff;
    color: white;
}

/* الإحصائيات العامة */
.general-stats {
    margin-bottom: 30px;
}

.general-stats h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-card.outgoing {
    border-color: #dc3545;
}

.stat-card.incoming {
    border-color: #28a745;
}

.stat-card.total {
    border-color: #007bff;
}

.stat-card.average {
    border-color: #ffc107;
}

.stat-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0,0,0,0.1);
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #495057;
    line-height: 1;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* إحصائيات الموارد */
.resource-stats {
    margin-bottom: 30px;
}

.resource-stats h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.resource-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.resource-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.resource-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.resource-header img {
    width: 32px;
    height: 32px;
}

.resource-header h4 {
    color: #495057;
    margin: 0;
    font-size: 1.1rem;
}

.resource-data {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.data-item.sent {
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.data-item.received {
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.data-item.balance {
    background-color: #e2e3e5;
    border-color: #d6d8db;
    font-weight: bold;
}

.data-item .label {
    color: #495057;
    font-weight: 500;
}

.data-item .value {
    color: #495057;
    font-weight: bold;
}

.data-item .value.positive {
    color: #28a745;
}

.data-item .value.negative {
    color: #dc3545;
}

/* أكثر الشركاء تجارة */
.top-partners {
    margin-bottom: 30px;
}

.top-partners h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.partners-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.partner-item {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    border: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.partner-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.partner-rank {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

.partner-info {
    flex: 1;
}

.partner-name {
    font-weight: bold;
    color: #495057;
    font-size: 1.1rem;
}

.partner-username {
    color: #6c757d;
    font-size: 0.9rem;
}

.partner-stats {
    text-align: right;
}

.trade-count {
    color: #007bff;
    font-weight: bold;
    font-size: 1rem;
}

.total-resources {
    color: #6c757d;
    font-size: 0.9rem;
}

/* الإحصائيات اليومية */
.daily-stats {
    margin-bottom: 30px;
}

.daily-stats h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.daily-chart {
    display: flex;
    justify-content: space-between;
    align-items: end;
    gap: 10px;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    margin-bottom: 15px;
    height: 200px;
}

.day-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    flex: 1;
}

.day-date {
    color: #6c757d;
    font-size: 0.8rem;
    font-weight: 500;
}

.day-bars {
    display: flex;
    gap: 2px;
    align-items: end;
    height: 120px;
}

.bar {
    width: 20px;
    min-height: 5px;
    border-radius: 3px 3px 0 0;
    position: relative;
    transition: all 0.3s ease;
}

.bar.outgoing {
    background: linear-gradient(to top, #dc3545, #ff6b6b);
}

.bar.incoming {
    background: linear-gradient(to top, #28a745, #6bff6b);
}

.bar:hover {
    transform: scale(1.1);
}

.bar-value {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.7rem;
    font-weight: bold;
    color: #495057;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bar:hover .bar-value {
    opacity: 1;
}

.day-total {
    color: #495057;
    font-weight: bold;
    font-size: 0.9rem;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
}

.legend-color.outgoing {
    background: linear-gradient(135deg, #dc3545, #ff6b6b);
}

.legend-color.incoming {
    background: linear-gradient(135deg, #28a745, #6bff6b);
}

/* نصائح التجارة */
.trade-tips {
    margin-bottom: 30px;
}

.trade-tips h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.tip-card {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #ffc107;
    text-align: center;
    transition: all 0.3s ease;
}

.tip-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
}

.tip-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.tip-card h4 {
    color: #856404;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.tip-card p {
    color: #856404;
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-card {
        padding: 15px;
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .resources-grid {
        grid-template-columns: 1fr;
    }
    
    .partner-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .partner-stats {
        text-align: center;
    }
    
    .daily-chart {
        height: 150px;
        padding: 15px;
    }
    
    .day-bars {
        height: 80px;
    }
    
    .bar {
        width: 15px;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-buttons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 12px;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .stat-value {
        font-size: 1.3rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
    
    .resource-card {
        padding: 15px;
    }
    
    .resource-header img {
        width: 28px;
        height: 28px;
    }
    
    .partner-rank {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
    
    .daily-chart {
        height: 120px;
        padding: 10px;
    }
    
    .day-bars {
        height: 60px;
    }
    
    .bar {
        width: 12px;
    }
    
    .tip-card {
        padding: 15px;
    }
    
    .tip-icon {
        font-size: 1.5rem;
    }
    
    .tip-card h4 {
        font-size: 1rem;
    }
    
    .tip-card p {
        font-size: 0.8rem;
    }
    
    .filter-btn {
        padding: 6px 12px;
        font-size: 0.9rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .period-filter {
        display: none;
    }
    
    .stat-card,
    .resource-card,
    .partner-item,
    .tip-card {
        border: 1px solid #000;
        box-shadow: none;
        margin-bottom: 15px;
        break-inside: avoid;
    }
    
    .daily-chart {
        border: 1px solid #000;
    }
}

/* تأثيرات الحركة */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card,
.resource-card,
.partner-item,
.tip-card {
    animation: slideIn 0.3s ease-out;
}

/* تحسينات للوضع المظلم (إذا تم تطبيقه لاحقاً) */
@media (prefers-color-scheme: dark) {
    .stat-card,
    .resource-card,
    .partner-item,
    .daily-chart {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .data-item {
        background-color: #3d3d3d;
        border-color: #555;
        color: #fff;
    }
    
    .data-item.sent {
        background-color: #4d1f1f;
        border-color: #6d2c2c;
    }
    
    .data-item.received {
        background-color: #1f4d1f;
        border-color: #2c6d2c;
    }
    
    .data-item.balance {
        background-color: #3d3d3d;
        border-color: #555;
    }
    
    .period-filter {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .filter-btn {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .filter-btn:hover,
    .filter-btn.active {
        background-color: #007bff;
        color: #fff;
    }
    
    .tip-card {
        background: linear-gradient(135deg, #4d4d00 0%, #6d6d00 100%);
        border-color: #ffc107;
    }
}
