<?php
class Village {
    private $conn;
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    public function createVillage($userId, $name, $x, $y) {
        $stmt = $this->conn->prepare("INSERT INTO villages (user_id, name, x_coord, y_coord) VALUES (?, ?, ?, ?)");
        $result = $stmt->execute([$userId, $name, $x, $y]);
        
        if ($result) {
            $villageId = $this->conn->lastInsertId();
            $this->createInitialBuildings($villageId);
            return $villageId;
        }
        return false;
    }
    
    private function createInitialBuildings($villageId) {
        $initialBuildings = [
            ['house', 1, 5, 5],
            ['farm', 1, 3, 3],
            ['warehouse', 1, 7, 7]
        ];
        
        foreach ($initialBuildings as $building) {
            $stmt = $this->conn->prepare("INSERT INTO buildings (village_id, building_type, level, x_position, y_position) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$villageId, $building[0], $building[1], $building[2], $building[3]]);
        }
    }
    
    public function getUserVillages($userId) {
        $stmt = $this->conn->prepare("SELECT * FROM villages WHERE user_id = ?");
        $stmt->execute([$userId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getVillageBuildings($villageId) {
        $stmt = $this->conn->prepare("SELECT * FROM buildings WHERE village_id = ?");
        $stmt->execute([$villageId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>