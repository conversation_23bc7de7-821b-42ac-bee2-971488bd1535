/**
 * تنسيق صفحة المباني
 * Buildings Page Styles
 */

.buildings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.buildings-header {
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
}

.buildings-header h1 {
    margin: 0;
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.buildings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.building-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.building-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.building-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
}

.building-icon {
    width: 64px;
    height: 64px;
    float: right;
    margin-left: 15px;
    border-radius: 8px;
    background: #f8f9fa;
    border: 2px solid #dee2e6;
}

.building-name {
    font-size: 1.3rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.building-level {
    color: #6c757d;
    font-size: 0.9rem;
}

.building-description {
    color: #495057;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-top: 10px;
}

.building-body {
    padding: 20px;
}

.building-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 5px;
    font-size: 0.9rem;
}

.stat-label {
    color: #6c757d;
}

.stat-value {
    font-weight: bold;
    color: #2c3e50;
}

.upgrade-section {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
}

.upgrade-costs {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.cost-item {
    text-align: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
}

.cost-icon {
    width: 24px;
    height: 24px;
    margin-bottom: 5px;
}

.cost-amount {
    font-weight: bold;
    color: #2c3e50;
    font-size: 0.9rem;
}

.cost-amount.insufficient {
    color: #dc3545;
}

.upgrade-time {
    text-align: center;
    margin-bottom: 15px;
    padding: 10px;
    background: #e3f2fd;
    border-radius: 5px;
    color: #1976d2;
    font-weight: bold;
}

.building-actions {
    display: flex;
    gap: 10px;
}

.btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

.building-queue {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.queue-header {
    font-weight: bold;
    color: #856404;
    margin-bottom: 10px;
}

.queue-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 5px;
    margin-bottom: 10px;
}

.queue-building {
    font-weight: bold;
    color: #2c3e50;
}

.queue-time {
    color: #6c757d;
    font-size: 0.9rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 5px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
}

.building-requirements {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
}

.requirements-header {
    font-weight: bold;
    color: #721c24;
    margin-bottom: 10px;
}

.requirement-item {
    color: #721c24;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.building-effects {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
}

.effects-header {
    font-weight: bold;
    color: #155724;
    margin-bottom: 10px;
}

.effect-item {
    color: #155724;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.max-level-notice {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 5px;
    padding: 15px;
    text-align: center;
    color: #0c5460;
    font-weight: bold;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .buildings-grid {
        grid-template-columns: 1fr;
    }
    
    .building-stats {
        grid-template-columns: 1fr;
    }
    
    .upgrade-costs {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .building-actions {
        flex-direction: column;
    }
    
    .buildings-header h1 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .buildings-container {
        padding: 10px;
    }
    
    .building-header {
        padding: 15px;
    }
    
    .building-body {
        padding: 15px;
    }
    
    .upgrade-costs {
        grid-template-columns: 1fr;
    }
}

/* تأثيرات خاصة */
.building-card.upgrading {
    border: 2px solid #ffc107;
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.3);
}

.building-card.max-level {
    border: 2px solid #28a745;
    box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
}

.building-card.unavailable {
    opacity: 0.6;
    filter: grayscale(50%);
}

/* أنيميشن للتحديثات */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.building-card.upgrading .building-icon {
    animation: pulse 2s infinite;
}

/* تنسيق خاص للمباني المختلفة */
.building-card[data-type="main_building"] .building-header {
    background: linear-gradient(135deg, #6f42c1 0%, #8e44ad 100%);
    color: white;
}

.building-card[data-type="barracks"] .building-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.building-card[data-type="marketplace"] .building-header {
    background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
    color: white;
}

.building-card[data-type="warehouse"] .building-header {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
}
