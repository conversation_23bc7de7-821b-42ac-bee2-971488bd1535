/* ملف CSS للمؤقتات والعدادات */
/* Timers CSS */

/* المؤقت الأساسي */
.timer {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
    border: 2px solid #007bff;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #0056b3;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.timer:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
}

/* أنواع المؤقتات */
.timer.building-timer {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffc107;
    color: #856404;
}

.timer.training-timer {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #28a745;
    color: #155724;
}

.timer.attack-timer {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #dc3545;
    color: #721c24;
}

.timer.trade-timer {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-color: #17a2b8;
    color: #0c5460;
}

/* المؤقت المكتمل */
.timer.completed {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #28a745;
    color: #155724;
    animation: pulse 1s infinite;
}

.timer.completed::before {
    content: "✓ ";
    color: #28a745;
}

/* أيقونات المؤقتات */
.timer-icon {
    font-size: 1rem;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.building-timer .timer-icon::before {
    content: "🏗️";
}

.training-timer .timer-icon::before {
    content: "⚔️";
}

.attack-timer .timer-icon::before {
    content: "🗡️";
}

.trade-timer .timer-icon::before {
    content: "🚚";
}

/* عرض الوقت */
.time-remaining,
.timer-display {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    letter-spacing: 0.5px;
    min-width: 60px;
    text-align: center;
}

/* مؤقت كبير */
.timer-large {
    padding: 15px 20px;
    font-size: 1.2rem;
    border-radius: 10px;
    min-width: 120px;
    justify-content: center;
}

.timer-large .time-remaining {
    font-size: 1.4rem;
    min-width: 100px;
}

/* مؤقت صغير */
.timer-small {
    padding: 4px 8px;
    font-size: 0.8rem;
    border-radius: 4px;
    min-width: 50px;
}

.timer-small .time-remaining {
    font-size: 0.8rem;
    min-width: 40px;
}

/* مؤقت دائري */
.timer-circular {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    background: conic-gradient(#007bff 0deg, #e9ecef 0deg);
    transition: all 0.3s ease;
}

.timer-circular .timer-display {
    font-size: 0.9rem;
    font-weight: bold;
    color: #495057;
    z-index: 2;
}

.timer-circular .timer-label {
    font-size: 0.7rem;
    color: #6c757d;
    z-index: 2;
}

.timer-circular::before {
    content: '';
    position: absolute;
    top: 4px;
    left: 4px;
    right: 4px;
    bottom: 4px;
    background-color: white;
    border-radius: 50%;
    z-index: 1;
}

/* شريط تقدم المؤقت */
.timer-progress {
    width: 100%;
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 8px;
}

.timer-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 3px;
    transition: width 1s linear;
    position: relative;
}

.timer-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

/* مجموعة المؤقتات */
.timers-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

.timers-group .timer {
    flex: 0 0 auto;
}

/* قائمة المؤقتات */
.timers-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.timer-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.timer-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.timer-item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.timer-item-title {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.timer-item-description {
    color: #6c757d;
    font-size: 0.8rem;
}

.timer-item .timer {
    margin-left: 15px;
}

/* عداد تنازلي */
.countdown {
    display: flex;
    gap: 15px;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border: 2px solid #dee2e6;
}

.countdown-unit {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    min-width: 60px;
}

.countdown-number {
    font-size: 2rem;
    font-weight: bold;
    color: #495057;
    font-family: 'Courier New', monospace;
    background: linear-gradient(135deg, #007bff, #0056b3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.countdown-label {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* تحذيرات المؤقت */
.timer.warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffc107;
    color: #856404;
    animation: pulse 2s infinite;
}

.timer.critical {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #dc3545;
    color: #721c24;
    animation: blink 1s infinite;
}

/* تأثيرات الحركة */
@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0,-10px,0); }
    70% { transform: translate3d(0,-5px,0); }
    90% { transform: translate3d(0,-2px,0); }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .timer {
        padding: 5px 10px;
        font-size: 0.8rem;
        gap: 6px;
    }
    
    .timer-large {
        padding: 12px 16px;
        font-size: 1rem;
        min-width: 100px;
    }
    
    .timer-large .time-remaining {
        font-size: 1.2rem;
        min-width: 80px;
    }
    
    .timer-circular {
        width: 60px;
        height: 60px;
    }
    
    .timer-circular .timer-display {
        font-size: 0.8rem;
    }
    
    .timer-circular .timer-label {
        font-size: 0.6rem;
    }
    
    .countdown {
        gap: 10px;
        padding: 15px;
    }
    
    .countdown-unit {
        min-width: 50px;
    }
    
    .countdown-number {
        font-size: 1.5rem;
    }
    
    .countdown-label {
        font-size: 0.7rem;
    }
    
    .timers-group {
        gap: 8px;
    }
    
    .timer-item {
        padding: 10px 12px;
    }
    
    .timer-item .timer {
        margin-left: 10px;
    }
}

@media (max-width: 480px) {
    .timer {
        padding: 4px 8px;
        font-size: 0.75rem;
        gap: 4px;
    }
    
    .time-remaining,
    .timer-display {
        min-width: 50px;
        font-size: 0.75rem;
    }
    
    .timer-large {
        padding: 10px 12px;
        font-size: 0.9rem;
        min-width: 80px;
    }
    
    .timer-large .time-remaining {
        font-size: 1rem;
        min-width: 70px;
    }
    
    .timer-circular {
        width: 50px;
        height: 50px;
    }
    
    .timer-circular .timer-display {
        font-size: 0.7rem;
    }
    
    .timer-circular .timer-label {
        font-size: 0.5rem;
    }
    
    .countdown {
        gap: 8px;
        padding: 12px;
        flex-wrap: wrap;
    }
    
    .countdown-unit {
        min-width: 40px;
    }
    
    .countdown-number {
        font-size: 1.2rem;
    }
    
    .countdown-label {
        font-size: 0.6rem;
    }
    
    .timers-group {
        gap: 6px;
    }
    
    .timer-item {
        padding: 8px 10px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .timer-item .timer {
        margin-left: 0;
        align-self: flex-end;
    }
}

/* تحسينات للطباعة */
@media print {
    .timer,
    .countdown,
    .timer-progress {
        border: 1px solid #000;
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .timer::before,
    .timer-icon::before {
        display: none;
    }
    
    .timer-progress-bar::after {
        display: none;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .timer {
        background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
        border-color: #444;
        color: #fff;
    }
    
    .timer.building-timer {
        background: linear-gradient(135deg, #4d4d00 0%, #6d6d00 100%);
        border-color: #ffc107;
        color: #ffff6b;
    }
    
    .timer.training-timer {
        background: linear-gradient(135deg, #1f4d1f 0%, #2c6d2c 100%);
        border-color: #28a745;
        color: #6bff6b;
    }
    
    .timer.attack-timer {
        background: linear-gradient(135deg, #4d1f1f 0%, #6d2c2c 100%);
        border-color: #dc3545;
        color: #ff6b6b;
    }
    
    .timer.trade-timer {
        background: linear-gradient(135deg, #1f4d4d 0%, #2c6d6d 100%);
        border-color: #17a2b8;
        color: #6bffff;
    }
    
    .timer-item {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .timer-item:hover {
        border-color: #007bff;
    }
    
    .countdown {
        background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
        border-color: #444;
    }
    
    .countdown-number {
        color: #fff;
        background: linear-gradient(135deg, #007bff, #0056b3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .countdown-label {
        color: #ccc;
    }
    
    .timer-progress {
        background-color: #444;
    }
    
    .timer-circular::before {
        background-color: #2d2d2d;
    }
}
