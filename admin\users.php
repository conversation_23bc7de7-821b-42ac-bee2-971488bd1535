<?php
/**
 * إدارة المستخدمين
 * User Management
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
requireAdmin();

$db = getDB();
$action = $_GET['action'] ?? 'list';
$userId = $_GET['id'] ?? null;

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $postAction = $_POST['action'] ?? '';
    
    if ($postAction === 'ban_user' && $userId) {
        $reason = $_POST['reason'] ?? '';
        $duration = $_POST['duration'] ?? 0;
        
        $banUntil = $duration > 0 ? date('Y-m-d H:i:s', time() + ($duration * 3600)) : null;
        
        $db->update('users', [
            'is_banned' => 1,
            'ban_reason' => $reason,
            'ban_until' => $banUntil
        ], ['id' => $userId]);
        
        $message = 'تم حظر المستخدم بنجاح';
        
    } elseif ($postAction === 'unban_user' && $userId) {
        $db->update('users', [
            'is_banned' => 0,
            'ban_reason' => null,
            'ban_until' => null
        ], ['id' => $userId]);
        
        $message = 'تم إلغاء حظر المستخدم بنجاح';
        
    } elseif ($postAction === 'delete_user' && $userId) {
        // حذف بيانات المستخدم
        $db->delete('villages', ['user_id' => $userId]);
        $db->delete('attacks', ['attacker_user_id' => $userId]);
        $db->delete('attacks', ['defender_user_id' => $userId]);
        $db->delete('trades', ['sender_user_id' => $userId]);
        $db->delete('trades', ['receiver_user_id' => $userId]);
        $db->delete('reports', ['user_id' => $userId]);
        $db->delete('users', ['id' => $userId]);
        
        $message = 'تم حذف المستخدم وجميع بياناته بنجاح';
        $action = 'list';
        
    } elseif ($postAction === 'update_user' && $userId) {
        $updateData = [
            'username' => $_POST['username'],
            'email' => $_POST['email'],
            'role' => $_POST['role']
        ];
        
        if (!empty($_POST['password'])) {
            $updateData['password'] = password_hash($_POST['password'], PASSWORD_DEFAULT);
        }
        
        $db->update('users', $updateData, ['id' => $userId]);
        $message = 'تم تحديث بيانات المستخدم بنجاح';
    }
}

// جلب بيانات المستخدمين
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $filter = $_GET['filter'] ?? 'all';
    $page = max(1, $_GET['page'] ?? 1);
    $limit = 20;
    $offset = ($page - 1) * $limit;
    
    $whereConditions = [];
    $params = [];
    
    if ($search) {
        $whereConditions[] = "(username LIKE ? OR email LIKE ?)";
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
    }
    
    if ($filter === 'banned') {
        $whereConditions[] = "is_banned = 1";
    } elseif ($filter === 'active') {
        $whereConditions[] = "is_banned = 0 AND last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
    } elseif ($filter === 'inactive') {
        $whereConditions[] = "last_login < DATE_SUB(NOW(), INTERVAL 30 DAY) OR last_login IS NULL";
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    $users = $db->select("
        SELECT u.*, 
               COUNT(v.id) as village_count,
               COALESCE(SUM(v.population), 0) as total_population
        FROM users u
        LEFT JOIN villages v ON u.id = v.user_id
        {$whereClause}
        GROUP BY u.id
        ORDER BY u.created_at DESC
        LIMIT {$limit} OFFSET {$offset}
    ", $params);
    
    $totalUsers = $db->selectOne("
        SELECT COUNT(*) as count 
        FROM users u 
        {$whereClause}
    ", $params)['count'];
    
    $totalPages = ceil($totalUsers / $limit);
    
} elseif ($action === 'view' && $userId) {
    $user = $db->selectOne("SELECT * FROM users WHERE id = ?", [$userId]);
    
    if (!$user) {
        redirect('users.php');
    }
    
    // إحصائيات المستخدم
    $userStats = [
        'villages' => $db->select("SELECT * FROM villages WHERE user_id = ? ORDER BY created_at", [$userId]),
        'attacks_sent' => $db->selectOne("SELECT COUNT(*) as count FROM attacks WHERE attacker_user_id = ?", [$userId])['count'],
        'attacks_received' => $db->selectOne("SELECT COUNT(*) as count FROM attacks WHERE defender_user_id = ?", [$userId])['count'],
        'trades_sent' => $db->selectOne("SELECT COUNT(*) as count FROM trades WHERE sender_user_id = ?", [$userId])['count'],
        'trades_received' => $db->selectOne("SELECT COUNT(*) as count FROM trades WHERE receiver_user_id = ?", [$userId])['count'],
        'total_population' => $db->selectOne("SELECT COALESCE(SUM(population), 0) as total FROM villages WHERE user_id = ?", [$userId])['total']
    ];
    
    // آخر الأنشطة
    $recentActivities = $db->select("
        SELECT 'attack_sent' as activity_type, 
               CONCAT('هجوم على قرية ', dv.name, ' (', dv.x, '|', dv.y, ')') as description,
               a.created_at as activity_time
        FROM attacks a
        JOIN villages av ON a.attacker_village_id = av.id
        JOIN villages dv ON a.defender_village_id = dv.id
        WHERE a.attacker_user_id = ?
        
        UNION ALL
        
        SELECT 'trade_sent' as activity_type,
               CONCAT('إرسال تجارة إلى قرية ', rv.name, ' (', rv.x, '|', rv.y, ')') as description,
               t.created_at as activity_time
        FROM trades t
        JOIN villages rv ON t.receiver_village_id = rv.id
        WHERE t.sender_user_id = ?
        
        ORDER BY activity_time DESC
        LIMIT 20
    ", [$userId, $userId]);
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body class="admin-page">
    <?php include 'includes/admin-header.php'; ?>
    
    <div class="admin-container">
        <?php include 'includes/admin-sidebar.php'; ?>
        
        <main class="admin-content">
            <?php if (isset($message)): ?>
                <div class="alert alert-success"><?php echo $message; ?></div>
            <?php endif; ?>
            
            <?php if ($action === 'list'): ?>
                <div class="admin-header">
                    <h1>إدارة المستخدمين</h1>
                    <p>إدارة حسابات المستخدمين وصلاحياتهم</p>
                </div>
                
                <!-- أدوات البحث والتصفية -->
                <div class="admin-tools">
                    <form method="GET" class="search-form">
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="البحث بالاسم أو البريد الإلكتروني">
                        <select name="filter">
                            <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>جميع المستخدمين</option>
                            <option value="active" <?php echo $filter === 'active' ? 'selected' : ''; ?>>نشط</option>
                            <option value="inactive" <?php echo $filter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                            <option value="banned" <?php echo $filter === 'banned' ? 'selected' : ''; ?>>محظور</option>
                        </select>
                        <button type="submit" class="btn btn-primary">بحث</button>
                    </form>
                </div>
                
                <!-- جدول المستخدمين -->
                <div class="table-container">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>المعرف</th>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>عدد القرى</th>
                                <th>إجمالي السكان</th>
                                <th>آخر دخول</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?php echo $user['id']; ?></td>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td>
                                        <span class="role-badge <?php echo $user['role']; ?>">
                                            <?php echo $user['role'] === 'admin' ? 'مدير' : 'مستخدم'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo $user['village_count']; ?></td>
                                    <td><?php echo formatNumber($user['total_population']); ?></td>
                                    <td>
                                        <?php if ($user['last_login']): ?>
                                            <?php echo date('d/m/Y H:i', strtotime($user['last_login'])); ?>
                                        <?php else: ?>
                                            <span class="text-muted">لم يدخل بعد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($user['is_banned']): ?>
                                            <span class="status-badge banned">محظور</span>
                                        <?php elseif ($user['last_login'] && strtotime($user['last_login']) > strtotime('-7 days')): ?>
                                            <span class="status-badge active">نشط</span>
                                        <?php else: ?>
                                            <span class="status-badge inactive">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="?action=view&id=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary">عرض</a>
                                            <a href="?action=edit&id=<?php echo $user['id']; ?>" class="btn btn-sm btn-warning">تعديل</a>
                                            <?php if (!$user['is_banned']): ?>
                                                <a href="?action=ban&id=<?php echo $user['id']; ?>" class="btn btn-sm btn-danger">حظر</a>
                                            <?php else: ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="unban_user">
                                                    <button type="submit" class="btn btn-sm btn-success">إلغاء الحظر</button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- ترقيم الصفحات -->
                <?php if ($totalPages > 1): ?>
                    <div class="pagination">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo $filter; ?>" 
                               class="page-link <?php echo $i === $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                    </div>
                <?php endif; ?>
                
            <?php elseif ($action === 'view' && isset($user)): ?>
                <div class="admin-header">
                    <h1>تفاصيل المستخدم: <?php echo htmlspecialchars($user['username']); ?></h1>
                    <div class="header-actions">
                        <a href="users.php" class="btn btn-secondary">العودة للقائمة</a>
                        <a href="?action=edit&id=<?php echo $user['id']; ?>" class="btn btn-primary">تعديل</a>
                    </div>
                </div>
                
                <!-- معلومات المستخدم -->
                <div class="user-details">
                    <div class="user-info-card">
                        <h3>المعلومات الأساسية</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>المعرف:</label>
                                <span><?php echo $user['id']; ?></span>
                            </div>
                            <div class="info-item">
                                <label>اسم المستخدم:</label>
                                <span><?php echo htmlspecialchars($user['username']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>البريد الإلكتروني:</label>
                                <span><?php echo htmlspecialchars($user['email']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>الدور:</label>
                                <span class="role-badge <?php echo $user['role']; ?>">
                                    <?php echo $user['role'] === 'admin' ? 'مدير' : 'مستخدم'; ?>
                                </span>
                            </div>
                            <div class="info-item">
                                <label>تاريخ التسجيل:</label>
                                <span><?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></span>
                            </div>
                            <div class="info-item">
                                <label>آخر دخول:</label>
                                <span>
                                    <?php if ($user['last_login']): ?>
                                        <?php echo date('d/m/Y H:i', strtotime($user['last_login'])); ?>
                                    <?php else: ?>
                                        لم يدخل بعد
                                    <?php endif; ?>
                                </span>
                            </div>
                        </div>
                        
                        <?php if ($user['is_banned']): ?>
                            <div class="ban-info">
                                <h4>معلومات الحظر</h4>
                                <p><strong>السبب:</strong> <?php echo htmlspecialchars($user['ban_reason'] ?? 'غير محدد'); ?></p>
                                <?php if ($user['ban_until']): ?>
                                    <p><strong>حتى:</strong> <?php echo date('d/m/Y H:i', strtotime($user['ban_until'])); ?></p>
                                <?php else: ?>
                                    <p><strong>النوع:</strong> حظر دائم</p>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- إحصائيات المستخدم -->
                    <div class="user-stats-card">
                        <h3>الإحصائيات</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value"><?php echo count($userStats['villages']); ?></div>
                                <div class="stat-label">عدد القرى</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value"><?php echo formatNumber($userStats['total_population']); ?></div>
                                <div class="stat-label">إجمالي السكان</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value"><?php echo $userStats['attacks_sent']; ?></div>
                                <div class="stat-label">هجمات مرسلة</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value"><?php echo $userStats['attacks_received']; ?></div>
                                <div class="stat-label">هجمات مستقبلة</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value"><?php echo $userStats['trades_sent']; ?></div>
                                <div class="stat-label">تجارة مرسلة</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value"><?php echo $userStats['trades_received']; ?></div>
                                <div class="stat-label">تجارة مستقبلة</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- قرى المستخدم -->
                <?php if (!empty($userStats['villages'])): ?>
                    <div class="user-villages">
                        <h3>قرى المستخدم</h3>
                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>اسم القرية</th>
                                        <th>الإحداثيات</th>
                                        <th>السكان</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($userStats['villages'] as $village): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($village['name']); ?></td>
                                            <td>(<?php echo $village['x']; ?>|<?php echo $village['y']; ?>)</td>
                                            <td><?php echo formatNumber($village['population']); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($village['created_at'])); ?></td>
                                            <td>
                                                <a href="villages.php?action=view&id=<?php echo $village['id']; ?>" class="btn btn-sm btn-primary">عرض</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- الأنشطة الأخيرة -->
                <?php if (!empty($recentActivities)): ?>
                    <div class="user-activities">
                        <h3>الأنشطة الأخيرة</h3>
                        <div class="activities-list">
                            <?php foreach ($recentActivities as $activity): ?>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <?php if ($activity['activity_type'] === 'attack_sent'): ?>
                                            ⚔️
                                        <?php elseif ($activity['activity_type'] === 'trade_sent'): ?>
                                            🚚
                                        <?php endif; ?>
                                    </div>
                                    <div class="activity-details">
                                        <div class="activity-description"><?php echo htmlspecialchars($activity['description']); ?></div>
                                        <div class="activity-time"><?php echo date('d/m/Y H:i', strtotime($activity['activity_time'])); ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
            <?php endif; ?>
        </main>
    </div>
    
    <script src="../js/admin.js"></script>
</body>
</html>
