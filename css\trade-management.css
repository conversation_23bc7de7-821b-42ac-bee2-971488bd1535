/* ملف CSS لإدارة التجارة */
/* Trade Management CSS */

/* إحصائيات التجارة */
.trade-stats {
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-card.outgoing {
    border-color: #dc3545;
}

.stat-card.incoming {
    border-color: #28a745;
}

.stat-card.total {
    border-color: #007bff;
}

.stat-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0,0,0,0.1);
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #495057;
    line-height: 1;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* أقسام التجارة */
.trade-section {
    margin-bottom: 40px;
}

.trade-section h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

/* قائمة التجارة */
.trades-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.trade-item {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.trade-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.trade-item.outgoing {
    border-left: 5px solid #dc3545;
}

.trade-item.incoming {
    border-left: 5px solid #28a745;
}

.trade-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.trade-info {
    flex: 1;
}

.destination,
.source {
    margin-bottom: 5px;
}

.destination strong,
.source strong {
    color: #495057;
    font-size: 1.1rem;
}

.destination span,
.source span {
    color: #6c757d;
    font-size: 0.9rem;
    margin-right: 10px;
}

.trade-time {
    color: #6c757d;
    font-size: 0.9rem;
}

.countdown {
    color: #007bff;
    font-weight: 500;
}

.arrived {
    color: #28a745;
    font-weight: bold;
}

.trade-actions {
    display: flex;
    gap: 10px;
}

.trade-actions .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* موارد التجارة */
.trade-resources {
    margin-top: 15px;
}

.trade-resources h5 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 1rem;
}

.resources-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.resource {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 0.9rem;
    font-weight: 500;
}

.resource img {
    width: 20px;
    height: 20px;
}

.resource span {
    color: #495057;
}

/* عدم وجود تجارة */
.no-trades {
    text-align: center;
    padding: 40px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
}

.no-trades p {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 15px;
}

.no-trades .btn {
    padding: 10px 20px;
    font-weight: 600;
}

/* تاريخ التجارة */
.history-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.history-item {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.history-item:hover {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.history-item.completed {
    border-left: 4px solid #28a745;
}

.history-item.cancelled {
    border-left: 4px solid #dc3545;
    opacity: 0.8;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.trade-direction {
    display: flex;
    align-items: center;
    gap: 10px;
}

.direction {
    font-size: 0.8rem;
    font-weight: bold;
    padding: 3px 8px;
    border-radius: 4px;
}

.direction.outgoing {
    background-color: #f8d7da;
    color: #721c24;
}

.direction.incoming {
    background-color: #d4edda;
    color: #155724;
}

.partner {
    color: #495057;
    font-size: 0.9rem;
}

.trade-status {
    text-align: right;
}

.status {
    font-size: 0.8rem;
    font-weight: bold;
    padding: 3px 8px;
    border-radius: 4px;
    display: block;
    margin-bottom: 3px;
}

.status.completed {
    background-color: #d4edda;
    color: #155724;
}

.status.cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

.date {
    color: #6c757d;
    font-size: 0.8rem;
}

.history-resources {
    margin-top: 10px;
}

.resources-summary {
    color: #495057;
    font-size: 0.9rem;
    font-weight: 500;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-card {
        padding: 15px;
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .trade-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .trade-actions {
        align-self: flex-end;
    }
    
    .resources-list {
        flex-direction: column;
    }
    
    .resource {
        justify-content: space-between;
    }
    
    .history-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .trade-status {
        text-align: left;
        align-self: flex-end;
    }
    
    .trade-direction {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 12px;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .stat-value {
        font-size: 1.3rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
    
    .trade-item {
        padding: 15px;
    }
    
    .trade-resources h5 {
        font-size: 0.9rem;
    }
    
    .resource {
        padding: 4px 8px;
        font-size: 0.8rem;
    }
    
    .resource img {
        width: 16px;
        height: 16px;
    }
    
    .history-item {
        padding: 12px;
    }
    
    .direction {
        font-size: 0.7rem;
        padding: 2px 6px;
    }
    
    .partner {
        font-size: 0.8rem;
    }
    
    .status {
        font-size: 0.7rem;
        padding: 2px 6px;
    }
    
    .date {
        font-size: 0.7rem;
    }
    
    .resources-summary {
        font-size: 0.8rem;
        padding: 6px 10px;
    }
}

/* تحسينات للطباعة */
@media print {
    .trade-actions {
        display: none;
    }
    
    .trade-item,
    .history-item {
        border: 1px solid #000;
        margin-bottom: 15px;
        break-inside: avoid;
    }
    
    .stat-card {
        border: 1px solid #000;
    }
    
    .no-trades {
        display: none;
    }
}

/* تأثيرات الحركة */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.trade-item,
.history-item {
    animation: slideIn 0.3s ease-out;
}

.countdown {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* تحسينات للوضع المظلم (إذا تم تطبيقه لاحقاً) */
@media (prefers-color-scheme: dark) {
    .trade-item,
    .history-item,
    .stat-card {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .resource,
    .resources-summary {
        background-color: #3d3d3d;
        border-color: #555;
        color: #fff;
    }
    
    .no-trades {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .direction.outgoing {
        background-color: #4d1f1f;
        color: #ff6b6b;
    }
    
    .direction.incoming {
        background-color: #1f4d1f;
        color: #6bff6b;
    }
    
    .status.completed {
        background-color: #1f4d1f;
        color: #6bff6b;
    }
    
    .status.cancelled {
        background-color: #4d1f1f;
        color: #ff6b6b;
    }
}
