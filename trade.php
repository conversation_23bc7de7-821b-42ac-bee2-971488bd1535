<?php
/**
 * صفحة التجارة المباشرة
 * Direct Trade Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];
$villageId = $_SESSION['current_village_id'];

// التحقق من القرية
$village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$villageId, $userId]);
if (!$village) {
    redirect('index.php');
}

// التحقق من وجود السوق
$marketplace = $db->selectOne("SELECT * FROM buildings WHERE village_id = ? AND building_type = 'marketplace'", [$villageId]);
if (!$marketplace || $marketplace['level'] < 1) {
    showError('يجب بناء السوق أولاً');
    redirect('buildings.php');
}

// الحصول على القرية المستهدفة
$targetVillageId = intval($_GET['target'] ?? 0);
$targetVillage = null;

if ($targetVillageId > 0) {
    $targetVillage = $db->selectOne("SELECT v.*, u.username FROM villages v JOIN users u ON v.user_id = u.id WHERE v.id = ?", [$targetVillageId]);
    
    if (!$targetVillage) {
        showError('القرية المستهدفة غير موجودة');
        redirect('map.php');
    }
    
    if ($targetVillage['user_id'] == $userId) {
        showError('لا يمكنك التجارة مع نفسك');
        redirect('map.php');
    }
}

// تحديث الموارد
updateVillageResources($villageId);

// الحصول على الموارد
$resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);

// حساب المسافة ووقت السفر
$distance = 0;
$travelTime = 0;
$merchantCapacity = 1000; // سعة التاجر الواحد

if ($targetVillage) {
    $distance = sqrt(pow($targetVillage['x_coordinate'] - $village['x_coordinate'], 2) + 
                    pow($targetVillage['y_coordinate'] - $village['y_coordinate'], 2));
    $travelTime = calculateTravelTime($distance, 'merchant');
}

// معالجة إرسال التجارة
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['send_trade'])) {
    $sendWood = intval($_POST['send_wood'] ?? 0);
    $sendClay = intval($_POST['send_clay'] ?? 0);
    $sendIron = intval($_POST['send_iron'] ?? 0);
    $sendCrop = intval($_POST['send_crop'] ?? 0);
    $csrf = $_POST['csrf_token'] ?? '';
    
    if (!validateCSRF($csrf)) {
        $error = 'رمز الأمان غير صحيح';
    } elseif (!$targetVillage) {
        $error = 'يجب تحديد قرية مستهدفة';
    } elseif ($sendWood + $sendClay + $sendIron + $sendCrop <= 0) {
        $error = 'يجب تحديد موارد للإرسال';
    } elseif ($resources['wood'] < $sendWood || 
              $resources['clay'] < $sendClay || 
              $resources['iron'] < $sendIron || 
              $resources['crop'] < $sendCrop) {
        $error = 'الموارد المتاحة غير كافية';
    } else {
        // حساب عدد التجار المطلوبين
        $totalResources = $sendWood + $sendClay + $sendIron + $sendCrop;
        $merchantsNeeded = ceil($totalResources / $merchantCapacity);
        
        // التحقق من توفر التجار
        $availableMerchants = getMerchantCapacity($marketplace['level']) - getTotalMerchants($villageId);
        
        if ($merchantsNeeded > $availableMerchants) {
            $error = "تحتاج إلى {$merchantsNeeded} تاجر ولكن لديك {$availableMerchants} فقط";
        } else {
            // إرسال التجارة
            $arrivalTime = date('Y-m-d H:i:s', time() + $travelTime);
            
            $db->beginTransaction();
            try {
                // خصم الموارد
                $db->update("UPDATE resources SET wood = wood - ?, clay = clay - ?, iron = iron - ?, crop = crop - ? WHERE village_id = ?", 
                           [$sendWood, $sendClay, $sendIron, $sendCrop, $villageId]);
                
                // إنشاء سجل التجارة
                $db->insert("INSERT INTO trades (sender_village_id, receiver_village_id, wood_amount, clay_amount, iron_amount, crop_amount, arrival_time) VALUES (?, ?, ?, ?, ?, ?, ?)", 
                           [$villageId, $targetVillageId, $sendWood, $sendClay, $sendIron, $sendCrop, $arrivalTime]);
                
                $db->commit();
                $success = "تم إرسال التجارة بنجاح! ستصل في " . date('d/m/Y H:i', strtotime($arrivalTime));
                
                // تحديث البيانات
                $resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);
                
            } catch (Exception $e) {
                $db->rollback();
                $error = 'حدث خطأ أثناء إرسال التجارة';
            }
        }
    }
}

$csrfToken = generateCSRF();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التجارة المباشرة - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/trade.css">
</head>
<body class="game-page">
    <?php include 'includes/header.php'; ?>
    
    <div class="main-content">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="content">
            <div class="page-header">
                <h1>التجارة المباشرة</h1>
                <p>إرسال الموارد إلى القرى الأخرى</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <!-- اختيار الهدف -->
            <?php if (!$targetVillage): ?>
                <div class="target-selection">
                    <h3>اختيار الهدف</h3>
                    <p>يجب تحديد قرية مستهدفة للتجارة. يمكنك العثور على القرى من خلال:</p>
                    <div class="selection-options">
                        <a href="map.php" class="btn btn-primary">الخريطة</a>
                        <a href="alliance.php" class="btn btn-secondary">التحالف</a>
                        <a href="friends.php" class="btn btn-secondary">الأصدقاء</a>
                    </div>
                </div>
            <?php else: ?>
                
                <!-- معلومات التجارة -->
                <div class="trade-info">
                    <div class="info-grid">
                        <div class="info-card source">
                            <h3>🏠 قريتي</h3>
                            <div class="village-details">
                                <div class="village-name"><?php echo htmlspecialchars($village['name']); ?></div>
                                <div class="coordinates">(<?php echo $village['x_coordinate']; ?>|<?php echo $village['y_coordinate']; ?>)</div>
                                <div class="resources">
                                    <div class="resource">
                                        <img src="images/resources/wood.png" alt="خشب">
                                        <span><?php echo formatNumber($resources['wood']); ?></span>
                                    </div>
                                    <div class="resource">
                                        <img src="images/resources/clay.png" alt="طين">
                                        <span><?php echo formatNumber($resources['clay']); ?></span>
                                    </div>
                                    <div class="resource">
                                        <img src="images/resources/iron.png" alt="حديد">
                                        <span><?php echo formatNumber($resources['iron']); ?></span>
                                    </div>
                                    <div class="resource">
                                        <img src="images/resources/crop.png" alt="قمح">
                                        <span><?php echo formatNumber($resources['crop']); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="trade-arrow">
                            <div class="arrow-icon">➡️</div>
                            <div class="trade-details">
                                <div class="distance">المسافة: <?php echo number_format($distance, 1); ?></div>
                                <div class="travel-time">وقت السفر: <?php echo formatDuration($travelTime); ?></div>
                            </div>
                        </div>
                        
                        <div class="info-card target">
                            <h3>🎯 الهدف</h3>
                            <div class="village-details">
                                <div class="village-name"><?php echo htmlspecialchars($targetVillage['name']); ?></div>
                                <div class="player-name"><?php echo htmlspecialchars($targetVillage['username']); ?></div>
                                <div class="coordinates">(<?php echo $targetVillage['x_coordinate']; ?>|<?php echo $targetVillage['y_coordinate']; ?>)</div>
                                <div class="population">السكان: <?php echo formatNumber($targetVillage['population']); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- نموذج التجارة -->
                <div class="trade-form-container">
                    <h3>إرسال الموارد</h3>
                    
                    <form method="POST" class="trade-form">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        
                        <div class="resources-section">
                            <h4>الموارد المراد إرسالها:</h4>
                            <div class="resources-grid">
                                <div class="resource-input">
                                    <img src="images/resources/wood.png" alt="خشب">
                                    <label for="send_wood">خشب</label>
                                    <input type="number" id="send_wood" name="send_wood" 
                                           min="0" max="<?php echo $resources['wood']; ?>" value="0"
                                           onchange="updateTradeCalculations()">
                                    <span class="max-available">الحد الأقصى: <?php echo formatNumber($resources['wood']); ?></span>
                                </div>
                                
                                <div class="resource-input">
                                    <img src="images/resources/clay.png" alt="طين">
                                    <label for="send_clay">طين</label>
                                    <input type="number" id="send_clay" name="send_clay" 
                                           min="0" max="<?php echo $resources['clay']; ?>" value="0"
                                           onchange="updateTradeCalculations()">
                                    <span class="max-available">الحد الأقصى: <?php echo formatNumber($resources['clay']); ?></span>
                                </div>
                                
                                <div class="resource-input">
                                    <img src="images/resources/iron.png" alt="حديد">
                                    <label for="send_iron">حديد</label>
                                    <input type="number" id="send_iron" name="send_iron" 
                                           min="0" max="<?php echo $resources['iron']; ?>" value="0"
                                           onchange="updateTradeCalculations()">
                                    <span class="max-available">الحد الأقصى: <?php echo formatNumber($resources['iron']); ?></span>
                                </div>
                                
                                <div class="resource-input">
                                    <img src="images/resources/crop.png" alt="قمح">
                                    <label for="send_crop">قمح</label>
                                    <input type="number" id="send_crop" name="send_crop" 
                                           min="0" max="<?php echo $resources['crop']; ?>" value="0"
                                           onchange="updateTradeCalculations()">
                                    <span class="max-available">الحد الأقصى: <?php echo formatNumber($resources['crop']); ?></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- حاسبة التجارة -->
                        <div class="trade-calculator">
                            <h4>تفاصيل التجارة</h4>
                            <div class="calculation-grid">
                                <div class="calc-item">
                                    <span class="label">إجمالي الموارد:</span>
                                    <span class="value" id="total-resources">0</span>
                                </div>
                                <div class="calc-item">
                                    <span class="label">التجار المطلوبون:</span>
                                    <span class="value" id="merchants-needed">0</span>
                                </div>
                                <div class="calc-item">
                                    <span class="label">التجار المتاحون:</span>
                                    <span class="value"><?php echo getMerchantCapacity($marketplace['level']) - getTotalMerchants($villageId); ?></span>
                                </div>
                                <div class="calc-item">
                                    <span class="label">وقت الوصول:</span>
                                    <span class="value" id="arrival-time">-</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="form-actions">
                            <button type="submit" name="send_trade" class="btn btn-primary" id="send-btn" disabled>
                                إرسال التجارة
                            </button>
                            <button type="button" onclick="clearForm()" class="btn btn-secondary">مسح</button>
                            <a href="map.php" class="btn btn-outline">تغيير الهدف</a>
                        </div>
                    </form>
                </div>
                
                <!-- نصائح التجارة -->
                <div class="trade-tips">
                    <h4>💡 نصائح التجارة</h4>
                    <ul>
                        <li>كل تاجر يمكنه حمل <?php echo formatNumber($merchantCapacity); ?> وحدة موارد</li>
                        <li>وقت السفر يعتمد على المسافة بين القريتين</li>
                        <li>التجار سيعودون تلقائياً بعد تسليم الموارد</li>
                        <li>يمكنك إلغاء التجارة قبل وصولها للهدف</li>
                        <li>ترقية السوق تزيد من عدد التجار المتاحين</li>
                    </ul>
                </div>
                
            <?php endif; ?>
        </main>
    </div>
    
    <script>
        const merchantCapacity = <?php echo $merchantCapacity; ?>;
        const travelTime = <?php echo $travelTime; ?>;
        
        function updateTradeCalculations() {
            const wood = parseInt(document.getElementById('send_wood').value) || 0;
            const clay = parseInt(document.getElementById('send_clay').value) || 0;
            const iron = parseInt(document.getElementById('send_iron').value) || 0;
            const crop = parseInt(document.getElementById('send_crop').value) || 0;
            
            const totalResources = wood + clay + iron + crop;
            const merchantsNeeded = Math.ceil(totalResources / merchantCapacity);
            
            document.getElementById('total-resources').textContent = formatNumber(totalResources);
            document.getElementById('merchants-needed').textContent = merchantsNeeded;
            
            if (totalResources > 0) {
                const arrivalTime = new Date(Date.now() + (travelTime * 1000));
                document.getElementById('arrival-time').textContent = arrivalTime.toLocaleString('ar-SA');
                document.getElementById('send-btn').disabled = false;
            } else {
                document.getElementById('arrival-time').textContent = '-';
                document.getElementById('send-btn').disabled = true;
            }
        }
        
        function clearForm() {
            document.querySelectorAll('.trade-form input[type="number"]').forEach(input => {
                input.value = 0;
            });
            updateTradeCalculations();
        }
        
        function formatNumber(num) {
            return new Intl.NumberFormat('ar-SA').format(num);
        }
        
        // تحديث الحسابات عند تحميل الصفحة
        updateTradeCalculations();
    </script>
</body>
</html>

<?php
function calculateTravelTime($distance, $unitType = 'merchant') {
    // سرعة التاجر: 16 مربع/ساعة
    $speed = 16;
    $timeInHours = $distance / $speed;
    return ceil($timeInHours * 3600 * BUILDING_TIME_FACTOR); // تحويل إلى ثواني
}

function getMerchantCapacity($marketplaceLevel) {
    return $marketplaceLevel * 2; // 2 تاجر لكل مستوى
}

function getTotalMerchants($villageId) {
    // حساب عدد التجار المستخدمين حالياً
    // هذا مثال مبسط - في التطبيق الحقيقي ستحتاج لحساب التجار في الطريق
    return 0;
}
?>
