-- قاعدة بيانات اللعبة الزراعية
-- Agricultural Game Database

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS u302460181_hoasb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE u302460181_hoasb;

-- جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    gold INT DEFAULT 100,
    premium_until TIMESTAMP NULL,
    tribe_id INT DEFAULT 1,
    population INT DEFAULT 2,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول القرى
CREATE TABLE villages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    x_coordinate INT NOT NULL,
    y_coordinate INT NOT NULL,
    population INT DEFAULT 2,
    is_capital BOOLEAN DEFAULT FALSE,
    loyalty INT DEFAULT 100,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_coordinates (x_coordinate, y_coordinate)
);

-- جدول المباني
CREATE TABLE buildings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    village_id INT NOT NULL,
    building_type ENUM('main_building', 'barracks', 'stable', 'workshop', 'academy', 'smithy', 'rally_point', 'marketplace', 'embassy', 'residence', 'palace', 'treasury', 'trade_office', 'great_barracks', 'great_stable', 'city_wall', 'earth_wall', 'palisade', 'stonemason', 'brewery', 'granary', 'warehouse', 'cranny', 'trapper', 'hero_mansion', 'wonder') NOT NULL,
    level INT DEFAULT 0,
    is_upgrading BOOLEAN DEFAULT FALSE,
    upgrade_finish_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (village_id) REFERENCES villages(id) ON DELETE CASCADE,
    UNIQUE KEY unique_building_per_village (village_id, building_type)
);

-- جدول الحقول (الموارد)
CREATE TABLE fields (
    id INT AUTO_INCREMENT PRIMARY KEY,
    village_id INT NOT NULL,
    field_type ENUM('wood', 'clay', 'iron', 'crop') NOT NULL,
    level INT DEFAULT 0,
    position INT NOT NULL,
    is_upgrading BOOLEAN DEFAULT FALSE,
    upgrade_finish_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (village_id) REFERENCES villages(id) ON DELETE CASCADE,
    UNIQUE KEY unique_field_position (village_id, position)
);

-- جدول الموارد
CREATE TABLE resources (
    id INT AUTO_INCREMENT PRIMARY KEY,
    village_id INT NOT NULL,
    wood INT DEFAULT 750,
    clay INT DEFAULT 750,
    iron INT DEFAULT 750,
    crop INT DEFAULT 750,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (village_id) REFERENCES villages(id) ON DELETE CASCADE,
    UNIQUE KEY unique_village_resources (village_id)
);

-- جدول الوحدات العسكرية
CREATE TABLE units (
    id INT AUTO_INCREMENT PRIMARY KEY,
    village_id INT NOT NULL,
    unit_type ENUM('phalanx', 'swordsman', 'pathfinder', 'theutates_thunder', 'druidrider', 'haeduan', 'ram', 'trebuchet', 'chieftain', 'settler') NOT NULL,
    count INT DEFAULT 0,
    is_training BOOLEAN DEFAULT FALSE,
    training_finish_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (village_id) REFERENCES villages(id) ON DELETE CASCADE
);

-- جدول الهجمات
CREATE TABLE attacks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    attacker_village_id INT NOT NULL,
    target_village_id INT NOT NULL,
    attack_type ENUM('normal', 'raid', 'siege') NOT NULL,
    units_sent TEXT NOT NULL, -- JSON format
    arrival_time TIMESTAMP NOT NULL,
    return_time TIMESTAMP NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    battle_report TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (attacker_village_id) REFERENCES villages(id) ON DELETE CASCADE,
    FOREIGN KEY (target_village_id) REFERENCES villages(id) ON DELETE CASCADE
);

-- جدول التجارة
CREATE TABLE trades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_village_id INT NOT NULL,
    receiver_village_id INT NOT NULL,
    wood_amount INT DEFAULT 0,
    clay_amount INT DEFAULT 0,
    iron_amount INT DEFAULT 0,
    crop_amount INT DEFAULT 0,
    arrival_time TIMESTAMP NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_village_id) REFERENCES villages(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_village_id) REFERENCES villages(id) ON DELETE CASCADE
);

-- جدول الرسائل
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT NULL,
    receiver_id INT NOT NULL,
    subject VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    message_type ENUM('normal', 'system', 'battle_report', 'trade_report') DEFAULT 'normal',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول التحالفات
CREATE TABLE alliances (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    tag VARCHAR(10) UNIQUE NOT NULL,
    description TEXT,
    leader_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (leader_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول عضوية التحالفات
CREATE TABLE alliance_members (
    id INT AUTO_INCREMENT PRIMARY KEY,
    alliance_id INT NOT NULL,
    user_id INT NOT NULL,
    role ENUM('member', 'diplomat', 'leader') DEFAULT 'member',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (alliance_id) REFERENCES alliances(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_alliance (user_id)
);

-- جدول الأبطال
CREATE TABLE heroes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    level INT DEFAULT 1,
    experience INT DEFAULT 0,
    health INT DEFAULT 100,
    attack_power INT DEFAULT 0,
    attack_cavalry INT DEFAULT 0,
    attack_infantry INT DEFAULT 0,
    defense_power INT DEFAULT 0,
    defense_cavalry INT DEFAULT 0,
    defense_infantry INT DEFAULT 0,
    speed INT DEFAULT 7,
    resource_production INT DEFAULT 0,
    current_village_id INT NULL,
    is_alive BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (current_village_id) REFERENCES villages(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_hero (user_id)
);

-- جدول السوق
CREATE TABLE marketplace_offers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    village_id INT NOT NULL,
    resource_offered ENUM('wood', 'clay', 'iron', 'crop') NOT NULL,
    amount_offered INT NOT NULL,
    resource_wanted ENUM('wood', 'clay', 'iron', 'crop') NOT NULL,
    amount_wanted INT NOT NULL,
    max_times INT DEFAULT 1,
    times_used INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    FOREIGN KEY (village_id) REFERENCES villages(id) ON DELETE CASCADE
);

-- جدول إعدادات اللعبة
CREATE TABLE game_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_name VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج الإعدادات الافتراضية
INSERT INTO game_settings (setting_name, setting_value, description) VALUES
('game_speed', '1', 'سرعة اللعبة (مضاعف الوقت)'),
('max_villages_per_user', '10', 'أقصى عدد قرى لكل مستخدم'),
('protection_time', '72', 'وقت الحماية للاعبين الجدد (بالساعات)'),
('resource_production_base', '30', 'الإنتاج الأساسي للموارد في الساعة'),
('population_per_crop', '1', 'عدد السكان لكل وحدة قمح'),
('building_time_factor', '1', 'مضاعف وقت البناء'),
('training_time_factor', '1', 'مضاعف وقت التدريب'),
('travel_speed', '1', 'مضاعف سرعة السفر');

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_villages_coordinates ON villages(x_coordinate, y_coordinate);
CREATE INDEX idx_villages_user ON villages(user_id);
CREATE INDEX idx_buildings_village ON buildings(village_id);
CREATE INDEX idx_fields_village ON fields(village_id);
CREATE INDEX idx_units_village ON units(village_id);
CREATE INDEX idx_attacks_target ON attacks(target_village_id);
CREATE INDEX idx_attacks_attacker ON attacks(attacker_village_id);
CREATE INDEX idx_messages_receiver ON messages(receiver_id);
CREATE INDEX idx_trades_sender ON trades(sender_village_id);
CREATE INDEX idx_trades_receiver ON trades(receiver_village_id);
