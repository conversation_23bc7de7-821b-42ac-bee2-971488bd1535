<?php
/**
 * إدارة القرى
 * Villages Management
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
requireAdmin();

$db = getDB();
$action = $_GET['action'] ?? 'list';
$villageId = $_GET['id'] ?? null;

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $postAction = $_POST['action'] ?? '';
    
    if ($postAction === 'delete_village' && $villageId) {
        // حذف القرية وجميع بياناتها
        $db->delete('village_buildings', ['village_id' => $villageId]);
        $db->delete('village_units', ['village_id' => $villageId]);
        $db->delete('attacks', ['attacker_village_id' => $villageId]);
        $db->delete('attacks', ['defender_village_id' => $villageId]);
        $db->delete('trades', ['sender_village_id' => $villageId]);
        $db->delete('trades', ['receiver_village_id' => $villageId]);
        $db->delete('villages', ['id' => $villageId]);
        
        $message = 'تم حذف القرية وجميع بياناتها بنجاح';
        $action = 'list';
        
    } elseif ($postAction === 'update_village' && $villageId) {
        $updateData = [
            'name' => $_POST['name'],
            'x' => $_POST['x'],
            'y' => $_POST['y'],
            'population' => $_POST['population'],
            'wood' => $_POST['wood'],
            'clay' => $_POST['clay'],
            'iron' => $_POST['iron'],
            'crop' => $_POST['crop']
        ];
        
        $db->update('villages', $updateData, ['id' => $villageId]);
        $message = 'تم تحديث بيانات القرية بنجاح';
        
    } elseif ($postAction === 'reset_village' && $villageId) {
        // إعادة تعيين القرية للحالة الأولى
        $db->update('villages', [
            'population' => 100,
            'wood' => 1000,
            'clay' => 1000,
            'iron' => 1000,
            'crop' => 1000
        ], ['id' => $villageId]);
        
        // حذف جميع المباني والوحدات
        $db->delete('village_buildings', ['village_id' => $villageId]);
        $db->delete('village_units', ['village_id' => $villageId]);
        
        $message = 'تم إعادة تعيين القرية بنجاح';
    }
}

// جلب بيانات القرى
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $filter = $_GET['filter'] ?? 'all';
    $page = max(1, $_GET['page'] ?? 1);
    $limit = 20;
    $offset = ($page - 1) * $limit;
    
    $whereConditions = [];
    $params = [];
    
    if ($search) {
        $whereConditions[] = "(v.name LIKE ? OR u.username LIKE ?)";
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
    }
    
    if ($filter === 'large') {
        $whereConditions[] = "v.population >= 1000";
    } elseif ($filter === 'small') {
        $whereConditions[] = "v.population < 500";
    } elseif ($filter === 'abandoned') {
        $whereConditions[] = "u.last_login < DATE_SUB(NOW(), INTERVAL 30 DAY)";
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    $villages = $db->select("
        SELECT v.*, u.username, u.last_login,
               COALESCE(SUM(CASE WHEN vb.building_type = 'main_building' THEN vb.level ELSE 0 END), 0) as main_building_level,
               COUNT(vu.id) as total_units
        FROM villages v
        LEFT JOIN users u ON v.user_id = u.id
        LEFT JOIN village_buildings vb ON v.id = vb.village_id
        LEFT JOIN village_units vu ON v.id = vu.village_id
        {$whereClause}
        GROUP BY v.id
        ORDER BY v.population DESC
        LIMIT {$limit} OFFSET {$offset}
    ", $params);
    
    $totalVillages = $db->selectOne("
        SELECT COUNT(*) as count 
        FROM villages v 
        LEFT JOIN users u ON v.user_id = u.id
        {$whereClause}
    ", $params)['count'];
    
    $totalPages = ceil($totalVillages / $limit);
    
} elseif ($action === 'view' && $villageId) {
    $village = $db->selectOne("
        SELECT v.*, u.username, u.email, u.last_login
        FROM villages v
        LEFT JOIN users u ON v.user_id = u.id
        WHERE v.id = ?
    ", [$villageId]);
    
    if (!$village) {
        redirect('villages.php');
    }
    
    // مباني القرية
    $buildings = $db->select("
        SELECT vb.*, bt.name as building_name, bt.description
        FROM village_buildings vb
        JOIN building_types bt ON vb.building_type = bt.type
        WHERE vb.village_id = ?
        ORDER BY vb.level DESC
    ", [$villageId]);
    
    // وحدات القرية
    $units = $db->select("
        SELECT vu.*, ut.name as unit_name, ut.attack, ut.defense_infantry, ut.defense_cavalry
        FROM village_units vu
        JOIN unit_types ut ON vu.unit_type = ut.type
        WHERE vu.village_id = ?
        ORDER BY vu.count DESC
    ", [$villageId]);
    
    // إحصائيات القرية
    $villageStats = [
        'total_buildings' => count($buildings),
        'total_units' => array_sum(array_column($units, 'count')),
        'attacks_sent' => $db->selectOne("SELECT COUNT(*) as count FROM attacks WHERE attacker_village_id = ?", [$villageId])['count'],
        'attacks_received' => $db->selectOne("SELECT COUNT(*) as count FROM attacks WHERE defender_village_id = ?", [$villageId])['count'],
        'trades_sent' => $db->selectOne("SELECT COUNT(*) as count FROM trades WHERE sender_village_id = ?", [$villageId])['count'],
        'trades_received' => $db->selectOne("SELECT COUNT(*) as count FROM trades WHERE receiver_village_id = ?", [$villageId])['count']
    ];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة القرى - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body class="admin-page">
    <?php include 'includes/admin-header.php'; ?>
    
    <div class="admin-container">
        <?php include 'includes/admin-sidebar.php'; ?>
        
        <main class="admin-content">
            <?php if (isset($message)): ?>
                <div class="alert alert-success"><?php echo $message; ?></div>
            <?php endif; ?>
            
            <?php if ($action === 'list'): ?>
                <div class="admin-header">
                    <h1>إدارة القرى</h1>
                    <p>إدارة جميع القرى في اللعبة</p>
                </div>
                
                <!-- أدوات البحث والتصفية -->
                <div class="admin-tools">
                    <form method="GET" class="search-form">
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="البحث باسم القرية أو المالك">
                        <select name="filter">
                            <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>جميع القرى</option>
                            <option value="large" <?php echo $filter === 'large' ? 'selected' : ''; ?>>قرى كبيرة (1000+ نسمة)</option>
                            <option value="small" <?php echo $filter === 'small' ? 'selected' : ''; ?>>قرى صغيرة (أقل من 500)</option>
                            <option value="abandoned" <?php echo $filter === 'abandoned' ? 'selected' : ''; ?>>قرى مهجورة</option>
                        </select>
                        <button type="submit" class="btn btn-primary">بحث</button>
                    </form>
                    
                    <div class="tools-actions">
                        <button class="btn btn-secondary" onclick="exportData('villages')">تصدير البيانات</button>
                        <button class="btn btn-warning" onclick="generateMap()">إنشاء خريطة</button>
                    </div>
                </div>
                
                <!-- إحصائيات سريعة -->
                <div class="quick-stats">
                    <div class="stat-item">
                        <span class="stat-label">إجمالي القرى:</span>
                        <span class="stat-value"><?php echo formatNumber($totalVillages); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">متوسط السكان:</span>
                        <span class="stat-value">
                            <?php 
                            $avgPop = $db->selectOne("SELECT AVG(population) as avg FROM villages")['avg'];
                            echo formatNumber(round($avgPop));
                            ?>
                        </span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">أكبر قرية:</span>
                        <span class="stat-value">
                            <?php 
                            $maxPop = $db->selectOne("SELECT MAX(population) as max FROM villages")['max'];
                            echo formatNumber($maxPop);
                            ?>
                        </span>
                    </div>
                </div>
                
                <!-- جدول القرى -->
                <div class="table-container">
                    <table class="admin-table sortable-table">
                        <thead>
                            <tr>
                                <th>المعرف</th>
                                <th>اسم القرية</th>
                                <th>المالك</th>
                                <th>الإحداثيات</th>
                                <th>السكان</th>
                                <th>المبنى الرئيسي</th>
                                <th>الوحدات</th>
                                <th>آخر نشاط</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($villages as $village): ?>
                                <tr>
                                    <td><?php echo $village['id']; ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($village['name']); ?></strong>
                                    </td>
                                    <td>
                                        <?php if ($village['username']): ?>
                                            <a href="users.php?action=view&id=<?php echo $village['user_id']; ?>">
                                                <?php echo htmlspecialchars($village['username']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>(<?php echo $village['x']; ?>|<?php echo $village['y']; ?>)</td>
                                    <td>
                                        <span class="population-badge <?php echo $village['population'] >= 1000 ? 'large' : ($village['population'] < 500 ? 'small' : 'medium'); ?>">
                                            <?php echo formatNumber($village['population']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($village['main_building_level'] > 0): ?>
                                            المستوى <?php echo $village['main_building_level']; ?>
                                        <?php else: ?>
                                            <span class="text-muted">غير مبني</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo formatNumber($village['total_units']); ?></td>
                                    <td>
                                        <?php if ($village['last_login']): ?>
                                            <?php 
                                            $lastLogin = strtotime($village['last_login']);
                                            $daysSince = floor((time() - $lastLogin) / 86400);
                                            ?>
                                            <span class="activity-badge <?php echo $daysSince <= 1 ? 'active' : ($daysSince <= 7 ? 'recent' : 'inactive'); ?>">
                                                <?php 
                                                if ($daysSince == 0) {
                                                    echo 'اليوم';
                                                } elseif ($daysSince == 1) {
                                                    echo 'أمس';
                                                } else {
                                                    echo "منذ {$daysSince} يوم";
                                                }
                                                ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">لم يدخل بعد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="?action=view&id=<?php echo $village['id']; ?>" class="btn btn-sm btn-primary">عرض</a>
                                            <a href="?action=edit&id=<?php echo $village['id']; ?>" class="btn btn-sm btn-warning">تعديل</a>
                                            <button class="btn btn-sm btn-info" onclick="resetVillage(<?php echo $village['id']; ?>)">إعادة تعيين</button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteVillage(<?php echo $village['id']; ?>)">حذف</button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- ترقيم الصفحات -->
                <?php if ($totalPages > 1): ?>
                    <div class="pagination">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo $filter; ?>" 
                               class="page-link <?php echo $i === $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                    </div>
                <?php endif; ?>
                
            <?php elseif ($action === 'view' && isset($village)): ?>
                <div class="admin-header">
                    <h1>تفاصيل القرية: <?php echo htmlspecialchars($village['name']); ?></h1>
                    <div class="header-actions">
                        <a href="villages.php" class="btn btn-secondary">العودة للقائمة</a>
                        <a href="?action=edit&id=<?php echo $village['id']; ?>" class="btn btn-primary">تعديل</a>
                    </div>
                </div>
                
                <!-- معلومات القرية -->
                <div class="village-details">
                    <div class="village-info-card">
                        <h3>المعلومات الأساسية</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>المعرف:</label>
                                <span><?php echo $village['id']; ?></span>
                            </div>
                            <div class="info-item">
                                <label>اسم القرية:</label>
                                <span><?php echo htmlspecialchars($village['name']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>المالك:</label>
                                <span>
                                    <?php if ($village['username']): ?>
                                        <a href="users.php?action=view&id=<?php echo $village['user_id']; ?>">
                                            <?php echo htmlspecialchars($village['username']); ?>
                                        </a>
                                    <?php else: ?>
                                        غير محدد
                                    <?php endif; ?>
                                </span>
                            </div>
                            <div class="info-item">
                                <label>الإحداثيات:</label>
                                <span>(<?php echo $village['x']; ?>|<?php echo $village['y']; ?>)</span>
                            </div>
                            <div class="info-item">
                                <label>السكان:</label>
                                <span><?php echo formatNumber($village['population']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>تاريخ الإنشاء:</label>
                                <span><?php echo date('d/m/Y H:i', strtotime($village['created_at'])); ?></span>
                            </div>
                        </div>
                        
                        <!-- الموارد -->
                        <div class="resources-info">
                            <h4>الموارد</h4>
                            <div class="resources-grid">
                                <div class="resource-item">
                                    <img src="../images/resources/wood.png" alt="خشب">
                                    <span class="resource-name">خشب</span>
                                    <span class="resource-amount"><?php echo formatNumber($village['wood']); ?></span>
                                </div>
                                <div class="resource-item">
                                    <img src="../images/resources/clay.png" alt="طين">
                                    <span class="resource-name">طين</span>
                                    <span class="resource-amount"><?php echo formatNumber($village['clay']); ?></span>
                                </div>
                                <div class="resource-item">
                                    <img src="../images/resources/iron.png" alt="حديد">
                                    <span class="resource-name">حديد</span>
                                    <span class="resource-amount"><?php echo formatNumber($village['iron']); ?></span>
                                </div>
                                <div class="resource-item">
                                    <img src="../images/resources/crop.png" alt="قمح">
                                    <span class="resource-name">قمح</span>
                                    <span class="resource-amount"><?php echo formatNumber($village['crop']); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إحصائيات القرية -->
                    <div class="village-stats-card">
                        <h3>الإحصائيات</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value"><?php echo $villageStats['total_buildings']; ?></div>
                                <div class="stat-label">عدد المباني</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value"><?php echo formatNumber($villageStats['total_units']); ?></div>
                                <div class="stat-label">إجمالي الوحدات</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value"><?php echo $villageStats['attacks_sent']; ?></div>
                                <div class="stat-label">هجمات مرسلة</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value"><?php echo $villageStats['attacks_received']; ?></div>
                                <div class="stat-label">هجمات مستقبلة</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value"><?php echo $villageStats['trades_sent']; ?></div>
                                <div class="stat-label">تجارة مرسلة</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value"><?php echo $villageStats['trades_received']; ?></div>
                                <div class="stat-label">تجارة مستقبلة</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- مباني القرية -->
                <?php if (!empty($buildings)): ?>
                    <div class="village-buildings">
                        <h3>مباني القرية</h3>
                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>نوع المبنى</th>
                                        <th>المستوى</th>
                                        <th>الوصف</th>
                                        <th>تاريخ البناء</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($buildings as $building): ?>
                                        <tr>
                                            <td>
                                                <img src="../images/buildings/<?php echo $building['building_type']; ?>.png" 
                                                     alt="<?php echo htmlspecialchars($building['building_name']); ?>" 
                                                     class="building-icon">
                                                <?php echo htmlspecialchars($building['building_name']); ?>
                                            </td>
                                            <td>
                                                <span class="level-badge">المستوى <?php echo $building['level']; ?></span>
                                            </td>
                                            <td><?php echo htmlspecialchars($building['description']); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($building['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- وحدات القرية -->
                <?php if (!empty($units)): ?>
                    <div class="village-units">
                        <h3>وحدات القرية</h3>
                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>نوع الوحدة</th>
                                        <th>العدد</th>
                                        <th>قوة الهجوم</th>
                                        <th>دفاع المشاة</th>
                                        <th>دفاع الفرسان</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($units as $unit): ?>
                                        <tr>
                                            <td>
                                                <img src="../images/units/<?php echo $unit['unit_type']; ?>.png" 
                                                     alt="<?php echo htmlspecialchars($unit['unit_name']); ?>" 
                                                     class="unit-icon">
                                                <?php echo htmlspecialchars($unit['unit_name']); ?>
                                            </td>
                                            <td>
                                                <span class="unit-count"><?php echo formatNumber($unit['count']); ?></span>
                                            </td>
                                            <td><?php echo $unit['attack']; ?></td>
                                            <td><?php echo $unit['defense_infantry']; ?></td>
                                            <td><?php echo $unit['defense_cavalry']; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>
                
            <?php endif; ?>
        </main>
    </div>
    
    <script src="../js/admin.js"></script>
    <script>
        function resetVillage(villageId) {
            if (confirm('هل أنت متأكد من إعادة تعيين هذه القرية؟ سيتم حذف جميع المباني والوحدات!')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="reset_village">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function deleteVillage(villageId) {
            if (confirm('هل أنت متأكد من حذف هذه القرية نهائياً؟ لا يمكن التراجع عن هذا الإجراء!')) {
                window.location.href = `?action=delete&id=${villageId}`;
            }
        }
        
        function generateMap() {
            window.open('map-generator.php', '_blank');
        }
    </script>
</body>
</html>
