<?php
/**
 * صفحة إدارة التجارة
 * Trade Management Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];
$villageId = $_SESSION['current_village_id'];

// التحقق من القرية
$village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$villageId, $userId]);
if (!$village) {
    redirect('index.php');
}

// الحصول على التجارة الصادرة
$outgoingTrades = $db->select("
    SELECT t.*, v.name as target_village_name, u.username as target_username
    FROM trades t
    JOIN villages v ON t.receiver_village_id = v.id
    JOIN users u ON v.user_id = u.id
    WHERE t.sender_village_id = ? AND t.status = 'in_transit'
    ORDER BY t.arrival_time ASC
", [$villageId]);

// الحصول على التجارة الواردة
$incomingTrades = $db->select("
    SELECT t.*, v.name as source_village_name, u.username as source_username
    FROM trades t
    JOIN villages v ON t.sender_village_id = v.id
    JOIN users u ON v.user_id = u.id
    WHERE t.receiver_village_id = ? AND t.status = 'in_transit'
    ORDER BY t.arrival_time ASC
", [$villageId]);

// الحصول على تاريخ التجارة
$tradeHistory = $db->select("
    SELECT t.*, 
           sv.name as source_village_name, su.username as source_username,
           tv.name as target_village_name, tu.username as target_username
    FROM trades t
    JOIN villages sv ON t.sender_village_id = sv.id
    JOIN users su ON sv.user_id = su.id
    JOIN villages tv ON t.receiver_village_id = tv.id
    JOIN users tu ON tv.user_id = tu.id
    WHERE (t.sender_village_id = ? OR t.receiver_village_id = ?) 
          AND t.status IN ('completed', 'cancelled')
    ORDER BY t.completed_at DESC
    LIMIT 20
", [$villageId, $villageId]);

// معالجة إلغاء التجارة
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['cancel_trade'])) {
    $tradeId = intval($_POST['trade_id'] ?? 0);
    $csrf = $_POST['csrf_token'] ?? '';
    
    if (!validateCSRF($csrf)) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        $trade = $db->selectOne("SELECT * FROM trades WHERE id = ? AND sender_village_id = ? AND status = 'in_transit'", [$tradeId, $villageId]);
        
        if (!$trade) {
            $error = 'التجارة غير موجودة أو لا يمكن إلغاؤها';
        } else {
            // إلغاء التجارة وإرجاع الموارد
            $db->beginTransaction();
            try {
                // إرجاع الموارد
                $db->update("UPDATE resources SET wood = wood + ?, clay = clay + ?, iron = iron + ?, crop = crop + ? WHERE village_id = ?", 
                           [$trade['wood_amount'], $trade['clay_amount'], $trade['iron_amount'], $trade['crop_amount'], $villageId]);
                
                // تحديث حالة التجارة
                $db->update("UPDATE trades SET status = 'cancelled', completed_at = NOW() WHERE id = ?", [$tradeId]);
                
                $db->commit();
                $success = 'تم إلغاء التجارة وإرجاع الموارد';
                
                // تحديث البيانات
                $outgoingTrades = $db->select("SELECT t.*, v.name as target_village_name, u.username as target_username FROM trades t JOIN villages v ON t.receiver_village_id = v.id JOIN users u ON v.user_id = u.id WHERE t.sender_village_id = ? AND t.status = 'in_transit' ORDER BY t.arrival_time ASC", [$villageId]);
                
            } catch (Exception $e) {
                $db->rollback();
                $error = 'حدث خطأ أثناء إلغاء التجارة';
            }
        }
    }
}

$csrfToken = generateCSRF();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التجارة - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/trade-management.css">
</head>
<body class="game-page">
    <?php include 'includes/header.php'; ?>
    
    <div class="main-content">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="content">
            <div class="page-header">
                <h1>إدارة التجارة</h1>
                <p>متابعة التجارة الواردة والصادرة</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <!-- إحصائيات التجارة -->
            <div class="trade-stats">
                <div class="stats-grid">
                    <div class="stat-card outgoing">
                        <div class="stat-icon">📤</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo count($outgoingTrades); ?></div>
                            <div class="stat-label">تجارة صادرة</div>
                        </div>
                    </div>
                    
                    <div class="stat-card incoming">
                        <div class="stat-icon">📥</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo count($incomingTrades); ?></div>
                            <div class="stat-label">تجارة واردة</div>
                        </div>
                    </div>
                    
                    <div class="stat-card total">
                        <div class="stat-icon">📊</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo count($tradeHistory); ?></div>
                            <div class="stat-label">إجمالي التجارة</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- التجارة الصادرة -->
            <div class="trade-section">
                <h3>📤 التجارة الصادرة</h3>
                
                <?php if (!empty($outgoingTrades)): ?>
                    <div class="trades-list">
                        <?php foreach ($outgoingTrades as $trade): ?>
                            <div class="trade-item outgoing">
                                <div class="trade-header">
                                    <div class="trade-info">
                                        <div class="destination">
                                            <strong>إلى: <?php echo htmlspecialchars($trade['target_village_name']); ?></strong>
                                            <span>(<?php echo htmlspecialchars($trade['target_username']); ?>)</span>
                                        </div>
                                        <div class="trade-time">
                                            <?php
                                            $arrivalTime = strtotime($trade['arrival_time']);
                                            $timeLeft = $arrivalTime - time();
                                            ?>
                                            <?php if ($timeLeft > 0): ?>
                                                <span class="countdown" data-time="<?php echo $arrivalTime; ?>">
                                                    يصل خلال: <?php echo formatDuration($timeLeft); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="arrived">وصلت</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="trade-actions">
                                        <?php if ($timeLeft > 0): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                                <input type="hidden" name="trade_id" value="<?php echo $trade['id']; ?>">
                                                <button type="submit" name="cancel_trade" class="btn btn-danger btn-sm" 
                                                        onclick="return confirm('هل أنت متأكد من إلغاء هذه التجارة؟')">
                                                    إلغاء
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="trade-resources">
                                    <h5>الموارد المرسلة:</h5>
                                    <div class="resources-list">
                                        <?php if ($trade['wood_amount'] > 0): ?>
                                            <div class="resource">
                                                <img src="images/resources/wood.png" alt="خشب">
                                                <span><?php echo formatNumber($trade['wood_amount']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($trade['clay_amount'] > 0): ?>
                                            <div class="resource">
                                                <img src="images/resources/clay.png" alt="طين">
                                                <span><?php echo formatNumber($trade['clay_amount']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($trade['iron_amount'] > 0): ?>
                                            <div class="resource">
                                                <img src="images/resources/iron.png" alt="حديد">
                                                <span><?php echo formatNumber($trade['iron_amount']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($trade['crop_amount'] > 0): ?>
                                            <div class="resource">
                                                <img src="images/resources/crop.png" alt="قمح">
                                                <span><?php echo formatNumber($trade['crop_amount']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="no-trades">
                        <p>لا توجد تجارة صادرة حالياً</p>
                        <a href="trade.php" class="btn btn-primary">إرسال تجارة جديدة</a>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- التجارة الواردة -->
            <div class="trade-section">
                <h3>📥 التجارة الواردة</h3>
                
                <?php if (!empty($incomingTrades)): ?>
                    <div class="trades-list">
                        <?php foreach ($incomingTrades as $trade): ?>
                            <div class="trade-item incoming">
                                <div class="trade-header">
                                    <div class="trade-info">
                                        <div class="source">
                                            <strong>من: <?php echo htmlspecialchars($trade['source_village_name']); ?></strong>
                                            <span>(<?php echo htmlspecialchars($trade['source_username']); ?>)</span>
                                        </div>
                                        <div class="trade-time">
                                            <?php
                                            $arrivalTime = strtotime($trade['arrival_time']);
                                            $timeLeft = $arrivalTime - time();
                                            ?>
                                            <?php if ($timeLeft > 0): ?>
                                                <span class="countdown" data-time="<?php echo $arrivalTime; ?>">
                                                    تصل خلال: <?php echo formatDuration($timeLeft); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="arrived">وصلت</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="trade-resources">
                                    <h5>الموارد الواردة:</h5>
                                    <div class="resources-list">
                                        <?php if ($trade['wood_amount'] > 0): ?>
                                            <div class="resource">
                                                <img src="images/resources/wood.png" alt="خشب">
                                                <span><?php echo formatNumber($trade['wood_amount']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($trade['clay_amount'] > 0): ?>
                                            <div class="resource">
                                                <img src="images/resources/clay.png" alt="طين">
                                                <span><?php echo formatNumber($trade['clay_amount']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($trade['iron_amount'] > 0): ?>
                                            <div class="resource">
                                                <img src="images/resources/iron.png" alt="حديد">
                                                <span><?php echo formatNumber($trade['iron_amount']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($trade['crop_amount'] > 0): ?>
                                            <div class="resource">
                                                <img src="images/resources/crop.png" alt="قمح">
                                                <span><?php echo formatNumber($trade['crop_amount']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="no-trades">
                        <p>لا توجد تجارة واردة حالياً</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- تاريخ التجارة -->
            <?php if (!empty($tradeHistory)): ?>
                <div class="trade-section">
                    <h3>📋 تاريخ التجارة</h3>
                    
                    <div class="history-list">
                        <?php foreach ($tradeHistory as $trade): ?>
                            <div class="history-item <?php echo $trade['status']; ?>">
                                <div class="history-header">
                                    <div class="trade-direction">
                                        <?php if ($trade['sender_village_id'] == $villageId): ?>
                                            <span class="direction outgoing">📤 صادرة</span>
                                            <span class="partner">إلى: <?php echo htmlspecialchars($trade['target_village_name']); ?> (<?php echo htmlspecialchars($trade['target_username']); ?>)</span>
                                        <?php else: ?>
                                            <span class="direction incoming">📥 واردة</span>
                                            <span class="partner">من: <?php echo htmlspecialchars($trade['source_village_name']); ?> (<?php echo htmlspecialchars($trade['source_username']); ?>)</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="trade-status">
                                        <span class="status <?php echo $trade['status']; ?>">
                                            <?php echo $trade['status'] == 'completed' ? 'مكتملة' : 'ملغية'; ?>
                                        </span>
                                        <span class="date"><?php echo date('d/m/Y H:i', strtotime($trade['completed_at'])); ?></span>
                                    </div>
                                </div>
                                
                                <div class="history-resources">
                                    <div class="resources-summary">
                                        <?php if ($trade['wood_amount'] > 0) echo formatNumber($trade['wood_amount']) . ' خشب '; ?>
                                        <?php if ($trade['clay_amount'] > 0) echo formatNumber($trade['clay_amount']) . ' طين '; ?>
                                        <?php if ($trade['iron_amount'] > 0) echo formatNumber($trade['iron_amount']) . ' حديد '; ?>
                                        <?php if ($trade['crop_amount'] > 0) echo formatNumber($trade['crop_amount']) . ' قمح '; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </main>
    </div>
    
    <script>
        // تحديث العد التنازلي
        function updateCountdowns() {
            const countdowns = document.querySelectorAll('.countdown');
            countdowns.forEach(countdown => {
                const targetTime = parseInt(countdown.dataset.time);
                const currentTime = Math.floor(Date.now() / 1000);
                const timeLeft = targetTime - currentTime;
                
                if (timeLeft > 0) {
                    countdown.textContent = 'يصل خلال: ' + formatDuration(timeLeft);
                } else {
                    countdown.textContent = 'وصلت';
                    countdown.className = 'arrived';
                    // إعادة تحميل الصفحة عند وصول التجارة
                    setTimeout(() => location.reload(), 1000);
                }
            });
        }
        
        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            }
        }
        
        // تحديث العد التنازلي كل ثانية
        setInterval(updateCountdowns, 1000);
        updateCountdowns();
    </script>
</body>
</html>
