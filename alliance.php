<?php
/**
 * صفحة التحالفات
 * Alliance Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];

// الحصول على بيانات المستخدم
$user = $db->selectOne("SELECT * FROM users WHERE id = ?", [$userId]);
if (!$user) {
    redirect('logout.php');
}

// الحصول على تحالف المستخدم
$userAlliance = $db->selectOne("
    SELECT a.*, am.rank, am.joined_at 
    FROM alliances a 
    JOIN alliance_members am ON a.id = am.alliance_id 
    WHERE am.user_id = ?
", [$userId]);

$action = $_GET['action'] ?? 'overview';
$allianceId = $_GET['id'] ?? null;

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $postAction = $_POST['action'] ?? '';
    
    if ($postAction === 'create_alliance') {
        $name = cleanInput($_POST['name']);
        $tag = cleanInput($_POST['tag']);
        $description = cleanInput($_POST['description']);
        
        if (strlen($name) < 3 || strlen($name) > 50) {
            $error = 'اسم التحالف يجب أن يكون بين 3 و 50 حرف';
        } elseif (strlen($tag) < 2 || strlen($tag) > 10) {
            $error = 'رمز التحالف يجب أن يكون بين 2 و 10 أحرف';
        } elseif ($userAlliance) {
            $error = 'أنت عضو في تحالف بالفعل';
        } else {
            // التحقق من عدم وجود التحالف
            $existing = $db->selectOne("SELECT id FROM alliances WHERE name = ? OR tag = ?", [$name, $tag]);
            if ($existing) {
                $error = 'اسم التحالف أو الرمز مستخدم بالفعل';
            } else {
                $db->beginTransaction();
                try {
                    // إنشاء التحالف
                    $allianceId = $db->insert("INSERT INTO alliances (name, tag, description, leader_id) VALUES (?, ?, ?, ?)", 
                                             [$name, $tag, $description, $userId]);
                    
                    // إضافة المستخدم كقائد
                    $db->insert("INSERT INTO alliance_members (alliance_id, user_id, rank) VALUES (?, ?, ?)", 
                               [$allianceId, $userId, 'leader']);
                    
                    $db->commit();
                    $success = 'تم إنشاء التحالف بنجاح';
                    
                    // تحديث بيانات التحالف
                    $userAlliance = $db->selectOne("
                        SELECT a.*, am.rank, am.joined_at 
                        FROM alliances a 
                        JOIN alliance_members am ON a.id = am.alliance_id 
                        WHERE am.user_id = ?
                    ", [$userId]);
                    
                } catch (Exception $e) {
                    $db->rollback();
                    $error = 'حدث خطأ في إنشاء التحالف';
                }
            }
        }
    } elseif ($postAction === 'join_alliance' && $allianceId) {
        if ($userAlliance) {
            $error = 'أنت عضو في تحالف بالفعل';
        } else {
            $alliance = $db->selectOne("SELECT * FROM alliances WHERE id = ?", [$allianceId]);
            if (!$alliance) {
                $error = 'التحالف غير موجود';
            } else {
                $db->insert("INSERT INTO alliance_members (alliance_id, user_id, rank) VALUES (?, ?, ?)", 
                           [$allianceId, $userId, 'member']);
                $success = 'تم الانضمام للتحالف بنجاح';
                
                // تحديث بيانات التحالف
                $userAlliance = $db->selectOne("
                    SELECT a.*, am.rank, am.joined_at 
                    FROM alliances a 
                    JOIN alliance_members am ON a.id = am.alliance_id 
                    WHERE am.user_id = ?
                ", [$userId]);
            }
        }
    } elseif ($postAction === 'leave_alliance') {
        if (!$userAlliance) {
            $error = 'أنت لست عضو في أي تحالف';
        } elseif ($userAlliance['rank'] === 'leader') {
            $error = 'لا يمكن للقائد ترك التحالف. قم بتعيين قائد جديد أولاً';
        } else {
            $db->delete('alliance_members', ['alliance_id' => $userAlliance['id'], 'user_id' => $userId]);
            $success = 'تم ترك التحالف بنجاح';
            $userAlliance = null;
        }
    }
}

// الحصول على قائمة التحالفات
if ($action === 'list') {
    $alliances = $db->select("
        SELECT a.*, u.username as leader_name,
               COUNT(am.user_id) as member_count,
               SUM(v.population) as total_population
        FROM alliances a
        LEFT JOIN users u ON a.leader_id = u.id
        LEFT JOIN alliance_members am ON a.id = am.alliance_id
        LEFT JOIN villages v ON am.user_id = v.user_id
        GROUP BY a.id
        ORDER BY total_population DESC
        LIMIT 20
    ");
} elseif ($action === 'view' && $allianceId) {
    $alliance = $db->selectOne("
        SELECT a.*, u.username as leader_name
        FROM alliances a
        LEFT JOIN users u ON a.leader_id = u.id
        WHERE a.id = ?
    ", [$allianceId]);
    
    if ($alliance) {
        $members = $db->select("
            SELECT u.username, am.rank, am.joined_at,
                   COUNT(v.id) as village_count,
                   SUM(v.population) as total_population
            FROM alliance_members am
            JOIN users u ON am.user_id = u.id
            LEFT JOIN villages v ON u.id = v.user_id
            WHERE am.alliance_id = ?
            GROUP BY u.id
            ORDER BY total_population DESC
        ", [$allianceId]);
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحالفات - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/alliance.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="alliance-container">
        <?php if (isset($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <div class="alliance-header">
            <h1>🤝 التحالفات</h1>
            <div class="alliance-nav">
                <a href="?action=overview" class="nav-link <?php echo $action === 'overview' ? 'active' : ''; ?>">نظرة عامة</a>
                <a href="?action=list" class="nav-link <?php echo $action === 'list' ? 'active' : ''; ?>">قائمة التحالفات</a>
                <?php if (!$userAlliance): ?>
                    <a href="?action=create" class="nav-link <?php echo $action === 'create' ? 'active' : ''; ?>">إنشاء تحالف</a>
                <?php endif; ?>
            </div>
        </div>
        
        <?php if ($action === 'overview'): ?>
            <?php if ($userAlliance): ?>
                <!-- تحالف المستخدم -->
                <div class="alliance-section">
                    <h2>تحالفي: <?php echo htmlspecialchars($userAlliance['name']); ?> [<?php echo htmlspecialchars($userAlliance['tag']); ?>]</h2>
                    
                    <div class="alliance-info">
                        <div class="info-item">
                            <strong>الرتبة:</strong> <?php echo $userAlliance['rank'] === 'leader' ? 'قائد' : 'عضو'; ?>
                        </div>
                        <div class="info-item">
                            <strong>تاريخ الانضمام:</strong> <?php echo date('d/m/Y', strtotime($userAlliance['joined_at'])); ?>
                        </div>
                        <div class="info-item">
                            <strong>الوصف:</strong> <?php echo htmlspecialchars($userAlliance['description']); ?>
                        </div>
                    </div>
                    
                    <div class="alliance-actions">
                        <a href="?action=view&id=<?php echo $userAlliance['id']; ?>" class="btn btn-primary">عرض التحالف</a>
                        <?php if ($userAlliance['rank'] !== 'leader'): ?>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="leave_alliance">
                                <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من ترك التحالف؟')">ترك التحالف</button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- لا يوجد تحالف -->
                <div class="alliance-section">
                    <h2>لست عضو في أي تحالف</h2>
                    <p>انضم إلى تحالف للاستفادة من المزايا التالية:</p>
                    <ul>
                        <li>التعاون مع اللاعبين الآخرين</li>
                        <li>تبادل الموارد والمساعدة</li>
                        <li>الحماية المتبادلة</li>
                        <li>التخطيط للهجمات المشتركة</li>
                    </ul>
                    
                    <div class="alliance-actions">
                        <a href="?action=list" class="btn btn-primary">تصفح التحالفات</a>
                        <a href="?action=create" class="btn btn-secondary">إنشاء تحالف جديد</a>
                    </div>
                </div>
            <?php endif; ?>
            
        <?php elseif ($action === 'list'): ?>
            <!-- قائمة التحالفات -->
            <div class="alliance-section">
                <h2>قائمة التحالفات</h2>
                
                <?php if (!empty($alliances)): ?>
                    <div class="alliances-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>الترتيب</th>
                                    <th>اسم التحالف</th>
                                    <th>الرمز</th>
                                    <th>القائد</th>
                                    <th>الأعضاء</th>
                                    <th>إجمالي السكان</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($alliances as $index => $alliance): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td><?php echo htmlspecialchars($alliance['name']); ?></td>
                                        <td>[<?php echo htmlspecialchars($alliance['tag']); ?>]</td>
                                        <td><?php echo htmlspecialchars($alliance['leader_name']); ?></td>
                                        <td><?php echo $alliance['member_count']; ?></td>
                                        <td><?php echo formatNumber($alliance['total_population'] ?? 0); ?></td>
                                        <td>
                                            <a href="?action=view&id=<?php echo $alliance['id']; ?>" class="btn btn-sm">عرض</a>
                                            <?php if (!$userAlliance): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="join_alliance">
                                                    <button type="submit" class="btn btn-sm btn-primary">انضمام</button>
                                                </form>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p>لا توجد تحالفات بعد.</p>
                <?php endif; ?>
            </div>
            
        <?php elseif ($action === 'create' && !$userAlliance): ?>
            <!-- إنشاء تحالف جديد -->
            <div class="alliance-section">
                <h2>إنشاء تحالف جديد</h2>
                
                <form method="POST" class="alliance-form">
                    <input type="hidden" name="action" value="create_alliance">
                    
                    <div class="form-group">
                        <label>اسم التحالف:</label>
                        <input type="text" name="name" required maxlength="50" placeholder="اسم التحالف">
                    </div>
                    
                    <div class="form-group">
                        <label>رمز التحالف:</label>
                        <input type="text" name="tag" required maxlength="10" placeholder="رمز مختصر">
                    </div>
                    
                    <div class="form-group">
                        <label>وصف التحالف:</label>
                        <textarea name="description" rows="4" placeholder="وصف مختصر عن التحالف وأهدافه"></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">إنشاء التحالف</button>
                </form>
            </div>
            
        <?php elseif ($action === 'view' && isset($alliance)): ?>
            <!-- عرض تفاصيل التحالف -->
            <div class="alliance-section">
                <h2><?php echo htmlspecialchars($alliance['name']); ?> [<?php echo htmlspecialchars($alliance['tag']); ?>]</h2>
                
                <div class="alliance-details">
                    <div class="detail-item">
                        <strong>القائد:</strong> <?php echo htmlspecialchars($alliance['leader_name']); ?>
                    </div>
                    <div class="detail-item">
                        <strong>تاريخ التأسيس:</strong> <?php echo date('d/m/Y', strtotime($alliance['created_at'])); ?>
                    </div>
                    <div class="detail-item">
                        <strong>الوصف:</strong> <?php echo htmlspecialchars($alliance['description']); ?>
                    </div>
                </div>
                
                <?php if (!empty($members)): ?>
                    <h3>أعضاء التحالف</h3>
                    <div class="members-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>اسم المستخدم</th>
                                    <th>الرتبة</th>
                                    <th>القرى</th>
                                    <th>إجمالي السكان</th>
                                    <th>تاريخ الانضمام</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($members as $member): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($member['username']); ?></td>
                                        <td><?php echo $member['rank'] === 'leader' ? 'قائد' : 'عضو'; ?></td>
                                        <td><?php echo $member['village_count']; ?></td>
                                        <td><?php echo formatNumber($member['total_population'] ?? 0); ?></td>
                                        <td><?php echo date('d/m/Y', strtotime($member['joined_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
                
                <?php if (!$userAlliance): ?>
                    <form method="POST" style="margin-top: 20px;">
                        <input type="hidden" name="action" value="join_alliance">
                        <button type="submit" class="btn btn-primary">انضمام للتحالف</button>
                    </form>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <?php include 'includes/sidebar.php'; ?>
    
    <script src="js/game.js"></script>
</body>
</html>
