/* ملف CSS للإسطبل */
/* Stable CSS */

/* معلومات الإسطبل */
.stable-info {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    border: 2px solid #ffc107;
    position: relative;
    overflow: hidden;
}

.stable-info::before {
    content: "🐎";
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 4rem;
    opacity: 0.1;
    transform: rotate(15deg);
}

.stable-info h3 {
    color: #856404;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.4rem;
}

.stable-level {
    background-color: #ffc107;
    color: #212529;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
    margin-right: 10px;
}

.stable-description {
    color: #856404;
    margin-bottom: 20px;
    line-height: 1.6;
    font-size: 1rem;
}

.stable-benefits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.stable-benefits .benefit-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border: 1px solid #ffc107;
}

.stable-benefits .benefit-item .icon {
    width: 28px;
    height: 28px;
    background-color: #ffc107;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #212529;
    font-weight: bold;
    font-size: 1rem;
}

.stable-benefits .benefit-item .text {
    color: #856404;
    font-size: 0.95rem;
    font-weight: 500;
}

/* تحسينات لوحدات الفرسان */
.cavalry-unit {
    position: relative;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 2px solid #ffc107;
    transition: all 0.3s ease;
}

.cavalry-unit:hover {
    border-color: #e0a800;
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
    transform: translateY(-3px);
}

.cavalry-unit::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ffc107, #e0a800);
}

.cavalry-unit.training::before {
    background: linear-gradient(90deg, #dc3545, #c82333);
    animation: pulse 2s infinite;
}

.cavalry-badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* تحسينات لإحصائيات الوحدات */
.cavalry-unit .unit-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 15px;
}

.cavalry-unit .unit-stats .stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.cavalry-unit .unit-stats .stat:hover {
    transform: scale(1.02);
}

.cavalry-unit .unit-stats .stat.attack {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border: 1px solid #dc3545;
}

.cavalry-unit .unit-stats .stat.defense {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 1px solid #28a745;
}

.cavalry-unit .unit-stats .stat.speed {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border: 1px solid #17a2b8;
}

.cavalry-unit .unit-stats .stat.carry {
    background: linear-gradient(135deg, #e2e3e5, #d6d8db);
    border: 1px solid #6c757d;
}

.cavalry-unit .unit-stats .label {
    color: #495057;
    font-weight: 500;
}

.cavalry-unit .unit-stats .value {
    font-weight: bold;
    color: #212529;
}

/* وصف الوحدة */
.unit-description {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    margin: 15px 0;
    border-left: 4px solid #ffc107;
    font-size: 0.9rem;
    color: #495057;
    line-height: 1.5;
    font-style: italic;
}

/* تحسينات لأزرار الفرسان */
.cavalry-btn {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border: none;
    color: #212529;
    font-weight: bold;
    padding: 12px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.cavalry-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

.cavalry-btn:disabled {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: #fff;
    cursor: not-allowed;
    opacity: 0.6;
}

/* جدول الفرسان */
.cavalry-table {
    border: 2px solid #ffc107;
    box-shadow: 0 4px 20px rgba(255, 193, 7, 0.2);
}

.cavalry-table thead {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.cavalry-table th {
    color: #212529;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.cavalry-table tbody tr {
    transition: all 0.3s ease;
}

.cavalry-table tbody tr:hover {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    transform: scale(1.01);
}

.cavalry-table .attack-power {
    color: #dc3545;
    font-weight: bold;
}

.cavalry-table .defense-power {
    color: #28a745;
    font-weight: bold;
}

.cavalry-table .speed-value {
    color: #17a2b8;
    font-weight: bold;
}

/* تحسينات للتكلفة الإجمالية */
.cavalry-unit .total-cost {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffc107;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.cavalry-unit .total-cost .cost-breakdown {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-top: 10px;
}

.cavalry-unit .total-cost .cost-item {
    background-color: white;
    border: 1px solid #ffc107;
    border-radius: 6px;
    padding: 8px;
    text-align: center;
    font-weight: 600;
    color: #856404;
    transition: all 0.3s ease;
}

.cavalry-unit .total-cost .cost-item:hover {
    background-color: #ffc107;
    color: #212529;
    transform: scale(1.05);
}

/* تحسينات للموارد */
.resources-summary {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffc107;
}

.resources-summary .resource {
    background: linear-gradient(135deg, white 0%, #fff3cd 100%);
    border: 2px solid #ffc107;
}

.resources-summary .resource:hover {
    border-color: #e0a800;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

/* تأثيرات الحركة المحسنة */
@keyframes gallop {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(2px); }
    75% { transform: translateX(-2px); }
}

.cavalry-unit:hover .cavalry-badge {
    animation: gallop 0.5s ease-in-out;
}

@keyframes shine {
    0% { background-position: -200px 0; }
    100% { background-position: 200px 0; }
}

.cavalry-btn:not(:disabled)::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.cavalry-btn:not(:disabled):hover::before {
    left: 100%;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .stable-benefits {
        grid-template-columns: 1fr;
    }
    
    .cavalry-unit .unit-stats {
        grid-template-columns: 1fr;
    }
    
    .cavalry-unit .total-cost .cost-breakdown {
        grid-template-columns: 1fr;
    }
    
    .cavalry-table {
        font-size: 0.8rem;
    }
    
    .cavalry-table th,
    .cavalry-table td {
        padding: 6px 8px;
    }
    
    .stable-info::before {
        font-size: 3rem;
        top: -5px;
        right: -5px;
    }
}

@media (max-width: 480px) {
    .stable-info {
        padding: 15px;
    }
    
    .stable-info h3 {
        font-size: 1.2rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .stable-level {
        margin-right: 0;
        margin-top: 5px;
    }
    
    .cavalry-badge {
        width: 20px;
        height: 20px;
        font-size: 10px;
    }
    
    .unit-description {
        font-size: 0.8rem;
        padding: 10px;
    }
    
    .cavalry-btn {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
    
    .cavalry-table th,
    .cavalry-table td {
        padding: 4px 6px;
        font-size: 0.7rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .stable-info::before {
        display: none;
    }
    
    .cavalry-unit::before {
        display: none;
    }
    
    .cavalry-badge {
        background: #ffc107 !important;
        color: #212529 !important;
    }
    
    .cavalry-table {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .cavalry-table thead {
        background-color: #f0f0f0 !important;
    }
    
    .cavalry-table th {
        color: #000 !important;
    }
}

/* تحسينات للوضع المظلم (إذا تم تطبيقه لاحقاً) */
@media (prefers-color-scheme: dark) {
    .stable-info {
        background: linear-gradient(135deg, #3d3d00 0%, #4d4d00 100%);
        border-color: #ffc107;
    }
    
    .stable-info h3,
    .stable-description,
    .stable-benefits .benefit-item .text {
        color: #ffc107;
    }
    
    .cavalry-unit {
        background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
        border-color: #ffc107;
    }
    
    .unit-description {
        background-color: #3d3d3d;
        color: #ffc107;
        border-left-color: #ffc107;
    }
}
