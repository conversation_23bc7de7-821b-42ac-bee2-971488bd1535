<?php
/**
 * ملف القائمة الجانبية المشتركة
 * Shared Sidebar File
 */

// تحديد الصفحة الحالية
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
?>

<!-- القائمة الجانبية -->
<aside class="sidebar">
    <nav class="game-nav">
        <ul>
            <li>
                <a href="index.php" class="<?php echo ($currentPage == 'index') ? 'active' : ''; ?>">
                    <img src="images/icons/overview.png" alt="نظرة عامة" class="icon">
                    نظرة عامة
                </a>
            </li>
            <li>
                <a href="village.php" class="<?php echo ($currentPage == 'village') ? 'active' : ''; ?>">
                    <img src="images/icons/village.png" alt="القرية" class="icon">
                    القرية
                </a>
            </li>
            <li>
                <a href="buildings.php" class="<?php echo ($currentPage == 'buildings') ? 'active' : ''; ?>">
                    <img src="images/icons/buildings.png" alt="المباني" class="icon">
                    المباني
                </a>
            </li>
            <li>
                <a href="fields.php" class="<?php echo ($currentPage == 'fields') ? 'active' : ''; ?>">
                    <img src="images/icons/fields.png" alt="الحقول" class="icon">
                    الحقول
                </a>
            </li>
            <li>
                <a href="army.php" class="<?php echo ($currentPage == 'army') ? 'active' : ''; ?>">
                    <img src="images/icons/army.png" alt="الجيش" class="icon">
                    الجيش
                </a>
            </li>
            <li>
                <a href="barracks.php" class="<?php echo ($currentPage == 'barracks') ? 'active' : ''; ?>">
                    <img src="images/icons/barracks.png" alt="الثكنة" class="icon">
                    الثكنة
                </a>
            </li>
            <li>
                <a href="stable.php" class="<?php echo ($currentPage == 'stable') ? 'active' : ''; ?>">
                    <img src="images/icons/stable.png" alt="الإسطبل" class="icon">
                    الإسطبل
                </a>
            </li>
            <li>
                <a href="marketplace.php" class="<?php echo ($currentPage == 'marketplace') ? 'active' : ''; ?>">
                    <img src="images/icons/marketplace.png" alt="السوق" class="icon">
                    السوق
                </a>
            </li>
            <li>
                <a href="trade.php" class="<?php echo ($currentPage == 'trade') ? 'active' : ''; ?>">
                    <img src="images/icons/trade.png" alt="التجارة" class="icon">
                    التجارة
                </a>
            </li>
            <li>
                <a href="reports.php" class="<?php echo ($currentPage == 'reports') ? 'active' : ''; ?>">
                    <img src="images/icons/reports.png" alt="التقارير" class="icon">
                    التقارير
                </a>
            </li>
            <li>
                <a href="messages.php" class="<?php echo ($currentPage == 'messages') ? 'active' : ''; ?>">
                    <img src="images/icons/message.png" alt="الرسائل" class="icon">
                    الرسائل
                    <?php if (isset($unreadMessages) && $unreadMessages['count'] > 0): ?>
                        <span class="badge"><?php echo $unreadMessages['count']; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            <li>
                <a href="alliance.php" class="<?php echo ($currentPage == 'alliance') ? 'active' : ''; ?>">
                    <img src="images/icons/alliance.png" alt="التحالف" class="icon">
                    التحالف
                </a>
            </li>
            <li>
                <a href="map.php" class="<?php echo ($currentPage == 'map') ? 'active' : ''; ?>">
                    <img src="images/icons/map.png" alt="الخريطة" class="icon">
                    الخريطة
                </a>
            </li>
            <li>
                <a href="statistics.php" class="<?php echo ($currentPage == 'statistics') ? 'active' : ''; ?>">
                    <img src="images/icons/statistics.png" alt="الإحصائيات" class="icon">
                    الإحصائيات
                </a>
            </li>
            <li>
                <a href="hero.php" class="<?php echo ($currentPage == 'hero') ? 'active' : ''; ?>">
                    <img src="images/icons/hero.png" alt="البطل" class="icon">
                    البطل
                </a>
            </li>
            <li>
                <a href="profile.php" class="<?php echo ($currentPage == 'profile') ? 'active' : ''; ?>">
                    <img src="images/icons/profile.png" alt="الملف الشخصي" class="icon">
                    الملف الشخصي
                </a>
            </li>
            <?php if (isAdmin()): ?>
            <li>
                <a href="admin/" class="admin-link">
                    <img src="images/icons/admin.png" alt="الإدارة" class="icon">
                    لوحة الإدارة
                </a>
            </li>
            <?php endif; ?>
        </ul>
    </nav>
</aside>

<style>
.sidebar .game-nav a {
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
}

.sidebar .game-nav .icon {
    width: 20px;
    height: 20px;
    opacity: 0.7;
}

.sidebar .game-nav a.active .icon,
.sidebar .game-nav a:hover .icon {
    opacity: 1;
}

.sidebar .game-nav .badge {
    position: absolute;
    left: 10px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.sidebar .game-nav .admin-link {
    background-color: #ffc107;
    color: #212529;
    font-weight: bold;
}

.sidebar .game-nav .admin-link:hover {
    background-color: #e0a800;
    color: #212529;
}

@media (max-width: 768px) {
    .sidebar .game-nav a {
        justify-content: center;
        flex-direction: column;
        gap: 5px;
        font-size: 12px;
        padding: 10px 5px;
    }
    
    .sidebar .game-nav .icon {
        width: 24px;
        height: 24px;
    }
    
    .sidebar .game-nav .badge {
        top: 5px;
        left: 50%;
        transform: translateX(50%);
    }
}
</style>
