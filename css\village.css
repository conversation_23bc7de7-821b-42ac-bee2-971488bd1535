/* ملف CSS لصفحة القرية */
/* Village Page CSS */

.capital-badge {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    margin-right: 10px;
    box-shadow: 0 2px 5px rgba(255, 215, 0, 0.3);
}

/* معلومات القرية */
.village-info {
    margin-bottom: 30px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.info-card {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.info-card h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item .label {
    color: #6c757d;
    font-weight: 500;
}

.info-item .value {
    font-weight: bold;
    color: #495057;
}

.info-item .btn {
    margin-right: 10px;
}

/* معلومات الموارد */
.resources-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.resource-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.resource-item img {
    width: 32px;
    height: 32px;
}

.resource-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.resource-details .amount {
    font-weight: bold;
    font-size: 1.1rem;
    color: #495057;
}

.resource-details .capacity {
    font-size: 0.9rem;
    color: #6c757d;
}

.resource-details .production {
    font-size: 0.8rem;
    color: #28a745;
    font-weight: 500;
}

/* المباني الرئيسية */
.main-buildings {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.building-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.building-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.building-item.upgrading {
    border-color: #ffc107;
    background-color: #fff3cd;
}

.building-item img {
    width: 32px;
    height: 32px;
}

.building-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.building-details .name {
    font-weight: bold;
    color: #495057;
}

.building-details .level {
    font-size: 0.9rem;
    color: #007bff;
}

.building-details .status {
    font-size: 0.8rem;
    color: #ffc107;
    font-weight: 500;
}

/* خريطة القرية */
.village-map {
    margin-bottom: 30px;
}

.village-map h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.map-container {
    position: relative;
    width: 100%;
    height: 500px;
    background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%);
    border-radius: 15px;
    border: 2px solid #28a745;
    overflow: hidden;
}

/* مركز القرية */
.village-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
}

.village-center img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid #007bff;
    background-color: white;
    padding: 5px;
}

.center-info {
    background-color: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 8px;
    margin-top: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.center-info h4 {
    margin: 0 0 5px 0;
    color: #495057;
}

.center-info p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* حلقة الحقول */
.fields-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.field-slot {
    position: absolute;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.field-slot:hover {
    transform: scale(1.2);
    z-index: 5;
}

.field-slot img {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    border: 2px solid #28a745;
}

.field-slot .field-level {
    position: absolute;
    bottom: -5px;
    right: -5px;
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.field-slot .upgrade-indicator {
    position: absolute;
    top: -5px;
    left: -5px;
    background-color: #ffc107;
    color: #212529;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 1.5s infinite;
}

/* مواقع الحقول */
.field-1 { top: 10%; left: 45%; }
.field-2 { top: 15%; left: 60%; }
.field-3 { top: 25%; left: 70%; }
.field-4 { top: 40%; left: 75%; }
.field-5 { top: 55%; left: 70%; }
.field-6 { top: 70%; left: 60%; }
.field-7 { top: 80%; left: 45%; }
.field-8 { top: 85%; left: 30%; }
.field-9 { top: 80%; left: 15%; }
.field-10 { top: 70%; left: 5%; }
.field-11 { top: 55%; left: 0%; }
.field-12 { top: 40%; left: 5%; }
.field-13 { top: 25%; left: 15%; }
.field-14 { top: 15%; left: 30%; }
.field-15 { top: 20%; left: 20%; }
.field-16 { top: 30%; left: 35%; }
.field-17 { top: 50%; left: 25%; }
.field-18 { top: 65%; left: 35%; }

/* منطقة المباني */
.buildings-area {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.building-slot {
    position: absolute;
    width: 35px;
    height: 35px;
    pointer-events: all;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.building-slot:hover {
    transform: scale(1.3);
    z-index: 5;
}

.building-slot img {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    border: 2px solid #6c757d;
}

.building-slot .building-level {
    position: absolute;
    bottom: -5px;
    right: -5px;
    background-color: #6c757d;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.building-slot .upgrade-indicator {
    position: absolute;
    top: -5px;
    left: -5px;
    background-color: #ffc107;
    color: #212529;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 1.5s infinite;
}

/* مواقع المباني */
.building-main_building { top: 35%; left: 40%; }
.building-warehouse { top: 25%; left: 50%; }
.building-granary { top: 45%; left: 50%; }
.building-barracks { top: 35%; left: 30%; }
.building-stable { top: 35%; left: 60%; }
.building-marketplace { top: 55%; left: 40%; }
.building-smithy { top: 25%; left: 40%; }
.building-rally_point { top: 45%; left: 30%; }

/* الإجراءات السريعة */
.quick-actions {
    margin-bottom: 30px;
}

.quick-actions h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
    background-color: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
}

.action-btn:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    color: #007bff;
}

.action-btn img {
    width: 32px;
    height: 32px;
}

.action-btn span {
    font-weight: 500;
    text-align: center;
}

/* النموذج المنبثق */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
}

.modal-header .close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-header .close:hover {
    color: #495057;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

/* الرسوم المتحركة */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .map-container {
        height: 400px;
    }
    
    .village-center img {
        width: 60px;
        height: 60px;
    }
    
    .field-slot,
    .building-slot {
        width: 30px;
        height: 30px;
    }
    
    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .action-btn {
        padding: 15px;
    }
    
    .action-btn img {
        width: 24px;
        height: 24px;
    }
}

@media (max-width: 480px) {
    .map-container {
        height: 300px;
    }
    
    .field-slot,
    .building-slot {
        width: 25px;
        height: 25px;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
    }
    
    .modal-header,
    .modal-body {
        padding: 15px;
    }
}
