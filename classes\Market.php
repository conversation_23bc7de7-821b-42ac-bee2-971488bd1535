<?php
class Market {
    private $conn;
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    public function buyItem($userId, $itemType, $quantity) {
        $prices = [
            'wood' => 2,
            'stone' => 3,
            'food' => 1,
            'wheat_seed' => 5,
            'corn_seed' => 7,
            'rice_seed' => 6,
            'potato_seed' => 4
        ];
        
        $totalCost = $prices[$itemType] * $quantity;
        
        // التحقق من وجود ذهب كافي
        $stmt = $this->conn->prepare("SELECT gold FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user['gold'] < $totalCost) {
            return false;
        }
        
        // خصم الذهب وإضافة المورد
        $this->conn->beginTransaction();
        
        try {
            // خصم الذهب
            $stmt = $this->conn->prepare("UPDATE users SET gold = gold - ? WHERE id = ?");
            $stmt->execute([$totalCost, $userId]);
            
            // إضافة المورد
            if (in_array($itemType, ['wood', 'stone', 'food'])) {
                $stmt = $this->conn->prepare("UPDATE users SET {$itemType} = {$itemType} + ? WHERE id = ?");
                $stmt->execute([$quantity, $userId]);
            }
            
            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }
    
    public function sellItem($userId, $itemType, $quantity) {
        $prices = [
            'wood' => 1,
            'stone' => 2,
            'food' => 1,
            'wheat' => 3,
            'corn' => 4,
            'rice' => 5
        ];
        
        $totalValue = $prices[$itemType] * $quantity;
        
        $this->conn->beginTransaction();
        
        try {
            // خصم المورد
            $stmt = $this->conn->prepare("UPDATE users SET {$itemType} = {$itemType} - ? WHERE id = ? AND {$itemType} >= ?");
            $result = $stmt->execute([$quantity, $userId, $quantity]);
            
            if ($stmt->rowCount() == 0) {
                throw new Exception("Insufficient resources");
            }
            
            // إضافة الذهب
            $stmt = $this->conn->prepare("UPDATE users SET gold = gold + ? WHERE id = ?");
            $stmt->execute([$totalValue, $userId]);
            
            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }
}
?>