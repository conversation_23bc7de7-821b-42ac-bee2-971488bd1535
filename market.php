<?php
session_start();
require_once 'config/database.php';
require_once 'classes/Market.php';
require_once 'classes/User.php';

if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$db = new Database();
$market = new Market($db->getConnection());
$user = new User($db->getConnection());

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $result = $market->buyItem($_SESSION['user_id'], $_POST['item_type'], $_POST['quantity']);
    if ($result) {
        $success = "تم الشراء بنجاح!";
    } else {
        $error = "فشل في الشراء - تأكد من وجود موارد كافية";
    }
}

$userData = $user->getUserById($_SESSION['user_id']);
$marketItems = $market->getMarketItems();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>السوق - المزرعة الذهبية</title>
    <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
    <div class="game-container">
        <?php include 'templates/header.php'; ?>
        
        <div class="market-container">
            <h2>السوق التجاري</h2>
            
            <div class="market-sections">
                <div class="market-section">
                    <h3>شراء الموارد</h3>
                    <div class="market-items">
                        <div class="market-item">
                            <img src="assets/images/wood.png" alt="خشب">
                            <h4>خشب</h4>
                            <p>السعر: 2 ذهب لكل وحدة</p>
                            <form method="POST" class="inline-form">
                                <input type="hidden" name="item_type" value="wood">
                                <input type="number" name="quantity" min="1" max="1000" value="10">
                                <button type="submit">شراء</button>
                            </form>
                        </div>
                        
                        <div class="market-item">
                            <img src="assets/images/stone.png" alt="حجر">
                            <h4>حجر</h4>
                            <p>السعر: 3 ذهب لكل وحدة</p>
                            <form method="POST" class="inline-form">
                                <input type="hidden" name="item_type" value="stone">
                                <input type="number" name="quantity" min="1" max="1000" value="10">
                                <button type="submit">شراء</button>
                            </form>
                        </div>
                        
                        <div class="market-item">
                            <img src="assets/images/food.png" alt="طعام">
                            <h4>طعام</h4>
                            <p>السعر: 1 ذهب لكل وحدة</p>
                            <form method="POST" class="inline-form">
                                <input type="hidden" name="item_type" value="food">
                                <input type="number" name="quantity" min="1" max="1000" value="10">
                                <button type="submit">شراء</button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="market-section">
                    <h3>شراء البذور</h3>
                    <div class="market-items">
                        <div class="market-item">
                            <img src="assets/images/seeds/wheat_seed.png" alt="بذور قمح">
                            <h4>بذور قمح</h4>
                            <p>السعر: 5 ذهب لكل بذرة</p>
                            <form method="POST" class="inline-form">
                                <input type="hidden" name="item_type" value="wheat_seed">
                                <input type="number" name="quantity" min="1" max="100" value="1">
                                <button type="submit">شراء</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="assets/js/market.js"></script>
</body>
</html>