<?php
/**
 * صفحة الخريطة
 * Map Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];
$villageId = $_SESSION['current_village_id'];

// التحقق من القرية
$village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$villageId, $userId]);
if (!$village) {
    redirect('index.php');
}

// الحصول على إحداثيات القرية الحالية
$currentX = $village['x_coordinate'];
$currentY = $village['y_coordinate'];

// معالجة البحث
$searchX = $currentX;
$searchY = $currentY;
$searchRadius = 10;

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['search'])) {
    $searchX = intval($_POST['x_coordinate'] ?? $currentX);
    $searchY = intval($_POST['y_coordinate'] ?? $currentY);
    $searchRadius = intval($_POST['radius'] ?? 10);
    
    // التحقق من صحة الإحداثيات
    $searchX = max(-400, min(400, $searchX));
    $searchY = max(-400, min(400, $searchY));
    $searchRadius = max(5, min(50, $searchRadius));
}

// الحصول على القرى في المنطقة المحددة
$minX = $searchX - $searchRadius;
$maxX = $searchX + $searchRadius;
$minY = $searchY - $searchRadius;
$maxY = $searchY + $searchRadius;

$nearbyVillages = $db->select("
    SELECT v.*, u.username, u.tribe 
    FROM villages v 
    JOIN users u ON v.user_id = u.id 
    WHERE v.x_coordinate BETWEEN ? AND ? 
    AND v.y_coordinate BETWEEN ? AND ? 
    ORDER BY v.x_coordinate, v.y_coordinate
", [$minX, $maxX, $minY, $maxY]);

// تنظيم القرى في شبكة
$mapGrid = [];
for ($y = $minY; $y <= $maxY; $y++) {
    for ($x = $minX; $x <= $maxX; $x++) {
        $mapGrid[$y][$x] = null;
    }
}

foreach ($nearbyVillages as $v) {
    $mapGrid[$v['y_coordinate']][$v['x_coordinate']] = $v;
}

// الحصول على قرى اللاعب
$playerVillages = $db->select("SELECT * FROM villages WHERE user_id = ? ORDER BY name", [$userId]);

$csrfToken = generateCSRF();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الخريطة - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/map.css">
</head>
<body class="game-page">
    <?php include 'includes/header.php'; ?>
    
    <div class="main-content">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="content">
            <div class="page-header">
                <h1>الخريطة</h1>
                <p>استكشف العالم وابحث عن القرى والأهداف</p>
            </div>
            
            <!-- أدوات البحث -->
            <div class="search-tools">
                <form method="POST" class="search-form">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    
                    <div class="search-inputs">
                        <div class="coordinate-input">
                            <label for="x_coordinate">الإحداثي X:</label>
                            <input type="number" id="x_coordinate" name="x_coordinate" 
                                   value="<?php echo $searchX; ?>" min="-400" max="400">
                        </div>
                        
                        <div class="coordinate-input">
                            <label for="y_coordinate">الإحداثي Y:</label>
                            <input type="number" id="y_coordinate" name="y_coordinate" 
                                   value="<?php echo $searchY; ?>" min="-400" max="400">
                        </div>
                        
                        <div class="radius-input">
                            <label for="radius">نطاق البحث:</label>
                            <select id="radius" name="radius">
                                <option value="5" <?php echo $searchRadius == 5 ? 'selected' : ''; ?>>5 مربعات</option>
                                <option value="10" <?php echo $searchRadius == 10 ? 'selected' : ''; ?>>10 مربعات</option>
                                <option value="20" <?php echo $searchRadius == 20 ? 'selected' : ''; ?>>20 مربع</option>
                                <option value="30" <?php echo $searchRadius == 30 ? 'selected' : ''; ?>>30 مربع</option>
                                <option value="50" <?php echo $searchRadius == 50 ? 'selected' : ''; ?>>50 مربع</option>
                            </select>
                        </div>
                        
                        <button type="submit" name="search" class="btn btn-primary">بحث</button>
                    </div>
                </form>
                
                <div class="quick-actions">
                    <button onclick="goToCurrentVillage()" class="btn btn-secondary">قريتي الحالية</button>
                    <button onclick="goToCenter()" class="btn btn-secondary">المركز (0|0)</button>
                    <button onclick="randomLocation()" class="btn btn-secondary">موقع عشوائي</button>
                </div>
            </div>
            
            <!-- معلومات المنطقة -->
            <div class="area-info">
                <h3>المنطقة: (<?php echo $searchX; ?>|<?php echo $searchY; ?>) - نطاق <?php echo $searchRadius; ?></h3>
                <div class="area-stats">
                    <span class="stat">
                        <strong>عدد القرى:</strong> <?php echo count($nearbyVillages); ?>
                    </span>
                    <span class="stat">
                        <strong>المساحة:</strong> <?php echo ($searchRadius * 2 + 1) * ($searchRadius * 2 + 1); ?> مربع
                    </span>
                </div>
            </div>
            
            <!-- الخريطة -->
            <div class="map-container">
                <div class="map-grid" style="grid-template-columns: repeat(<?php echo ($searchRadius * 2 + 1); ?>, 1fr);">
                    <?php for ($y = $maxY; $y >= $minY; $y--): ?>
                        <?php for ($x = $minX; $x <= $maxX; $x++): ?>
                            <?php
                            $villageData = $mapGrid[$y][$x] ?? null;
                            $isCurrentVillage = $villageData && $villageData['id'] == $villageId;
                            $isPlayerVillage = $villageData && $villageData['user_id'] == $userId;
                            $isEmpty = !$villageData;
                            ?>
                            
                            <div class="map-cell <?php echo $isCurrentVillage ? 'current-village' : ($isPlayerVillage ? 'player-village' : ($isEmpty ? 'empty' : 'other-village')); ?>" 
                                 data-x="<?php echo $x; ?>" 
                                 data-y="<?php echo $y; ?>"
                                 onclick="showVillageInfo(<?php echo $x; ?>, <?php echo $y; ?>, <?php echo $villageData ? "'" . htmlspecialchars(json_encode($villageData), ENT_QUOTES) . "'" : 'null'; ?>)">
                                
                                <div class="coordinates">(<?php echo $x; ?>|<?php echo $y; ?>)</div>
                                
                                <?php if ($villageData): ?>
                                    <div class="village-info">
                                        <div class="village-name"><?php echo htmlspecialchars($villageData['name']); ?></div>
                                        <div class="village-owner"><?php echo htmlspecialchars($villageData['username']); ?></div>
                                        <div class="village-population"><?php echo formatNumber($villageData['population']); ?> نسمة</div>
                                        <div class="village-tribe tribe-<?php echo $villageData['tribe']; ?>">
                                            <?php echo getTribeName($villageData['tribe']); ?>
                                        </div>
                                    </div>
                                    
                                    <?php if ($isCurrentVillage): ?>
                                        <div class="village-marker current">🏠</div>
                                    <?php elseif ($isPlayerVillage): ?>
                                        <div class="village-marker player">🏘️</div>
                                    <?php else: ?>
                                        <div class="village-marker other">🏛️</div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <div class="empty-land">
                                        <span class="empty-icon">🌾</span>
                                        <span class="empty-text">أرض فارغة</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endfor; ?>
                    <?php endfor; ?>
                </div>
            </div>
            
            <!-- قائمة القرى -->
            <div class="villages-list">
                <h3>قرى المنطقة</h3>
                
                <?php if (!empty($nearbyVillages)): ?>
                    <div class="villages-table-container">
                        <table class="villages-table">
                            <thead>
                                <tr>
                                    <th>الإحداثيات</th>
                                    <th>اسم القرية</th>
                                    <th>اللاعب</th>
                                    <th>القبيلة</th>
                                    <th>السكان</th>
                                    <th>المسافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($nearbyVillages as $v): ?>
                                    <?php
                                    $distance = sqrt(pow($v['x_coordinate'] - $currentX, 2) + pow($v['y_coordinate'] - $currentY, 2));
                                    $isPlayerVillage = $v['user_id'] == $userId;
                                    ?>
                                    <tr class="<?php echo $isPlayerVillage ? 'player-village-row' : ''; ?>">
                                        <td class="coordinates-cell">
                                            <button onclick="centerMap(<?php echo $v['x_coordinate']; ?>, <?php echo $v['y_coordinate']; ?>)" 
                                                    class="coordinate-btn">
                                                (<?php echo $v['x_coordinate']; ?>|<?php echo $v['y_coordinate']; ?>)
                                            </button>
                                        </td>
                                        <td class="village-name-cell">
                                            <?php echo htmlspecialchars($v['name']); ?>
                                            <?php if ($v['id'] == $villageId): ?>
                                                <span class="current-badge">الحالية</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="player-cell">
                                            <?php echo htmlspecialchars($v['username']); ?>
                                            <?php if ($isPlayerVillage): ?>
                                                <span class="player-badge">أنت</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="tribe-cell">
                                            <span class="tribe-badge tribe-<?php echo $v['tribe']; ?>">
                                                <?php echo getTribeName($v['tribe']); ?>
                                            </span>
                                        </td>
                                        <td class="population-cell"><?php echo formatNumber($v['population']); ?></td>
                                        <td class="distance-cell"><?php echo number_format($distance, 1); ?></td>
                                        <td class="actions-cell">
                                            <?php if (!$isPlayerVillage): ?>
                                                <a href="attack.php?target=<?php echo $v['id']; ?>" class="btn btn-small btn-danger">هجوم</a>
                                                <a href="trade.php?target=<?php echo $v['id']; ?>" class="btn btn-small btn-success">تجارة</a>
                                            <?php else: ?>
                                                <a href="change-village.php?village_id=<?php echo $v['id']; ?>" class="btn btn-small btn-primary">انتقال</a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="no-villages">
                        <p>لا توجد قرى في هذه المنطقة</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- قرى اللاعب -->
            <div class="player-villages">
                <h3>قراي</h3>
                <div class="villages-grid">
                    <?php foreach ($playerVillages as $pv): ?>
                        <div class="village-card <?php echo $pv['id'] == $villageId ? 'current' : ''; ?>">
                            <div class="village-header">
                                <h4><?php echo htmlspecialchars($pv['name']); ?></h4>
                                <?php if ($pv['is_capital']): ?>
                                    <span class="capital-badge">العاصمة</span>
                                <?php endif; ?>
                                <?php if ($pv['id'] == $villageId): ?>
                                    <span class="current-badge">الحالية</span>
                                <?php endif; ?>
                            </div>
                            <div class="village-details">
                                <div class="detail">
                                    <span class="label">الإحداثيات:</span>
                                    <span class="value">(<?php echo $pv['x_coordinate']; ?>|<?php echo $pv['y_coordinate']; ?>)</span>
                                </div>
                                <div class="detail">
                                    <span class="label">السكان:</span>
                                    <span class="value"><?php echo formatNumber($pv['population']); ?></span>
                                </div>
                            </div>
                            <div class="village-actions">
                                <?php if ($pv['id'] != $villageId): ?>
                                    <a href="change-village.php?village_id=<?php echo $pv['id']; ?>" class="btn btn-small btn-primary">انتقال</a>
                                <?php endif; ?>
                                <button onclick="centerMap(<?php echo $pv['x_coordinate']; ?>, <?php echo $pv['y_coordinate']; ?>)" 
                                        class="btn btn-small btn-secondary">عرض</button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </main>
    </div>
    
    <!-- نافذة معلومات القرية -->
    <div id="village-info-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">معلومات القرية</h3>
                <button class="close" onclick="closeVillageInfo()">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- سيتم ملء المحتوى بواسطة JavaScript -->
            </div>
        </div>
    </div>
    
    <script>
        function showVillageInfo(x, y, villageData) {
            const modal = document.getElementById('village-info-modal');
            const title = document.getElementById('modal-title');
            const body = document.getElementById('modal-body');
            
            if (villageData) {
                const village = JSON.parse(villageData);
                title.textContent = village.name;
                
                body.innerHTML = `
                    <div class="village-info-content">
                        <div class="info-row">
                            <span class="label">الإحداثيات:</span>
                            <span class="value">(${x}|${y})</span>
                        </div>
                        <div class="info-row">
                            <span class="label">اللاعب:</span>
                            <span class="value">${village.username}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">القبيلة:</span>
                            <span class="value">${getTribeNameJS(village.tribe)}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">السكان:</span>
                            <span class="value">${formatNumber(village.population)}</span>
                        </div>
                        ${village.user_id != <?php echo $userId; ?> ? `
                            <div class="action-buttons">
                                <a href="attack.php?target=${village.id}" class="btn btn-danger">هجوم</a>
                                <a href="trade.php?target=${village.id}" class="btn btn-success">تجارة</a>
                            </div>
                        ` : `
                            <div class="action-buttons">
                                <a href="change-village.php?village_id=${village.id}" class="btn btn-primary">انتقال</a>
                            </div>
                        `}
                    </div>
                `;
            } else {
                title.textContent = `أرض فارغة (${x}|${y})`;
                body.innerHTML = `
                    <div class="empty-land-info">
                        <p>هذه أرض فارغة يمكن إنشاء قرية جديدة عليها.</p>
                        <div class="action-buttons">
                            <a href="settle.php?x=${x}&y=${y}" class="btn btn-primary">إنشاء قرية</a>
                        </div>
                    </div>
                `;
            }
            
            modal.style.display = 'flex';
        }
        
        function closeVillageInfo() {
            document.getElementById('village-info-modal').style.display = 'none';
        }
        
        function centerMap(x, y) {
            document.getElementById('x_coordinate').value = x;
            document.getElementById('y_coordinate').value = y;
            document.querySelector('.search-form').submit();
        }
        
        function goToCurrentVillage() {
            centerMap(<?php echo $currentX; ?>, <?php echo $currentY; ?>);
        }
        
        function goToCenter() {
            centerMap(0, 0);
        }
        
        function randomLocation() {
            const x = Math.floor(Math.random() * 801) - 400; // -400 to 400
            const y = Math.floor(Math.random() * 801) - 400;
            centerMap(x, y);
        }
        
        function getTribeNameJS(tribe) {
            const tribes = {
                'gauls': 'الغاليون',
                'romans': 'الرومان',
                'teutons': 'التيوتون'
            };
            return tribes[tribe] || tribe;
        }
        
        function formatNumber(num) {
            return new Intl.NumberFormat('ar-SA').format(num);
        }
        
        // إغلاق النافذة عند النقر خارجها
        document.getElementById('village-info-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeVillageInfo();
            }
        });
        
        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeVillageInfo();
            }
        });
    </script>
</body>
</html>

<?php
function getTribeName($tribe) {
    $tribes = [
        'gauls' => 'الغاليون',
        'romans' => 'الرومان',
        'teutons' => 'التيوتون'
    ];
    return $tribes[$tribe] ?? $tribe;
}
?>
