<?php
/**
 * معالجة التجارة التلقائية
 * Automatic Trade Processing
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مسموح']);
    exit;
}

$db = getDB();

try {
    // الحصول على التجارة التي وصلت
    $arrivedTrades = $db->select("
        SELECT * FROM trades 
        WHERE status = 'in_transit' AND arrival_time <= NOW()
    ");
    
    $processedCount = 0;
    
    foreach ($arrivedTrades as $trade) {
        $db->beginTransaction();
        
        try {
            // إضافة الموارد للقرية المستقبلة
            $db->update("
                UPDATE resources 
                SET wood = wood + ?, clay = clay + ?, iron = iron + ?, crop = crop + ? 
                WHERE village_id = ?
            ", [
                $trade['wood_amount'],
                $trade['clay_amount'], 
                $trade['iron_amount'],
                $trade['crop_amount'],
                $trade['receiver_village_id']
            ]);
            
            // تحديث حالة التجارة
            $db->update("
                UPDATE trades 
                SET status = 'completed', completed_at = NOW() 
                WHERE id = ?
            ", [$trade['id']]);
            
            // إنشاء تقرير تجارة للمرسل
            createTradeReport(
                $trade['sender_village_id'],
                $trade['receiver_village_id'],
                $trade,
                'delivered'
            );
            
            // إنشاء تقرير تجارة للمستقبل
            createTradeReport(
                $trade['receiver_village_id'],
                $trade['sender_village_id'],
                $trade,
                'received'
            );
            
            $db->commit();
            $processedCount++;
            
        } catch (Exception $e) {
            $db->rollback();
            error_log("Error processing trade {$trade['id']}: " . $e->getMessage());
        }
    }
    
    echo json_encode([
        'success' => true,
        'processed' => $processedCount,
        'message' => "تم معالجة {$processedCount} تجارة"
    ]);
    
} catch (Exception $e) {
    error_log("Error in process-trades.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ في معالجة التجارة'
    ]);
}

function createTradeReport($villageId, $partnerVillageId, $trade, $type) {
    $db = getDB();
    
    // الحصول على معلومات القرى
    $village = $db->selectOne("SELECT v.*, u.username FROM villages v JOIN users u ON v.user_id = u.id WHERE v.id = ?", [$villageId]);
    $partnerVillage = $db->selectOne("SELECT v.*, u.username FROM villages v JOIN users u ON v.user_id = u.id WHERE v.id = ?", [$partnerVillageId]);
    
    if (!$village || !$partnerVillage) {
        return;
    }
    
    $reportData = [
        'trade_type' => $type,
        'resources' => [
            'wood' => $trade['wood_amount'],
            'clay' => $trade['clay_amount'],
            'iron' => $trade['iron_amount'],
            'crop' => $trade['crop_amount']
        ],
        'partner_village' => [
            'name' => $partnerVillage['name'],
            'username' => $partnerVillage['username'],
            'coordinates' => [
                'x' => $partnerVillage['x_coordinate'],
                'y' => $partnerVillage['y_coordinate']
            ]
        ],
        'trade_time' => [
            'sent' => $trade['created_at'],
            'arrived' => date('Y-m-d H:i:s')
        ]
    ];
    
    // تحديد نوع التقرير والرسالة
    if ($type === 'delivered') {
        $reportType = 'trade_sent';
        $title = "تم تسليم التجارة إلى {$partnerVillage['name']}";
    } else {
        $reportType = 'trade_received';
        $title = "تم استلام تجارة من {$partnerVillage['name']}";
    }
    
    // إنشاء التقرير
    $db->insert("
        INSERT INTO reports (
            attacker_village_id, 
            defender_village_id, 
            report_type, 
            result, 
            report_data,
            title
        ) VALUES (?, ?, ?, ?, ?, ?)
    ", [
        $villageId,
        $partnerVillageId,
        $reportType,
        'success',
        json_encode($reportData),
        $title
    ]);
}
?>
