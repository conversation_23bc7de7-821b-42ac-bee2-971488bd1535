<?php
/**
 * ملف تشخيص المشاكل
 * Debug and Troubleshooting File
 */

echo "<h1>🔧 تشخيص مشاكل اللعبة</h1>";

// 1. فحص PHP
echo "<h2>1. فحص PHP</h2>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "PDO متوفر: " . (class_exists('PDO') ? '✅ نعم' : '❌ لا') . "<br>";
echo "MySQL PDO متوفر: " . (extension_loaded('pdo_mysql') ? '✅ نعم' : '❌ لا') . "<br>";

// 2. فحص الملفات
echo "<h2>2. فحص الملفات الأساسية</h2>";
$files = [
    'config/config.php',
    'config/database.php', 
    'includes/functions.php',
    'database/database.sql'
];

foreach ($files as $file) {
    echo "ملف $file: " . (file_exists($file) ? '✅ موجود' : '❌ مفقود') . "<br>";
}

// 3. فحص المجلدات
echo "<h2>3. فحص المجلدات</h2>";
$folders = [
    'images',
    'images/buildings',
    'images/units', 
    'images/resources',
    'uploads',
    'logs',
    'cache'
];

foreach ($folders as $folder) {
    $exists = is_dir($folder);
    $writable = $exists ? is_writable($folder) : false;
    echo "مجلد $folder: " . ($exists ? '✅ موجود' : '❌ مفقود');
    if ($exists) {
        echo " | " . ($writable ? '✅ قابل للكتابة' : '❌ غير قابل للكتابة');
    }
    echo "<br>";
}

// 4. فحص قاعدة البيانات
echo "<h2>4. فحص قاعدة البيانات</h2>";
try {
    require_once 'config/config.php';
    
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ الاتصال بقاعدة البيانات ناجح<br>";
    
    // فحص الجداول
    $tables = ['users', 'villages', 'building_types', 'unit_types'];
    foreach ($tables as $table) {
        try {
            $result = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            echo "جدول $table: ✅ موجود ($result سجل)<br>";
        } catch (Exception $e) {
            echo "جدول $table: ❌ غير موجود أو خطأ<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br>";
    echo "<strong>الحلول المقترحة:</strong><br>";
    echo "1. تأكد من تشغيل خدمة MySQL<br>";
    echo "2. تحقق من بيانات الاتصال في config/config.php<br>";
    echo "3. أنشئ قاعدة البيانات باستخدام setup-database.php<br>";
}

// 5. فحص الجلسات
echo "<h2>5. فحص الجلسات</h2>";
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
echo "حالة الجلسة: " . (session_status() == PHP_SESSION_ACTIVE ? '✅ نشطة' : '❌ غير نشطة') . "<br>";
echo "معرف الجلسة: " . session_id() . "<br>";

// 6. فحص الأذونات
echo "<h2>6. فحص الأذونات</h2>";
$testFile = 'test_write.txt';
if (file_put_contents($testFile, 'test')) {
    echo "✅ الكتابة في المجلد الرئيسي ممكنة<br>";
    unlink($testFile);
} else {
    echo "❌ لا يمكن الكتابة في المجلد الرئيسي<br>";
}

// 7. روابط مفيدة
echo "<h2>7. روابط مفيدة</h2>";
echo "<a href='setup-database.php' style='background: #4CAF50; color: white; padding: 10px; text-decoration: none; margin: 5px; border-radius: 5px;'>🗄️ إعداد قاعدة البيانات</a><br><br>";
echo "<a href='test-integration.php' style='background: #2196F3; color: white; padding: 10px; text-decoration: none; margin: 5px; border-radius: 5px;'>🧪 اختبار التكامل</a><br><br>";
echo "<a href='register.php' style='background: #FF9800; color: white; padding: 10px; text-decoration: none; margin: 5px; border-radius: 5px;'>📝 تسجيل حساب</a><br><br>";
echo "<a href='login.php' style='background: #9C27B0; color: white; padding: 10px; text-decoration: none; margin: 5px; border-radius: 5px;'>🔐 تسجيل الدخول</a><br><br>";

// 8. معلومات النظام
echo "<h2>8. معلومات النظام</h2>";
echo "نظام التشغيل: " . PHP_OS . "<br>";
echo "الخادم: " . $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد' . "<br>";
echo "المجلد الحالي: " . __DIR__ . "<br>";
echo "الوقت الحالي: " . date('Y-m-d H:i:s') . "<br>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f5f5;
    margin: 0;
    padding: 20px;
    direction: rtl;
}

h1 {
    color: #2c3e50;
    text-align: center;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h2 {
    color: #34495e;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
    margin-top: 30px;
}

a {
    display: inline-block;
    margin: 5px;
}
</style>
