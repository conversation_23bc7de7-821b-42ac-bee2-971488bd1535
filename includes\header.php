<?php
/**
 * ملف الهيدر المشترك
 * Shared Header File
 */

if (!isLoggedIn()) {
    redirect('login.php');
}

$db = getDB();
$userId = $_SESSION['user_id'];

// الحصول على بيانات المستخدم
$user = $db->selectOne("SELECT * FROM users WHERE id = ?", [$userId]);
if (!$user) {
    redirect('logout.php');
}

// الحصول على القرية الحالية
$currentVillageId = $_SESSION['current_village_id'] ?? null;
if (!$currentVillageId) {
    $village = $db->selectOne("SELECT * FROM villages WHERE user_id = ? AND is_capital = 1", [$userId]);
    if (!$village) {
        $village = $db->selectOne("SELECT * FROM villages WHERE user_id = ? ORDER BY id ASC LIMIT 1", [$userId]);
    }
    if ($village) {
        $currentVillageId = $village['id'];
        $_SESSION['current_village_id'] = $currentVillageId;
    }
} else {
    $village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$currentVillageId, $userId]);
}

// تحديث الموارد
if ($village) {
    updateVillageResources($village['id']);
    $resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$village['id']]);
    $production = calculateResourceProduction($village['id']);
}

// الحصول على الرسائل غير المقروءة
$unreadMessages = $db->selectOne("SELECT COUNT(*) as count FROM messages WHERE receiver_id = ? AND is_read = 0", [$userId]);

// الحصول على قائمة القرى
$userVillages = $db->select("SELECT * FROM villages WHERE user_id = ? ORDER BY is_capital DESC, name ASC", [$userId]);
?>

<!-- شريط التنقل العلوي -->
<nav class="top-nav">
    <div class="nav-left">
        <div class="logo">
            <img src="images/logo.png" alt="<?php echo SITE_NAME; ?>">
            <span><?php echo SITE_NAME; ?></span>
        </div>
    </div>
    
    <div class="nav-center">
        <?php if (!empty($userVillages)): ?>
            <div class="village-selector">
                <select id="village-select" onchange="changeVillage(this.value)">
                    <?php foreach ($userVillages as $v): ?>
                        <option value="<?php echo $v['id']; ?>" <?php echo ($v['id'] == $currentVillageId) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($v['name']); ?>
                            <?php if ($v['is_capital']): ?>(العاصمة)<?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="nav-right">
        <div class="user-info">
            <span class="username"><?php echo htmlspecialchars($user['username']); ?></span>
            <span class="gold">
                <img src="images/icons/gold.png" alt="ذهب">
                <?php echo formatNumber($user['gold']); ?>
            </span>
            <a href="messages.php" class="messages-link">
                <img src="images/icons/message.png" alt="رسائل">
                <?php if ($unreadMessages['count'] > 0): ?>
                    <span class="badge"><?php echo $unreadMessages['count']; ?></span>
                <?php endif; ?>
            </a>
            <a href="logout.php" class="logout-link">خروج</a>
        </div>
    </div>
</nav>

<!-- شريط الموارد -->
<?php if (isset($resources) && isset($production)): ?>
<div class="resources-bar">
    <div class="resource">
        <img src="images/resources/wood.png" alt="خشب">
        <span class="amount" id="wood-amount"><?php echo formatNumber($resources['wood']); ?></span>
        <span class="production">+<?php echo formatNumber($production['wood']); ?>/ساعة</span>
    </div>
    <div class="resource">
        <img src="images/resources/clay.png" alt="طين">
        <span class="amount" id="clay-amount"><?php echo formatNumber($resources['clay']); ?></span>
        <span class="production">+<?php echo formatNumber($production['clay']); ?>/ساعة</span>
    </div>
    <div class="resource">
        <img src="images/resources/iron.png" alt="حديد">
        <span class="amount" id="iron-amount"><?php echo formatNumber($resources['iron']); ?></span>
        <span class="production">+<?php echo formatNumber($production['iron']); ?>/ساعة</span>
    </div>
    <div class="resource">
        <img src="images/resources/crop.png" alt="قمح">
        <span class="amount" id="crop-amount"><?php echo formatNumber($resources['crop']); ?></span>
        <span class="production">+<?php echo formatNumber($production['crop']); ?>/ساعة</span>
    </div>
    <div class="population">
        <img src="images/icons/population.png" alt="سكان">
        <span><?php echo $village['population']; ?></span>
    </div>
</div>
<?php endif; ?>

<script>
function changeVillage(villageId) {
    if (villageId) {
        window.location.href = 'change-village.php?id=' + villageId + '&redirect=' + encodeURIComponent(window.location.pathname);
    }
}

// تحديث الموارد كل دقيقة
<?php if (isset($village)): ?>
setInterval(function() {
    updateResources();
}, 60000);

function updateResources() {
    fetch('ajax/update-resources.php?village_id=<?php echo $village['id']; ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('wood-amount').textContent = formatNumber(data.resources.wood);
                document.getElementById('clay-amount').textContent = formatNumber(data.resources.clay);
                document.getElementById('iron-amount').textContent = formatNumber(data.resources.iron);
                document.getElementById('crop-amount').textContent = formatNumber(data.resources.crop);
            }
        })
        .catch(error => console.error('Error updating resources:', error));
}

function formatNumber(num) {
    return new Intl.NumberFormat('ar-SA').format(num);
}
<?php endif; ?>
</script>
