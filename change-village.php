<?php
/**
 * تغيير القرية الحالية
 * Change Current Village
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$villageId = intval($_GET['id'] ?? 0);
$userId = $_SESSION['user_id'];

if ($villageId <= 0) {
    redirect('index.php');
}

$db = getDB();

// التحقق من أن القرية تنتمي للمستخدم
$village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$villageId, $userId]);

if (!$village) {
    showError('القرية غير موجودة أو لا تنتمي لك');
    redirect('index.php');
}

// تحديث القرية الحالية في الجلسة
$_SESSION['current_village_id'] = $villageId;

// إعادة التوجيه إلى الصفحة المطلوبة أو الصفحة الرئيسية
$redirectTo = $_GET['redirect'] ?? 'index.php';
redirect($redirectTo);
?>
