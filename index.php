<?php
/**
 * الصفحة الرئيسية للعبة
 * Main Game Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];

// الحصول على بيانات المستخدم
$user = $db->selectOne("SELECT * FROM users WHERE id = ?", [$userId]);
if (!$user) {
    redirect('logout.php');
}

// الحصول على القرية الحالية (العاصمة أو آخر قرية تم زيارتها)
$currentVillageId = $_SESSION['current_village_id'] ?? null;

if (!$currentVillageId) {
    $village = $db->selectOne("SELECT * FROM villages WHERE user_id = ? AND is_capital = 1", [$userId]);
    if (!$village) {
        $village = $db->selectOne("SELECT * FROM villages WHERE user_id = ? ORDER BY id ASC LIMIT 1", [$userId]);
    }
    if ($village) {
        $currentVillageId = $village['id'];
        $_SESSION['current_village_id'] = $currentVillageId;
    }
} else {
    $village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$currentVillageId, $userId]);
}

if (!$village) {
    showError('لم يتم العثور على قرية. يرجى الاتصال بالإدارة.');
    redirect('logout.php');
}

// تحديث موارد القرية
updateVillageResources($village['id']);

// الحصول على الموارد المحدثة
$resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$village['id']]);

// الحصول على المباني
$buildings = $db->select("SELECT * FROM buildings WHERE village_id = ?", [$village['id']]);
$buildingsArray = [];
foreach ($buildings as $building) {
    $buildingsArray[$building['building_type']] = $building;
}

// الحصول على الحقول
$fields = $db->select("SELECT * FROM fields WHERE village_id = ? ORDER BY position", [$village['id']]);

// حساب الإنتاج
$production = calculateResourceProduction($village['id']);

// الحصول على الوحدات العسكرية
$units = $db->select("SELECT * FROM units WHERE village_id = ?", [$village['id']]);
$unitsArray = [];
foreach ($units as $unit) {
    $unitsArray[$unit['unit_type']] = $unit;
}

// الحصول على الرسائل غير المقروءة
$unreadMessages = $db->selectOne("SELECT COUNT(*) as count FROM messages WHERE receiver_id = ? AND is_read = 0", [$userId]);

// الحصول على الهجمات الواردة
$incomingAttacks = $db->select("SELECT * FROM attacks WHERE target_village_id = ? AND is_completed = 0 ORDER BY arrival_time ASC", [$village['id']]);

// الحصول على قائمة القرى
$userVillages = $db->select("SELECT * FROM villages WHERE user_id = ? ORDER BY is_capital DESC, name ASC", [$userId]);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $village['name']; ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <script src="js/game.js"></script>
</head>
<body class="game-page">
    <!-- شريط التنقل العلوي -->
    <nav class="top-nav">
        <div class="nav-left">
            <div class="logo">
                <img src="images/logo.png" alt="<?php echo SITE_NAME; ?>">
                <span><?php echo SITE_NAME; ?></span>
            </div>
        </div>
        
        <div class="nav-center">
            <div class="village-selector">
                <select id="village-select" onchange="changeVillage(this.value)">
                    <?php foreach ($userVillages as $v): ?>
                        <option value="<?php echo $v['id']; ?>" <?php echo ($v['id'] == $village['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($v['name']); ?>
                            <?php if ($v['is_capital']): ?>(العاصمة)<?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        
        <div class="nav-right">
            <div class="user-info">
                <span class="username"><?php echo htmlspecialchars($user['username']); ?></span>
                <span class="gold">
                    <img src="images/icons/gold.png" alt="ذهب">
                    <?php echo formatNumber($user['gold']); ?>
                </span>
                <a href="messages.php" class="messages-link">
                    <img src="images/icons/message.png" alt="رسائل">
                    <?php if ($unreadMessages['count'] > 0): ?>
                        <span class="badge"><?php echo $unreadMessages['count']; ?></span>
                    <?php endif; ?>
                </a>
                <a href="logout.php" class="logout-link">خروج</a>
            </div>
        </div>
    </nav>
    
    <!-- شريط الموارد -->
    <div class="resources-bar">
        <div class="resource">
            <img src="images/resources/wood.png" alt="خشب">
            <span class="amount" id="wood-amount"><?php echo formatNumber($resources['wood']); ?></span>
            <span class="production">+<?php echo formatNumber($production['wood']); ?>/ساعة</span>
        </div>
        <div class="resource">
            <img src="images/resources/clay.png" alt="طين">
            <span class="amount" id="clay-amount"><?php echo formatNumber($resources['clay']); ?></span>
            <span class="production">+<?php echo formatNumber($production['clay']); ?>/ساعة</span>
        </div>
        <div class="resource">
            <img src="images/resources/iron.png" alt="حديد">
            <span class="amount" id="iron-amount"><?php echo formatNumber($resources['iron']); ?></span>
            <span class="production">+<?php echo formatNumber($production['iron']); ?>/ساعة</span>
        </div>
        <div class="resource">
            <img src="images/resources/crop.png" alt="قمح">
            <span class="amount" id="crop-amount"><?php echo formatNumber($resources['crop']); ?></span>
            <span class="production">+<?php echo formatNumber($production['crop']); ?>/ساعة</span>
        </div>
        <div class="population">
            <img src="images/icons/population.png" alt="سكان">
            <span><?php echo $village['population']; ?></span>
        </div>
    </div>
    
    <!-- التنبيهات -->
    <?php if (!empty($incomingAttacks)): ?>
        <div class="alerts">
            <div class="alert alert-danger">
                <strong>تحذير!</strong> هناك <?php echo count($incomingAttacks); ?> هجمات واردة على قريتك!
                <a href="attacks.php">عرض التفاصيل</a>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- القائمة الجانبية -->
        <aside class="sidebar">
            <nav class="game-nav">
                <ul>
                    <li><a href="index.php" class="active">نظرة عامة</a></li>
                    <li><a href="village.php">القرية</a></li>
                    <li><a href="buildings.php">المباني</a></li>
                    <li><a href="fields.php">الحقول</a></li>
                    <li><a href="army.php">الجيش</a></li>
                    <li><a href="marketplace.php">السوق</a></li>
                    <li><a href="reports.php">التقارير</a></li>
                    <li><a href="alliance.php">التحالف</a></li>
                    <li><a href="map.php">الخريطة</a></li>
                    <li><a href="statistics.php">الإحصائيات</a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- المحتوى -->
        <main class="content">
            <div class="page-header">
                <h1><?php echo htmlspecialchars($village['name']); ?></h1>
                <p>الإحداثيات: (<?php echo $village['x_coordinate']; ?>|<?php echo $village['y_coordinate']; ?>)</p>
            </div>
            
            <!-- نظرة عامة على القرية -->
            <div class="overview-grid">
                <!-- المباني -->
                <div class="overview-section">
                    <h3>المباني</h3>
                    <div class="buildings-overview">
                        <?php
                        $importantBuildings = ['main_building', 'warehouse', 'granary', 'barracks', 'stable'];
                        foreach ($importantBuildings as $buildingType):
                            $building = $buildingsArray[$buildingType] ?? null;
                            $level = $building ? $building['level'] : 0;
                        ?>
                            <div class="building-item">
                                <img src="images/buildings/<?php echo $buildingType; ?>.png" alt="<?php echo BUILDING_TYPES[$buildingType]; ?>">
                                <span><?php echo BUILDING_TYPES[$buildingType]; ?></span>
                                <span class="level">المستوى <?php echo $level; ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <a href="buildings.php" class="btn btn-small">عرض جميع المباني</a>
                </div>
                
                <!-- الجيش -->
                <div class="overview-section">
                    <h3>الجيش</h3>
                    <div class="army-overview">
                        <?php
                        $totalUnits = 0;
                        foreach ($units as $unit) {
                            $totalUnits += $unit['count'];
                        }
                        ?>
                        <p>إجمالي الوحدات: <strong><?php echo formatNumber($totalUnits); ?></strong></p>
                        
                        <?php if (!empty($units)): ?>
                            <?php foreach (array_slice($units, 0, 3) as $unit): ?>
                                <?php if ($unit['count'] > 0): ?>
                                    <div class="unit-item">
                                        <img src="images/units/<?php echo $unit['unit_type']; ?>.png" alt="<?php echo UNIT_TYPES[$unit['unit_type']]; ?>">
                                        <span><?php echo UNIT_TYPES[$unit['unit_type']]; ?></span>
                                        <span class="count"><?php echo formatNumber($unit['count']); ?></span>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p>لا توجد وحدات عسكرية</p>
                        <?php endif; ?>
                    </div>
                    <a href="army.php" class="btn btn-small">إدارة الجيش</a>
                </div>
                
                <!-- الأحداث الأخيرة -->
                <div class="overview-section">
                    <h3>الأحداث الأخيرة</h3>
                    <div class="events-overview">
                        <?php
                        // الحصول على آخر الأحداث (رسائل النظام)
                        $recentEvents = $db->select("SELECT * FROM messages WHERE receiver_id = ? AND message_type = 'system' ORDER BY created_at DESC LIMIT 5", [$userId]);
                        ?>
                        
                        <?php if (!empty($recentEvents)): ?>
                            <?php foreach ($recentEvents as $event): ?>
                                <div class="event-item">
                                    <span class="time"><?php echo timeAgo($event['created_at']); ?></span>
                                    <span class="message"><?php echo htmlspecialchars($event['subject']); ?></span>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p>لا توجد أحداث حديثة</p>
                        <?php endif; ?>
                    </div>
                    <a href="messages.php" class="btn btn-small">عرض جميع الرسائل</a>
                </div>
                
                <!-- الإحصائيات -->
                <div class="overview-section">
                    <h3>إحصائيات سريعة</h3>
                    <div class="stats-overview">
                        <div class="stat-item">
                            <span class="label">عدد القرى:</span>
                            <span class="value"><?php echo count($userVillages); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="label">إجمالي السكان:</span>
                            <span class="value"><?php echo formatNumber(array_sum(array_column($userVillages, 'population'))); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="label">نقاط القوة:</span>
                            <span class="value"><?php echo formatNumber($user['population'] * 10); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="label">الترتيب:</span>
                            <span class="value">#<?php echo rand(1, 1000); ?></span>
                        </div>
                    </div>
                    <a href="statistics.php" class="btn btn-small">إحصائيات مفصلة</a>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // تحديث الموارد كل دقيقة
        setInterval(updateResources, 60000);
        
        function updateResources() {
            fetch('ajax/update-resources.php?village_id=<?php echo $village['id']; ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('wood-amount').textContent = formatNumber(data.resources.wood);
                        document.getElementById('clay-amount').textContent = formatNumber(data.resources.clay);
                        document.getElementById('iron-amount').textContent = formatNumber(data.resources.iron);
                        document.getElementById('crop-amount').textContent = formatNumber(data.resources.crop);
                    }
                })
                .catch(error => console.error('Error updating resources:', error));
        }
        
        function changeVillage(villageId) {
            window.location.href = 'change-village.php?id=' + villageId;
        }
        
        function formatNumber(num) {
            return new Intl.NumberFormat('ar-SA').format(num);
        }
    </script>
</body>
</html>
