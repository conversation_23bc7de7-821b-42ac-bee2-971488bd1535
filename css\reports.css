/* ملف CSS للتقارير */
/* Reports CSS */

/* إحصائيات التقارير */
.reports-stats {
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-card.total {
    border-color: #007bff;
}

.stat-card.attacks {
    border-color: #dc3545;
}

.stat-card.defenses {
    border-color: #28a745;
}

.stat-card.trades {
    border-color: #ffc107;
}

.stat-card.unread {
    border-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.stat-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0,0,0,0.1);
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #495057;
    line-height: 1;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* أدوات التحكم */
.reports-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    flex-wrap: wrap;
    gap: 15px;
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-controls label {
    font-weight: 500;
    color: #495057;
}

.filter-controls select {
    padding: 8px 12px;
    border: 2px solid #ced4da;
    border-radius: 6px;
    font-size: 1rem;
    background-color: white;
}

.filter-controls select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* قائمة التقارير */
.reports-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.report-item {
    background-color: white;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.report-item:hover {
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.report-item.unread {
    border-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #f8f9fa 100%);
}

.report-item.unread .report-header {
    background-color: rgba(23, 162, 184, 0.1);
}

.report-item.attack {
    border-left: 5px solid #dc3545;
}

.report-item.defense {
    border-left: 5px solid #28a745;
}

.report-item.trade {
    border-left: 5px solid #ffc107;
}

.report-item.scout {
    border-left: 5px solid #6f42c1;
}

/* رأس التقرير */
.report-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    transition: background-color 0.3s ease;
}

.report-header:hover {
    background-color: #f8f9fa;
}

.report-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0,0,0,0.1);
}

.report-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.report-title {
    font-weight: bold;
    font-size: 1.1rem;
    color: #495057;
}

.report-participants {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: #6c757d;
    flex-wrap: wrap;
}

.report-participants .attacker {
    color: #dc3545;
    font-weight: 500;
}

.report-participants .defender {
    color: #28a745;
    font-weight: 500;
}

.report-participants .vs {
    color: #6c757d;
    font-style: italic;
}

.report-time {
    font-size: 0.8rem;
    color: #6c757d;
}

.report-result {
    display: flex;
    align-items: center;
    margin-left: 15px;
}

.result {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.result.victory {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.result.defeat {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.result.draw {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.result.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.result.failed {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.report-toggle {
    display: flex;
    align-items: center;
    margin-left: 10px;
}

.toggle-icon {
    font-size: 1.2rem;
    color: #6c757d;
    transition: transform 0.3s ease;
}

.report-item:hover .toggle-icon {
    color: #007bff;
}

/* تفاصيل التقرير */
.report-details {
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.details-content {
    padding: 20px;
}

.report-summary h4 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.battle-summary,
.trade-summary {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.battle-summary p,
.trade-summary p {
    margin: 0;
    color: #495057;
}

.battle-summary ul,
.trade-summary ul {
    margin: 5px 0 0 20px;
    padding: 0;
}

.battle-summary li,
.trade-summary li {
    color: #6c757d;
    margin-bottom: 3px;
}

/* التنقل بين الصفحات */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
    padding: 20px;
}

.page-btn {
    padding: 8px 15px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    text-decoration: none;
    color: #495057;
    font-weight: 500;
    transition: all 0.3s ease;
}

.page-btn:hover {
    border-color: #007bff;
    background-color: #007bff;
    color: white;
}

.page-btn.active {
    border-color: #007bff;
    background-color: #007bff;
    color: white;
}

/* عدم وجود تقارير */
.no-reports {
    text-align: center;
    padding: 60px 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
}

.no-reports-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-reports h3 {
    color: #6c757d;
    margin-bottom: 10px;
}

.no-reports p {
    color: #6c757d;
    font-size: 1.1rem;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-card {
        padding: 15px;
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .reports-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-controls {
        justify-content: center;
    }
    
    .report-header {
        padding: 15px;
        flex-wrap: wrap;
    }
    
    .report-participants {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .report-result {
        margin-left: 0;
        margin-top: 10px;
    }
    
    .details-content {
        padding: 15px;
    }
    
    .pagination {
        flex-wrap: wrap;
    }
    
    .page-btn {
        padding: 6px 12px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 12px;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .stat-value {
        font-size: 1.3rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
    
    .report-header {
        padding: 12px;
        gap: 10px;
    }
    
    .report-icon {
        width: 40px;
        height: 40px;
        font-size: 1.5rem;
    }
    
    .report-title {
        font-size: 1rem;
    }
    
    .report-participants {
        font-size: 0.8rem;
    }
    
    .report-time {
        font-size: 0.7rem;
    }
    
    .result {
        padding: 3px 8px;
        font-size: 0.7rem;
    }
    
    .details-content {
        padding: 12px;
    }
    
    .no-reports {
        padding: 40px 15px;
    }
    
    .no-reports-icon {
        font-size: 3rem;
    }
    
    .no-reports h3 {
        font-size: 1.2rem;
    }
    
    .no-reports p {
        font-size: 1rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .reports-controls,
    .pagination {
        display: none;
    }
    
    .report-item {
        border: 1px solid #000;
        margin-bottom: 20px;
        break-inside: avoid;
    }
    
    .report-details {
        display: block !important;
    }
    
    .toggle-icon {
        display: none;
    }
    
    .stat-card {
        border: 1px solid #000;
    }
}

/* تحسينات للوضع المظلم (إذا تم تطبيقه لاحقاً) */
@media (prefers-color-scheme: dark) {
    .report-item {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .report-header:hover {
        background-color: #3d3d3d;
    }
    
    .report-details {
        background-color: #3d3d3d;
        border-top-color: #444;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
        border-color: #444;
        color: #fff;
    }
    
    .reports-controls {
        background-color: #2d2d2d;
        border-color: #444;
    }
    
    .no-reports {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
}
