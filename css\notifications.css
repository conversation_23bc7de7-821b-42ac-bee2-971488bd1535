/* ملف CSS للإشعارات والتنبيهات */
/* Notifications CSS */

/* حاوية الإشعارات */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    width: 100%;
    pointer-events: none;
}

/* الإشعار الفردي */
.notification {
    background-color: white;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border-left: 4px solid #007bff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    animation: slideIn 0.3s ease-out;
    pointer-events: auto;
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, rgba(0,0,0,0.1), transparent);
    animation: progress 5s linear;
}

/* أنواع الإشعارات */
.notification.success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.notification.error {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.notification.warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.notification.info {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

/* رسالة الإشعار */
.notification-message {
    flex: 1;
    color: #495057;
    font-weight: 500;
    font-size: 0.9rem;
    line-height: 1.4;
}

.notification.success .notification-message {
    color: #155724;
}

.notification.error .notification-message {
    color: #721c24;
}

.notification.warning .notification-message {
    color: #856404;
}

.notification.info .notification-message {
    color: #0c5460;
}

/* زر الإغلاق */
.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.notification-close:hover {
    background-color: rgba(0,0,0,0.1);
    color: #495057;
}

/* أيقونات الإشعارات */
.notification-icon {
    margin-left: 10px;
    font-size: 1.2rem;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification.success .notification-icon::before {
    content: "✓";
    color: #28a745;
}

.notification.error .notification-icon::before {
    content: "✗";
    color: #dc3545;
}

.notification.warning .notification-icon::before {
    content: "⚠";
    color: #ffc107;
}

.notification.info .notification-icon::before {
    content: "ℹ";
    color: #17a2b8;
}

/* التنبيهات في الصفحة */
.alert {
    padding: 15px 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid transparent;
    position: relative;
    font-size: 0.9rem;
    line-height: 1.4;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-error,
.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* التنبيهات القابلة للإغلاق */
.alert-dismissible {
    padding-left: 50px;
}

.alert-dismissible .close {
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 1.2rem;
    color: inherit;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.alert-dismissible .close:hover {
    opacity: 1;
}

/* مؤشرات التحميل */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s infinite;
}

/* أزرار التحميل */
.btn.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* أشرطة التقدم */
.progress {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar.normal {
    background: linear-gradient(90deg, #28a745, #1e7e34);
}

.progress-bar.warning {
    background: linear-gradient(90deg, #ffc107, #e0a800);
}

.progress-bar.danger {
    background: linear-gradient(90deg, #dc3545, #c82333);
}

/* تأثيرات الحركة */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

@keyframes progress {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(100%);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes dots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60%, 100% { content: '...'; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0,-30px,0); }
    70% { transform: translate3d(0,-15px,0); }
    90% { transform: translate3d(0,-4px,0); }
}

/* تأثيرات التفاعل */
.notification:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0,0,0,0.2);
}

.alert:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .notifications-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .notification {
        padding: 12px 15px;
        font-size: 0.8rem;
    }
    
    .notification-close {
        font-size: 1rem;
        width: 18px;
        height: 18px;
    }
    
    .alert {
        padding: 12px 15px;
        font-size: 0.8rem;
    }
    
    .alert-dismissible {
        padding-left: 40px;
    }
    
    .alert-dismissible .close {
        left: 10px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .notifications-container {
        top: 5px;
        right: 5px;
        left: 5px;
    }
    
    .notification {
        padding: 10px 12px;
        font-size: 0.75rem;
    }
    
    .notification-message {
        font-size: 0.75rem;
    }
    
    .notification-close {
        font-size: 0.9rem;
        width: 16px;
        height: 16px;
        margin-left: 8px;
    }
    
    .alert {
        padding: 10px 12px;
        font-size: 0.75rem;
    }
    
    .alert-dismissible {
        padding-left: 35px;
    }
    
    .alert-dismissible .close {
        left: 8px;
        font-size: 0.9rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .notifications-container,
    .notification,
    .loading-spinner,
    .btn.loading::after {
        display: none !important;
    }
    
    .alert {
        border: 1px solid #000;
        background: #f0f0f0 !important;
        color: #000 !important;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .notification {
        background-color: #2d2d2d;
        color: #fff;
    }
    
    .notification.success {
        background: linear-gradient(135deg, #1f4d1f 0%, #2c6d2c 100%);
    }
    
    .notification.error {
        background: linear-gradient(135deg, #4d1f1f 0%, #6d2c2c 100%);
    }
    
    .notification.warning {
        background: linear-gradient(135deg, #4d4d00 0%, #6d6d00 100%);
    }
    
    .notification.info {
        background: linear-gradient(135deg, #1f4d4d 0%, #2c6d6d 100%);
    }
    
    .alert {
        background-color: #2d2d2d;
        color: #fff;
        border-color: #444;
    }
    
    .alert-success {
        background-color: #1f4d1f;
        color: #6bff6b;
        border-color: #2c6d2c;
    }
    
    .alert-error,
    .alert-danger {
        background-color: #4d1f1f;
        color: #ff6b6b;
        border-color: #6d2c2c;
    }
    
    .alert-warning {
        background-color: #4d4d00;
        color: #ffff6b;
        border-color: #6d6d00;
    }
    
    .alert-info {
        background-color: #1f4d4d;
        color: #6bffff;
        border-color: #2c6d6d;
    }
    
    .progress {
        background-color: #444;
    }
}
