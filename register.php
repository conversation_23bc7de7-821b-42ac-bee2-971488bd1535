<?php
/**
 * صفحة التسجيل
 * Registration Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// إعادة توجيه المستخدمين المسجلين
if (isLoggedIn()) {
    redirect('index.php');
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = cleanInput($_POST['username'] ?? '');
    $email = cleanInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $tribe = intval($_POST['tribe'] ?? 1);
    $csrf = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!validateCSRF($csrf)) {
        $error = 'رمز الأمان غير صحيح';
    }
    // التحقق من المدخلات
    elseif (empty($username) || empty($email) || empty($password)) {
        $error = 'جميع الحقول مطلوبة';
    }
    elseif (strlen($username) < 3 || strlen($username) > 20) {
        $error = 'اسم المستخدم يجب أن يكون بين 3 و 20 حرف';
    }
    elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        $error = 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط';
    }
    elseif (!isValidEmail($email)) {
        $error = 'البريد الإلكتروني غير صحيح';
    }
    elseif (!isStrongPassword($password)) {
        $error = 'كلمة المرور يجب أن تحتوي على 6 أحرف على الأقل مع أرقام وحروف';
    }
    elseif ($password !== $confirmPassword) {
        $error = 'كلمات المرور غير متطابقة';
    }
    elseif (!in_array($tribe, [1, 2, 3])) {
        $error = 'القبيلة المختارة غير صحيحة';
    }
    else {
        // محاولة إنشاء المستخدم
        $userId = createUser($username, $email, $password, $tribe);
        
        if ($userId) {
            $success = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول';
            // إعادة تعيين النموذج
            $username = $email = '';
        } else {
            $error = 'اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل';
        }
    }
}

$csrfToken = generateCSRF();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التسجيل - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-header">
            <h1><?php echo SITE_NAME; ?></h1>
            <p>انضم إلى أكبر لعبة زراعية استراتيجية</p>
        </div>
        
        <div class="auth-form">
            <h2>إنشاء حساب جديد</h2>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                    <small>3-20 حرف، أحرف وأرقام فقط</small>
                </div>
                
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" required>
                    <small>6 أحرف على الأقل مع أرقام وحروف</small>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">تأكيد كلمة المرور</label>
                    <input type="password" id="confirm_password" name="confirm_password" required>
                </div>
                
                <div class="form-group">
                    <label for="tribe">اختر القبيلة</label>
                    <select id="tribe" name="tribe" required>
                        <option value="1" <?php echo (isset($tribe) && $tribe == 1) ? 'selected' : ''; ?>>الغال</option>
                        <option value="2" <?php echo (isset($tribe) && $tribe == 2) ? 'selected' : ''; ?>>الرومان</option>
                        <option value="3" <?php echo (isset($tribe) && $tribe == 3) ? 'selected' : ''; ?>>الجرمان</option>
                    </select>
                </div>
                
                <div class="tribe-info">
                    <div class="tribe-description" id="tribe-1">
                        <h4>الغال</h4>
                        <p>قبيلة متوازنة مع قوة دفاعية جيدة وسرعة في البناء</p>
                        <ul>
                            <li>مقاومة عالية للهجمات</li>
                            <li>بناء سريع</li>
                            <li>تجارة فعالة</li>
                        </ul>
                    </div>
                    
                    <div class="tribe-description" id="tribe-2" style="display: none;">
                        <h4>الرومان</h4>
                        <p>قبيلة قوية مع جيش متطور وقدرات هجومية ودفاعية متوازنة</p>
                        <ul>
                            <li>جيش قوي ومتنوع</li>
                            <li>قدرات هجومية ودفاعية</li>
                            <li>تطوير سريع</li>
                        </ul>
                    </div>
                    
                    <div class="tribe-description" id="tribe-3" style="display: none;">
                        <h4>الجرمان</h4>
                        <p>قبيلة هجومية مع جيش سريع وقدرة على الغارات السريعة</p>
                        <ul>
                            <li>هجمات سريعة</li>
                            <li>جيش رخيص</li>
                            <li>غارات فعالة</li>
                        </ul>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">إنشاء الحساب</button>
            </form>
            
            <div class="auth-links">
                <p>لديك حساب بالفعل؟ <a href="login.php">تسجيل الدخول</a></p>
            </div>
        </div>
    </div>
    
    <script>
        // عرض معلومات القبيلة المختارة
        document.getElementById('tribe').addEventListener('change', function() {
            const selectedTribe = this.value;
            const descriptions = document.querySelectorAll('.tribe-description');
            
            descriptions.forEach(desc => {
                desc.style.display = 'none';
            });
            
            document.getElementById('tribe-' + selectedTribe).style.display = 'block';
        });
        
        // التحقق من قوة كلمة المرور
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strength = document.getElementById('password-strength') || document.createElement('div');
            strength.id = 'password-strength';
            
            if (password.length < 6) {
                strength.textContent = 'ضعيفة - يجب أن تكون 6 أحرف على الأقل';
                strength.className = 'password-weak';
            } else if (!/[A-Za-z]/.test(password) || !/[0-9]/.test(password)) {
                strength.textContent = 'متوسطة - أضف أرقام وحروف';
                strength.className = 'password-medium';
            } else {
                strength.textContent = 'قوية';
                strength.className = 'password-strong';
            }
            
            if (!document.getElementById('password-strength')) {
                this.parentNode.appendChild(strength);
            }
        });
    </script>
</body>
</html>
