<?php
session_start();
require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'classes/Village.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $db = new Database();
    $user = new User($db->getConnection());
    $village = new Village($db->getConnection());
    
    if ($user->register($_POST['username'], $_POST['email'], $_POST['password'])) {
        // إنشاء قرية أولى للمستخدم
        $userId = $db->getConnection()->lastInsertId();
        $village->createVillage($userId, "قرية " . $_POST['username'], rand(1, 100), rand(1, 100));
        
        header('Location: login.php?registered=1');
        exit;
    } else {
        $error = "حدث خطأ في التسجيل";
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إنشاء حساب - المزرعة الذهبية</title>
    <link rel="stylesheet" href="assets/css/auth.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-box">
            <h1>إنشاء حساب جديد</h1>
            
            <?php if (isset($error)): ?>
                <div class="error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <input type="text" name="username" placeholder="اسم المستخدم" required>
                </div>
                <div class="form-group">
                    <input type="email" name="email" placeholder="البريد الإلكتروني" required>
                </div>
                <div class="form-group">
                    <input type="password" name="password" placeholder="كلمة المرور" required>
                </div>
                <button type="submit" class="btn-primary">إنشاء الحساب</button>
            </form>
            
            <p><a href="login.php">لديك حساب؟ سجل دخولك</a></p>
        </div>
    </div>
</body>
</html>