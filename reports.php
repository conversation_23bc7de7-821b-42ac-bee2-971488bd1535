<?php
/**
 * صفحة التقارير
 * Reports Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];
$villageId = $_SESSION['current_village_id'];

// التحقق من القرية
$village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$villageId, $userId]);
if (!$village) {
    redirect('index.php');
}

// معالجة الفلترة
$reportType = $_GET['type'] ?? 'all';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// بناء استعلام التقارير
$whereConditions = ["(r.attacker_village_id IN (SELECT id FROM villages WHERE user_id = ?) OR r.defender_village_id IN (SELECT id FROM villages WHERE user_id = ?))"];
$params = [$userId, $userId];

if ($reportType !== 'all') {
    $whereConditions[] = "r.report_type = ?";
    $params[] = $reportType;
}

$whereClause = implode(' AND ', $whereConditions);

// الحصول على التقارير
$reports = $db->select("
    SELECT r.*, 
           av.name as attacker_village_name, au.username as attacker_username,
           dv.name as defender_village_name, du.username as defender_username
    FROM reports r
    LEFT JOIN villages av ON r.attacker_village_id = av.id
    LEFT JOIN users au ON av.user_id = au.id
    LEFT JOIN villages dv ON r.defender_village_id = dv.id
    LEFT JOIN users du ON dv.user_id = du.id
    WHERE {$whereClause}
    ORDER BY r.created_at DESC
    LIMIT ? OFFSET ?
", array_merge($params, [$limit, $offset]));

// عدد التقارير الإجمالي
$totalReports = $db->selectOne("
    SELECT COUNT(*) as count
    FROM reports r
    WHERE {$whereClause}
", $params)['count'];

$totalPages = ceil($totalReports / $limit);

// إحصائيات التقارير
$reportStats = $db->selectOne("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN report_type = 'attack' THEN 1 ELSE 0 END) as attacks,
        SUM(CASE WHEN report_type = 'defense' THEN 1 ELSE 0 END) as defenses,
        SUM(CASE WHEN report_type = 'trade' THEN 1 ELSE 0 END) as trades,
        SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread
    FROM reports r
    WHERE (r.attacker_village_id IN (SELECT id FROM villages WHERE user_id = ?) 
           OR r.defender_village_id IN (SELECT id FROM villages WHERE user_id = ?))
", [$userId, $userId]);

// معالجة تحديد الكل كمقروء
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['mark_all_read'])) {
    $csrf = $_POST['csrf_token'] ?? '';
    if (validateCSRF($csrf)) {
        $db->update("
            UPDATE reports 
            SET is_read = 1 
            WHERE (attacker_village_id IN (SELECT id FROM villages WHERE user_id = ?) 
                   OR defender_village_id IN (SELECT id FROM villages WHERE user_id = ?))
        ", [$userId, $userId]);
        
        redirect('reports.php');
    }
}

$csrfToken = generateCSRF();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/reports.css">
</head>
<body class="game-page">
    <?php include 'includes/header.php'; ?>
    
    <div class="main-content">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="content">
            <div class="page-header">
                <h1>التقارير</h1>
                <p>تقارير المعارك والتجارة والأحداث</p>
            </div>
            
            <!-- إحصائيات التقارير -->
            <div class="reports-stats">
                <div class="stats-grid">
                    <div class="stat-card total">
                        <div class="stat-icon">📊</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo formatNumber($reportStats['total']); ?></div>
                            <div class="stat-label">إجمالي التقارير</div>
                        </div>
                    </div>
                    
                    <div class="stat-card attacks">
                        <div class="stat-icon">⚔️</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo formatNumber($reportStats['attacks']); ?></div>
                            <div class="stat-label">تقارير الهجوم</div>
                        </div>
                    </div>
                    
                    <div class="stat-card defenses">
                        <div class="stat-icon">🛡️</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo formatNumber($reportStats['defenses']); ?></div>
                            <div class="stat-label">تقارير الدفاع</div>
                        </div>
                    </div>
                    
                    <div class="stat-card trades">
                        <div class="stat-icon">🤝</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo formatNumber($reportStats['trades']); ?></div>
                            <div class="stat-label">تقارير التجارة</div>
                        </div>
                    </div>
                    
                    <div class="stat-card unread">
                        <div class="stat-icon">📬</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo formatNumber($reportStats['unread']); ?></div>
                            <div class="stat-label">غير مقروءة</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- أدوات التحكم -->
            <div class="reports-controls">
                <div class="filter-controls">
                    <label for="report-filter">فلترة التقارير:</label>
                    <select id="report-filter" onchange="filterReports(this.value)">
                        <option value="all" <?php echo $reportType === 'all' ? 'selected' : ''; ?>>جميع التقارير</option>
                        <option value="attack" <?php echo $reportType === 'attack' ? 'selected' : ''; ?>>تقارير الهجوم</option>
                        <option value="defense" <?php echo $reportType === 'defense' ? 'selected' : ''; ?>>تقارير الدفاع</option>
                        <option value="trade" <?php echo $reportType === 'trade' ? 'selected' : ''; ?>>تقارير التجارة</option>
                        <option value="scout" <?php echo $reportType === 'scout' ? 'selected' : ''; ?>>تقارير الاستطلاع</option>
                    </select>
                </div>
                
                <?php if ($reportStats['unread'] > 0): ?>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <button type="submit" name="mark_all_read" class="btn btn-secondary">
                            تحديد الكل كمقروء
                        </button>
                    </form>
                <?php endif; ?>
            </div>
            
            <!-- قائمة التقارير -->
            <div class="reports-list">
                <?php if (!empty($reports)): ?>
                    <?php foreach ($reports as $report): ?>
                        <?php
                        $isAttacker = in_array($report['attacker_village_id'], array_column($db->select("SELECT id FROM villages WHERE user_id = ?", [$userId]), 'id'));
                        $isDefender = in_array($report['defender_village_id'], array_column($db->select("SELECT id FROM villages WHERE user_id = ?", [$userId]), 'id'));
                        $reportClass = $report['is_read'] ? 'read' : 'unread';
                        $reportTypeClass = $report['report_type'];
                        ?>
                        
                        <div class="report-item <?php echo $reportClass; ?> <?php echo $reportTypeClass; ?>" 
                             onclick="toggleReportDetails(<?php echo $report['id']; ?>)">
                            
                            <div class="report-header">
                                <div class="report-icon">
                                    <?php echo getReportIcon($report['report_type']); ?>
                                </div>
                                
                                <div class="report-info">
                                    <div class="report-title">
                                        <?php echo getReportTitle($report, $isAttacker); ?>
                                    </div>
                                    
                                    <div class="report-participants">
                                        <span class="attacker">
                                            <?php echo htmlspecialchars($report['attacker_username']); ?> 
                                            (<?php echo htmlspecialchars($report['attacker_village_name']); ?>)
                                        </span>
                                        <span class="vs">ضد</span>
                                        <span class="defender">
                                            <?php echo htmlspecialchars($report['defender_username']); ?> 
                                            (<?php echo htmlspecialchars($report['defender_village_name']); ?>)
                                        </span>
                                    </div>
                                    
                                    <div class="report-time">
                                        <?php echo formatTimeAgo($report['created_at']); ?>
                                    </div>
                                </div>
                                
                                <div class="report-result">
                                    <?php echo getReportResult($report); ?>
                                </div>
                                
                                <div class="report-toggle">
                                    <span class="toggle-icon">▼</span>
                                </div>
                            </div>
                            
                            <div class="report-details" id="report-details-<?php echo $report['id']; ?>" style="display: none;">
                                <div class="details-content">
                                    <?php echo getReportDetails($report); ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <!-- التنقل بين الصفحات -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?type=<?php echo $reportType; ?>&page=<?php echo $page - 1; ?>" class="page-btn">السابق</a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <a href="?type=<?php echo $reportType; ?>&page=<?php echo $i; ?>" 
                                   class="page-btn <?php echo $i === $page ? 'active' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <a href="?type=<?php echo $reportType; ?>&page=<?php echo $page + 1; ?>" class="page-btn">التالي</a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <div class="no-reports">
                        <div class="no-reports-icon">📋</div>
                        <h3>لا توجد تقارير</h3>
                        <p>لم يتم العثور على تقارير بالمعايير المحددة</p>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <script>
        function filterReports(type) {
            window.location.href = `reports.php?type=${type}`;
        }
        
        function toggleReportDetails(reportId) {
            const details = document.getElementById(`report-details-${reportId}`);
            const toggle = details.previousElementSibling.querySelector('.toggle-icon');
            
            if (details.style.display === 'none') {
                details.style.display = 'block';
                toggle.textContent = '▲';
                
                // تحديد التقرير كمقروء
                markReportAsRead(reportId);
            } else {
                details.style.display = 'none';
                toggle.textContent = '▼';
            }
        }
        
        function markReportAsRead(reportId) {
            fetch('ajax/mark-report-read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    report_id: reportId,
                    csrf_token: '<?php echo $csrfToken; ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const reportItem = document.querySelector(`#report-details-${reportId}`).parentElement;
                    reportItem.classList.remove('unread');
                    reportItem.classList.add('read');
                }
            })
            .catch(error => console.error('Error:', error));
        }
    </script>
</body>
</html>

<?php
function getReportIcon($type) {
    $icons = [
        'attack' => '⚔️',
        'defense' => '🛡️',
        'trade' => '🤝',
        'scout' => '👁️',
        'reinforcement' => '🏃‍♂️'
    ];
    return $icons[$type] ?? '📄';
}

function getReportTitle($report, $isAttacker) {
    $titles = [
        'attack' => $isAttacker ? 'تقرير هجوم' : 'تقرير دفاع',
        'defense' => 'تقرير دفاع',
        'trade' => 'تقرير تجارة',
        'scout' => 'تقرير استطلاع',
        'reinforcement' => 'تقرير تعزيزات'
    ];
    return $titles[$report['report_type']] ?? 'تقرير';
}

function getReportResult($report) {
    $results = [
        'victory' => '<span class="result victory">انتصار</span>',
        'defeat' => '<span class="result defeat">هزيمة</span>',
        'draw' => '<span class="result draw">تعادل</span>',
        'success' => '<span class="result success">نجح</span>',
        'failed' => '<span class="result failed">فشل</span>'
    ];
    return $results[$report['result']] ?? '';
}

function getReportDetails($report) {
    // هذه دالة مبسطة لعرض تفاصيل التقرير
    // في التطبيق الحقيقي، ستحتاج لتحليل بيانات JSON المخزنة في قاعدة البيانات
    $details = json_decode($report['report_data'], true) ?? [];
    
    $html = '<div class="report-summary">';
    
    if ($report['report_type'] === 'attack' || $report['report_type'] === 'defense') {
        $html .= '<h4>ملخص المعركة</h4>';
        $html .= '<div class="battle-summary">';
        $html .= '<p><strong>النتيجة:</strong> ' . getReportResult($report) . '</p>';
        
        if (isset($details['attacker_losses'])) {
            $html .= '<p><strong>خسائر المهاجم:</strong> ' . formatNumber($details['attacker_losses']) . '</p>';
        }
        
        if (isset($details['defender_losses'])) {
            $html .= '<p><strong>خسائر المدافع:</strong> ' . formatNumber($details['defender_losses']) . '</p>';
        }
        
        if (isset($details['resources_stolen'])) {
            $html .= '<p><strong>الموارد المسروقة:</strong></p>';
            $html .= '<ul>';
            foreach ($details['resources_stolen'] as $resource => $amount) {
                if ($amount > 0) {
                    $html .= '<li>' . getResourceName($resource) . ': ' . formatNumber($amount) . '</li>';
                }
            }
            $html .= '</ul>';
        }
        
        $html .= '</div>';
    } elseif ($report['report_type'] === 'trade') {
        $html .= '<h4>تفاصيل التجارة</h4>';
        $html .= '<div class="trade-summary">';
        $html .= '<p><strong>الحالة:</strong> ' . ($report['result'] === 'success' ? 'تمت بنجاح' : 'فشلت') . '</p>';
        
        if (isset($details['resources_sent'])) {
            $html .= '<p><strong>الموارد المرسلة:</strong></p>';
            $html .= '<ul>';
            foreach ($details['resources_sent'] as $resource => $amount) {
                if ($amount > 0) {
                    $html .= '<li>' . getResourceName($resource) . ': ' . formatNumber($amount) . '</li>';
                }
            }
            $html .= '</ul>';
        }
        
        $html .= '</div>';
    }
    
    $html .= '</div>';
    
    return $html;
}

function getResourceName($resource) {
    $names = [
        'wood' => 'خشب',
        'clay' => 'طين',
        'iron' => 'حديد',
        'crop' => 'قمح'
    ];
    return $names[$resource] ?? $resource;
}

function formatTimeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return 'منذ لحظات';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return "منذ {$minutes} دقيقة";
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return "منذ {$hours} ساعة";
    } else {
        $days = floor($time / 86400);
        return "منذ {$days} يوم";
    }
}
?>
