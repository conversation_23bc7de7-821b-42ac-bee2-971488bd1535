<?php
/**
 * تحديد التقرير كمقروء
 * Mark Report as Read
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'error' => 'غير مسجل الدخول']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'طريقة طلب غير صحيحة']);
    exit;
}

// قراءة البيانات
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'error' => 'بيانات غير صحيحة']);
    exit;
}

$reportId = intval($input['report_id'] ?? 0);
$csrf = $input['csrf_token'] ?? '';

// التحقق من صحة البيانات
if (!validateCSRF($csrf)) {
    echo json_encode(['success' => false, 'error' => 'رمز الأمان غير صحيح']);
    exit;
}

if ($reportId <= 0) {
    echo json_encode(['success' => false, 'error' => 'معرف التقرير غير صحيح']);
    exit;
}

$db = getDB();
$userId = $_SESSION['user_id'];

try {
    // التحقق من أن التقرير يخص المستخدم
    $report = $db->selectOne("
        SELECT r.* 
        FROM reports r
        WHERE r.id = ? 
        AND (r.attacker_village_id IN (SELECT id FROM villages WHERE user_id = ?) 
             OR r.defender_village_id IN (SELECT id FROM villages WHERE user_id = ?))
    ", [$reportId, $userId, $userId]);
    
    if (!$report) {
        echo json_encode(['success' => false, 'error' => 'التقرير غير موجود']);
        exit;
    }
    
    // تحديث حالة القراءة
    $updated = $db->update("UPDATE reports SET is_read = 1 WHERE id = ?", [$reportId]);
    
    if ($updated) {
        echo json_encode(['success' => true, 'message' => 'تم تحديد التقرير كمقروء']);
    } else {
        echo json_encode(['success' => false, 'error' => 'فشل في تحديث التقرير']);
    }
    
} catch (Exception $e) {
    error_log("Error marking report as read: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'حدث خطأ في الخادم']);
}
?>
