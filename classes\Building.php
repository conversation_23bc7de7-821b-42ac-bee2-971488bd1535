<?php
class Building {
    private $conn;
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    public function constructBuilding($villageId, $buildingType, $x, $y, $userId) {
        $costs = $this->getBuildingCosts($buildingType, 1);
        
        // التحقق من الموارد
        if (!$this->checkResources($userId, $costs)) {
            return false;
        }
        
        $this->conn->beginTransaction();
        
        try {
            // خصم الموارد
            $this->deductResources($userId, $costs);
            
            // إنشاء المبنى
            $stmt = $this->conn->prepare("INSERT INTO buildings (village_id, building_type, level, x_position, y_position) VALUES (?, ?, 1, ?, ?)");
            $stmt->execute([$villageId, $buildingType, $x, $y]);
            
            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }
    
    public function upgradeBuilding($buildingId, $userId) {
        $stmt = $this->conn->prepare("SELECT * FROM buildings WHERE id = ?");
        $stmt->execute([$buildingId]);
        $building = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$building) return false;
        
        $newLevel = $building['level'] + 1;
        $costs = $this->getBuildingCosts($building['building_type'], $newLevel);
        
        if (!$this->checkResources($userId, $costs)) {
            return false;
        }
        
        $upgradeTime = date('Y-m-d H:i:s', strtotime('+' . ($newLevel * 30) . ' minutes'));
        
        $this->conn->beginTransaction();
        
        try {
            $this->deductResources($userId, $costs);
            
            $stmt = $this->conn->prepare("UPDATE buildings SET level = ?, upgrade_end_time = ? WHERE id = ?");
            $stmt->execute([$newLevel, $upgradeTime, $buildingId]);
            
            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }
    
    private function getBuildingCosts($buildingType, $level) {
        $baseCosts = [
            'house' => ['gold' => 100, 'wood' => 50, 'stone' => 0],
            'farm' => ['gold' => 150, 'wood' => 75, 'stone' => 0],
            'warehouse' => ['gold' => 200, 'wood' => 100, 'stone' => 50],
            'market' => ['gold' => 250, 'wood' => 125, 'stone' => 75],
            'barracks' => ['gold' => 300, 'wood' => 150, 'stone' => 100],
            'wall' => ['gold' => 400, 'wood' => 200, 'stone' => 200]
        ];
        
        $costs = $baseCosts[$buildingType];
        $multiplier = pow(1.5, $level - 1);
        
        foreach ($costs as $resource => $amount) {
            $costs[$resource] = floor($amount * $multiplier);
        }
        
        return $costs;
    }
    
    private function checkResources($userId, $costs) {
        $stmt = $this->conn->prepare("SELECT gold, wood, stone FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $user['gold'] >= $costs['gold'] && 
               $user['wood'] >= $costs['wood'] && 
               $user['stone'] >= $costs['stone'];
    }
    
    private function deductResources($userId, $costs) {
        $stmt = $this->conn->prepare("UPDATE users SET gold = gold - ?, wood = wood - ?, stone = stone - ? WHERE id = ?");
        $stmt->execute([$costs['gold'], $costs['wood'], $costs['stone'], $userId]);
    }
    
    public function getVillageBuildings($villageId) {
        $stmt = $this->conn->prepare("SELECT * FROM buildings WHERE village_id = ?");
        $stmt->execute([$villageId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>