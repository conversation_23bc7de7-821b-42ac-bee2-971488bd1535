<?php
/**
 * صفحة إحصائيات التجارة
 * Trade Statistics Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];
$villageId = $_SESSION['current_village_id'];

// التحقق من القرية
$village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$villageId, $userId]);
if (!$village) {
    redirect('index.php');
}

// فترة الإحصائيات (افتراضياً آخر 30 يوم)
$period = $_GET['period'] ?? '30';
$validPeriods = ['7', '30', '90', 'all'];
if (!in_array($period, $validPeriods)) {
    $period = '30';
}

// تحديد التاريخ
$dateCondition = '';
$dateParams = [];
if ($period !== 'all') {
    $dateCondition = 'AND t.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)';
    $dateParams = [$period];
}

// إحصائيات التجارة الصادرة
$outgoingStats = $db->selectOne("
    SELECT 
        COUNT(*) as total_trades,
        SUM(wood_amount) as total_wood,
        SUM(clay_amount) as total_clay,
        SUM(iron_amount) as total_iron,
        SUM(crop_amount) as total_crop,
        AVG(wood_amount + clay_amount + iron_amount + crop_amount) as avg_trade_size
    FROM trades t
    WHERE sender_village_id = ? AND status IN ('completed', 'in_transit') {$dateCondition}
", array_merge([$villageId], $dateParams));

// إحصائيات التجارة الواردة
$incomingStats = $db->selectOne("
    SELECT 
        COUNT(*) as total_trades,
        SUM(wood_amount) as total_wood,
        SUM(clay_amount) as total_clay,
        SUM(iron_amount) as total_iron,
        SUM(crop_amount) as total_crop,
        AVG(wood_amount + clay_amount + iron_amount + crop_amount) as avg_trade_size
    FROM trades t
    WHERE receiver_village_id = ? AND status IN ('completed', 'in_transit') {$dateCondition}
", array_merge([$villageId], $dateParams));

// أكثر الشركاء تجارة
$topPartners = $db->select("
    SELECT 
        CASE 
            WHEN t.sender_village_id = ? THEN rv.name
            ELSE sv.name
        END as partner_village,
        CASE 
            WHEN t.sender_village_id = ? THEN ru.username
            ELSE su.username
        END as partner_username,
        COUNT(*) as trade_count,
        SUM(t.wood_amount + t.clay_amount + t.iron_amount + t.crop_amount) as total_resources
    FROM trades t
    LEFT JOIN villages sv ON t.sender_village_id = sv.id
    LEFT JOIN users su ON sv.user_id = su.id
    LEFT JOIN villages rv ON t.receiver_village_id = rv.id
    LEFT JOIN users ru ON rv.user_id = ru.id
    WHERE (t.sender_village_id = ? OR t.receiver_village_id = ?) 
          AND t.status IN ('completed', 'in_transit') {$dateCondition}
    GROUP BY partner_village, partner_username
    ORDER BY trade_count DESC
    LIMIT 10
", array_merge([$villageId, $villageId, $villageId, $villageId], $dateParams));

// إحصائيات يومية (آخر 7 أيام)
$dailyStats = $db->select("
    SELECT 
        DATE(t.created_at) as trade_date,
        COUNT(*) as daily_trades,
        SUM(CASE WHEN t.sender_village_id = ? THEN 1 ELSE 0 END) as outgoing_trades,
        SUM(CASE WHEN t.receiver_village_id = ? THEN 1 ELSE 0 END) as incoming_trades,
        SUM(t.wood_amount + t.clay_amount + t.iron_amount + t.crop_amount) as daily_resources
    FROM trades t
    WHERE (t.sender_village_id = ? OR t.receiver_village_id = ?) 
          AND t.status IN ('completed', 'in_transit')
          AND t.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY DATE(t.created_at)
    ORDER BY trade_date DESC
", [$villageId, $villageId, $villageId, $villageId]);

// إحصائيات الموارد
$resourceStats = [
    'wood' => [
        'sent' => $outgoingStats['total_wood'] ?? 0,
        'received' => $incomingStats['total_wood'] ?? 0
    ],
    'clay' => [
        'sent' => $outgoingStats['total_clay'] ?? 0,
        'received' => $incomingStats['total_clay'] ?? 0
    ],
    'iron' => [
        'sent' => $outgoingStats['total_iron'] ?? 0,
        'received' => $incomingStats['total_iron'] ?? 0
    ],
    'crop' => [
        'sent' => $outgoingStats['total_crop'] ?? 0,
        'received' => $incomingStats['total_crop'] ?? 0
    ]
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إحصائيات التجارة - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/trade-statistics.css">
</head>
<body class="game-page">
    <?php include 'includes/header.php'; ?>
    
    <div class="main-content">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="content">
            <div class="page-header">
                <h1>إحصائيات التجارة</h1>
                <p>تحليل نشاط التجارة وأداء القرية</p>
            </div>
            
            <!-- فلتر الفترة الزمنية -->
            <div class="period-filter">
                <h3>الفترة الزمنية:</h3>
                <div class="filter-buttons">
                    <a href="?period=7" class="filter-btn <?php echo $period === '7' ? 'active' : ''; ?>">آخر 7 أيام</a>
                    <a href="?period=30" class="filter-btn <?php echo $period === '30' ? 'active' : ''; ?>">آخر 30 يوم</a>
                    <a href="?period=90" class="filter-btn <?php echo $period === '90' ? 'active' : ''; ?>">آخر 90 يوم</a>
                    <a href="?period=all" class="filter-btn <?php echo $period === 'all' ? 'active' : ''; ?>">كل الوقت</a>
                </div>
            </div>
            
            <!-- الإحصائيات العامة -->
            <div class="general-stats">
                <h3>📊 الإحصائيات العامة</h3>
                <div class="stats-grid">
                    <div class="stat-card outgoing">
                        <div class="stat-icon">📤</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo formatNumber($outgoingStats['total_trades'] ?? 0); ?></div>
                            <div class="stat-label">تجارة صادرة</div>
                        </div>
                    </div>
                    
                    <div class="stat-card incoming">
                        <div class="stat-icon">📥</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo formatNumber($incomingStats['total_trades'] ?? 0); ?></div>
                            <div class="stat-label">تجارة واردة</div>
                        </div>
                    </div>
                    
                    <div class="stat-card total">
                        <div class="stat-icon">🔄</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo formatNumber(($outgoingStats['total_trades'] ?? 0) + ($incomingStats['total_trades'] ?? 0)); ?></div>
                            <div class="stat-label">إجمالي التجارة</div>
                        </div>
                    </div>
                    
                    <div class="stat-card average">
                        <div class="stat-icon">📈</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo formatNumber(round(($outgoingStats['avg_trade_size'] ?? 0) + ($incomingStats['avg_trade_size'] ?? 0)) / 2); ?></div>
                            <div class="stat-label">متوسط حجم التجارة</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات الموارد -->
            <div class="resource-stats">
                <h3>💰 إحصائيات الموارد</h3>
                <div class="resources-grid">
                    <?php foreach ($resourceStats as $resourceType => $stats): ?>
                        <div class="resource-card">
                            <div class="resource-header">
                                <img src="images/resources/<?php echo $resourceType; ?>.png" alt="<?php echo getResourceName($resourceType); ?>">
                                <h4><?php echo getResourceName($resourceType); ?></h4>
                            </div>
                            <div class="resource-data">
                                <div class="data-item sent">
                                    <span class="label">مُرسل:</span>
                                    <span class="value"><?php echo formatNumber($stats['sent']); ?></span>
                                </div>
                                <div class="data-item received">
                                    <span class="label">مُستقبل:</span>
                                    <span class="value"><?php echo formatNumber($stats['received']); ?></span>
                                </div>
                                <div class="data-item balance">
                                    <span class="label">الرصيد:</span>
                                    <span class="value <?php echo ($stats['received'] - $stats['sent']) >= 0 ? 'positive' : 'negative'; ?>">
                                        <?php echo formatNumber($stats['received'] - $stats['sent']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- أكثر الشركاء تجارة -->
            <?php if (!empty($topPartners)): ?>
                <div class="top-partners">
                    <h3>🤝 أكثر الشركاء تجارة</h3>
                    <div class="partners-list">
                        <?php foreach ($topPartners as $index => $partner): ?>
                            <div class="partner-item">
                                <div class="partner-rank"><?php echo $index + 1; ?></div>
                                <div class="partner-info">
                                    <div class="partner-name"><?php echo htmlspecialchars($partner['partner_village']); ?></div>
                                    <div class="partner-username"><?php echo htmlspecialchars($partner['partner_username']); ?></div>
                                </div>
                                <div class="partner-stats">
                                    <div class="trade-count"><?php echo formatNumber($partner['trade_count']); ?> تجارة</div>
                                    <div class="total-resources"><?php echo formatNumber($partner['total_resources']); ?> موارد</div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- الإحصائيات اليومية -->
            <?php if (!empty($dailyStats)): ?>
                <div class="daily-stats">
                    <h3>📅 الإحصائيات اليومية (آخر 7 أيام)</h3>
                    <div class="daily-chart">
                        <?php foreach ($dailyStats as $day): ?>
                            <div class="day-item">
                                <div class="day-date"><?php echo date('d/m', strtotime($day['trade_date'])); ?></div>
                                <div class="day-bars">
                                    <div class="bar outgoing" style="height: <?php echo min(100, ($day['outgoing_trades'] / max(1, $day['daily_trades'])) * 100); ?>%">
                                        <span class="bar-value"><?php echo $day['outgoing_trades']; ?></span>
                                    </div>
                                    <div class="bar incoming" style="height: <?php echo min(100, ($day['incoming_trades'] / max(1, $day['daily_trades'])) * 100); ?>%">
                                        <span class="bar-value"><?php echo $day['incoming_trades']; ?></span>
                                    </div>
                                </div>
                                <div class="day-total"><?php echo $day['daily_trades']; ?></div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color outgoing"></div>
                            <span>صادرة</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color incoming"></div>
                            <span>واردة</span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- نصائح التجارة -->
            <div class="trade-tips">
                <h3>💡 نصائح لتحسين التجارة</h3>
                <div class="tips-grid">
                    <div class="tip-card">
                        <div class="tip-icon">🎯</div>
                        <h4>استهدف الشركاء النشطين</h4>
                        <p>ركز على التجارة مع اللاعبين الذين يتفاعلون بانتظام</p>
                    </div>
                    
                    <div class="tip-card">
                        <div class="tip-icon">⚖️</div>
                        <h4>حافظ على التوازن</h4>
                        <p>تأكد من أن التجارة الواردة تعوض الصادرة</p>
                    </div>
                    
                    <div class="tip-card">
                        <div class="tip-icon">📈</div>
                        <h4>راقب الأسعار</h4>
                        <p>تابع أسعار الموارد في السوق للحصول على أفضل الصفقات</p>
                    </div>
                    
                    <div class="tip-card">
                        <div class="tip-icon">🚀</div>
                        <h4>طور السوق</h4>
                        <p>ترقية السوق تزيد من عدد التجار وسرعة التجارة</p>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>

<?php
function getResourceName($type) {
    $names = [
        'wood' => 'خشب',
        'clay' => 'طين',
        'iron' => 'حديد',
        'crop' => 'قمح'
    ];
    return $names[$type] ?? $type;
}
?>
