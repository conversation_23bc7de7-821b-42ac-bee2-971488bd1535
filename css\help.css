/* ملف CSS لصفحة المساعدة */
/* Help Page CSS */

/* قائمة التنقل */
.help-navigation {
    margin-bottom: 30px;
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px 10px;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.nav-item:hover {
    border-color: #007bff;
    background-color: #e7f3ff;
    color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

.nav-item.active {
    border-color: #007bff;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.nav-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0,0,0,0.1);
}

.nav-item.active .nav-icon {
    background-color: rgba(255,255,255,0.2);
}

.nav-item span {
    font-weight: 500;
    font-size: 0.9rem;
    text-align: center;
}

/* محتوى المساعدة */
.help-content {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.help-section h2 {
    color: #495057;
    margin-bottom: 25px;
    font-size: 1.8rem;
    border-bottom: 3px solid #007bff;
    padding-bottom: 10px;
}

/* بطاقات النظرة العامة */
.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.overview-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
    text-align: center;
}

.overview-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.overview-card h3 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.overview-card p {
    color: #6c757d;
    line-height: 1.5;
    font-size: 0.9rem;
}

/* البداية السريعة */
.getting-started {
    background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
    border-radius: 10px;
    padding: 25px;
    border: 2px solid #007bff;
    margin-top: 30px;
}

.getting-started h3 {
    color: #0056b3;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.steps-list {
    list-style: none;
    padding: 0;
    counter-reset: step-counter;
}

.steps-list li {
    counter-increment: step-counter;
    margin-bottom: 12px;
    padding: 12px 15px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #bee5eb;
    position: relative;
    padding-right: 50px;
}

.steps-list li::before {
    content: counter(step-counter);
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 25px;
    height: 25px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
}

/* دليل المباني */
.buildings-guide {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.building-category h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.3rem;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 8px;
}

.buildings-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.building-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.building-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.building-item img {
    width: 50px;
    height: 50px;
    border-radius: 6px;
    border: 2px solid #dee2e6;
}

.building-info {
    flex: 1;
}

.building-info h4 {
    color: #495057;
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.building-info p {
    color: #6c757d;
    line-height: 1.4;
    font-size: 0.9rem;
    margin: 0;
}

/* دليل الموارد */
.resources-guide {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.resource-item {
    display: flex;
    gap: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.resource-item img {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    border: 2px solid #dee2e6;
}

.resource-info {
    flex: 1;
}

.resource-info h3 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.resource-info > p {
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.4;
}

.resource-tips h4 {
    color: #495057;
    margin-bottom: 8px;
    font-size: 1rem;
}

.resource-tips ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.resource-tips li {
    color: #6c757d;
    margin-bottom: 5px;
    padding-right: 15px;
    position: relative;
    font-size: 0.9rem;
}

.resource-tips li::before {
    content: "•";
    position: absolute;
    right: 0;
    color: #007bff;
    font-weight: bold;
}

/* إدارة الموارد */
.resource-management {
    margin-top: 30px;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 10px;
    padding: 25px;
    border: 2px solid #ffc107;
}

.resource-management h3 {
    color: #856404;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.management-tips {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.tip-card {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #ffeaa7;
    text-align: center;
}

.tip-card h4 {
    color: #856404;
    margin-bottom: 8px;
    font-size: 1rem;
}

.tip-card p {
    color: #856404;
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
}

/* دليل الجيش */
.military-guide {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.unit-category h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.3rem;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 8px;
}

.units-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.unit-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.unit-item:hover {
    border-color: #dc3545;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.unit-item img {
    width: 50px;
    height: 50px;
    border-radius: 6px;
    border: 2px solid #dee2e6;
}

.unit-info {
    flex: 1;
}

.unit-info h4 {
    color: #495057;
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.unit-info p {
    color: #6c757d;
    margin-bottom: 10px;
    line-height: 1.4;
    font-size: 0.9rem;
}

.unit-stats {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.unit-stats span {
    background-color: #e9ecef;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #495057;
    font-weight: 500;
}

/* نصائح القتال */
.combat-tips {
    margin-top: 30px;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-radius: 10px;
    padding: 25px;
    border: 2px solid #dc3545;
}

.combat-tips h3 {
    color: #721c24;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.tips-grid .tip-card {
    background-color: white;
    border: 1px solid #f5c6cb;
}

.tips-grid .tip-card h4 {
    color: #721c24;
}

.tips-grid .tip-card p {
    color: #721c24;
}

/* دليل التجارة */
.trade-guide {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.trade-method {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.trade-method h3 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.trade-method p {
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.4;
}

.method-steps ol {
    margin: 0;
    padding-right: 20px;
}

.method-steps li {
    color: #495057;
    margin-bottom: 8px;
    line-height: 1.4;
}

/* نصائح التجارة */
.trade-tips {
    margin-top: 30px;
}

.trade-tips h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.tips-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.tip-item {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.tip-item h4 {
    color: #495057;
    margin-bottom: 8px;
    font-size: 1rem;
}

.tip-item p {
    color: #6c757d;
    line-height: 1.4;
    font-size: 0.9rem;
    margin: 0;
}

/* دليل التحالفات */
.alliance-guide {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.alliance-benefits h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.benefits-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.benefit-item {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
    text-align: center;
    transition: all 0.3s ease;
}

.benefit-item:hover {
    border-color: #28a745;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.benefit-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.benefit-item h4 {
    color: #495057;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.benefit-item p {
    color: #6c757d;
    line-height: 1.4;
    font-size: 0.9rem;
    margin: 0;
}

/* نصائح التحالف */
.alliance-tips {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-radius: 10px;
    padding: 25px;
    border: 2px solid #28a745;
}

.alliance-tips h3 {
    color: #155724;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.alliance-tips .tips-grid .tip-card {
    background-color: white;
    border: 1px solid #c3e6cb;
}

.alliance-tips .tips-grid .tip-card h4 {
    color: #155724;
}

.alliance-tips .tips-grid .tip-card p {
    color: #155724;
}

/* فئات النصائح */
.tips-categories {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.tips-category {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    border: 2px solid #e9ecef;
}

.tips-category h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.3rem;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 8px;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .nav-sections {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }
    
    .nav-item {
        padding: 12px 8px;
        gap: 6px;
    }
    
    .nav-icon {
        font-size: 1.2rem;
        width: 35px;
        height: 35px;
    }
    
    .nav-item span {
        font-size: 0.8rem;
    }
    
    .help-content {
        padding: 20px;
    }
    
    .overview-cards {
        grid-template-columns: 1fr;
    }
    
    .building-item,
    .unit-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .resource-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .benefits-list {
        grid-template-columns: 1fr;
    }
    
    .management-tips,
    .tips-grid {
        grid-template-columns: 1fr;
    }
    
    .unit-stats {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .nav-sections {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .help-content {
        padding: 15px;
    }
    
    .help-section h2 {
        font-size: 1.5rem;
    }
    
    .overview-card,
    .getting-started,
    .resource-management,
    .combat-tips,
    .alliance-tips,
    .tips-category {
        padding: 15px;
    }
    
    .card-icon {
        font-size: 2.5rem;
    }
    
    .benefit-icon {
        font-size: 2rem;
    }
    
    .building-item img,
    .unit-item img {
        width: 40px;
        height: 40px;
    }
    
    .resource-item img {
        width: 50px;
        height: 50px;
    }
}

/* تحسينات للطباعة */
@media print {
    .help-navigation {
        display: none;
    }
    
    .help-content {
        border: none;
        box-shadow: none;
        padding: 0;
    }
    
    .overview-card,
    .building-item,
    .unit-item,
    .resource-item,
    .tip-card,
    .benefit-item {
        border: 1px solid #000;
        box-shadow: none;
        break-inside: avoid;
        margin-bottom: 10px;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .help-content,
    .overview-card,
    .building-item,
    .unit-item,
    .resource-item,
    .trade-method,
    .tip-item,
    .benefit-item,
    .tips-category {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .nav-item {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .nav-item:hover {
        background-color: #3d3d3d;
    }
    
    .tip-card {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
}
