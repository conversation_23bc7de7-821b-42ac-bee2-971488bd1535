<?php
/**
 * تحديث الموارد عبر AJAX
 * AJAX Resource Update
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// تعيين نوع المحتوى
header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'error' => 'غير مسجل الدخول']);
    exit();
}

$villageId = intval($_GET['village_id'] ?? 0);
$userId = $_SESSION['user_id'];

if ($villageId <= 0) {
    echo json_encode(['success' => false, 'error' => 'معرف القرية غير صحيح']);
    exit();
}

$db = getDB();

// التحقق من أن القرية تنتمي للمستخدم
$village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$villageId, $userId]);

if (!$village) {
    echo json_encode(['success' => false, 'error' => 'القرية غير موجودة']);
    exit();
}

// تحديث موارد القرية
$updated = updateVillageResources($villageId);

if (!$updated) {
    echo json_encode(['success' => false, 'error' => 'فشل في تحديث الموارد']);
    exit();
}

// الحصول على الموارد المحدثة
$resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);

if (!$resources) {
    echo json_encode(['success' => false, 'error' => 'لم يتم العثور على موارد القرية']);
    exit();
}

// حساب الإنتاج الحالي
$production = calculateResourceProduction($villageId);

// إرجاع البيانات
echo json_encode([
    'success' => true,
    'resources' => [
        'wood' => intval($resources['wood']),
        'clay' => intval($resources['clay']),
        'iron' => intval($resources['iron']),
        'crop' => intval($resources['crop'])
    ],
    'production' => [
        'wood' => intval($production['wood']),
        'clay' => intval($production['clay']),
        'iron' => intval($production['iron']),
        'crop' => intval($production['crop'])
    ],
    'last_update' => $resources['last_update']
]);
?>
