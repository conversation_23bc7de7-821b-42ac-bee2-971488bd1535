/* ملف CSS للوحة الإدارة */
/* Admin Panel CSS */

/* التخطيط العام */
.admin-page {
    background-color: #f8f9fa;
    min-height: 100vh;
}

.admin-container {
    display: flex;
    min-height: calc(100vh - 60px);
}

/* الشريط الجانبي */
.admin-sidebar {
    width: 250px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 20px 0;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.admin-sidebar .sidebar-header {
    padding: 0 20px 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    margin-bottom: 20px;
}

.admin-sidebar .sidebar-header h3 {
    color: white;
    margin: 0;
    font-size: 1.2rem;
}

.admin-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-nav li {
    margin-bottom: 5px;
}

.admin-nav a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.admin-nav a:hover,
.admin-nav a.active {
    background-color: rgba(255,255,255,0.1);
    color: white;
    border-right-color: #3498db;
}

.admin-nav .nav-icon {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* المحتوى الرئيسي */
.admin-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.admin-header {
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.admin-header h1 {
    color: #2c3e50;
    margin: 0;
    font-size: 2rem;
}

.admin-header p {
    color: #6c757d;
    margin: 5px 0 0;
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* شبكة الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-card.users {
    border-color: #007bff;
}

.stat-card.villages {
    border-color: #28a745;
}

.stat-card.attacks {
    border-color: #dc3545;
}

.stat-card.trades {
    border-color: #ffc107;
}

.stat-card.reports {
    border-color: #6f42c1;
}

.stat-card.server {
    border-color: #fd7e14;
}

.stat-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0,0,0,0.05);
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 3px;
}

.stat-change {
    color: #28a745;
    font-size: 0.8rem;
    font-weight: 500;
}

/* المحتوى الرئيسي */
.admin-main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.admin-section {
    background: white;
    border-radius: 10px;
    padding: 25px;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

/* الجداول */
.table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.admin-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    font-weight: 600;
    padding: 12px 15px;
    text-align: right;
    border-bottom: 2px solid #dee2e6;
    font-size: 0.9rem;
}

.admin-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    color: #495057;
    font-size: 0.9rem;
}

.admin-table tr:hover {
    background-color: #f8f9fa;
}

.admin-table tr:last-child td {
    border-bottom: none;
}

/* الشارات */
.role-badge,
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.role-badge.admin {
    background-color: #dc3545;
    color: white;
}

.role-badge.user {
    background-color: #6c757d;
    color: white;
}

.status-badge.active {
    background-color: #28a745;
    color: white;
}

.status-badge.inactive {
    background-color: #6c757d;
    color: white;
}

.status-badge.banned {
    background-color: #dc3545;
    color: white;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    text-align: center;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.75rem;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

/* أدوات الإدارة */
.admin-tools {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.search-form {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.search-form input,
.search-form select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
}

.search-form input {
    flex: 1;
    min-width: 200px;
}

.search-form select {
    min-width: 150px;
}

/* قائمة الأحداث */
.events-list {
    max-height: 400px;
    overflow-y: auto;
}

.event-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.3s ease;
}

.event-item:hover {
    background-color: #f8f9fa;
}

.event-item:last-child {
    border-bottom: none;
}

.event-icon {
    font-size: 1.2rem;
    width: 30px;
    text-align: center;
}

.event-details {
    flex: 1;
}

.event-description {
    color: #495057;
    font-size: 0.9rem;
    margin-bottom: 3px;
}

.event-time {
    color: #6c757d;
    font-size: 0.8rem;
}

.no-events {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

/* معلومات الخادم */
.server-info {
    background: white;
    border-radius: 10px;
    padding: 25px;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.server-info h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.server-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.server-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.server-stat .label {
    color: #6c757d;
    font-size: 0.9rem;
}

.server-stat .value {
    color: #495057;
    font-weight: 500;
    font-size: 0.9rem;
}

/* تفاصيل المستخدم */
.user-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.user-info-card,
.user-stats-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.user-info-card h3,
.user-stats-card h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-item label {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

.info-item span {
    color: #495057;
    font-size: 0.95rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.stat-item .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-item .stat-label {
    color: #6c757d;
    font-size: 0.8rem;
}

/* معلومات الحظر */
.ban-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    color: #721c24;
}

.ban-info h4 {
    margin-bottom: 10px;
    color: #721c24;
}

/* قرى المستخدم */
.user-villages,
.user-activities {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 20px;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.user-villages h3,
.user-activities h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

/* قائمة الأنشطة */
.activities-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.3s ease;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    font-size: 1.2rem;
    width: 30px;
    text-align: center;
}

.activity-details {
    flex: 1;
}

.activity-description {
    color: #495057;
    font-size: 0.9rem;
    margin-bottom: 3px;
}

.activity-time {
    color: #6c757d;
    font-size: 0.8rem;
}

/* ترقيم الصفحات */
.pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 20px;
}

.page-link {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    color: #007bff;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.page-link.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

/* التنبيهات */
.alert {
    padding: 12px 20px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 1024px) {
    .admin-container {
        flex-direction: column;
    }
    
    .admin-sidebar {
        width: 100%;
        order: 2;
    }
    
    .admin-content {
        order: 1;
        padding: 20px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .admin-main-content {
        grid-template-columns: 1fr;
    }
    
    .user-details {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .admin-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .search-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-form input,
    .search-form select {
        width: 100%;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .server-stats {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .admin-content {
        padding: 15px;
    }
    
    .admin-section,
    .user-info-card,
    .user-stats-card,
    .user-villages,
    .user-activities {
        padding: 15px;
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .stat-icon {
        font-size: 2rem;
        width: 50px;
        height: 50px;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .table-container {
        font-size: 0.8rem;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 8px 10px;
    }
}

/* تحسينات للطباعة */
@media print {
    .admin-sidebar,
    .admin-tools,
    .action-buttons,
    .header-actions {
        display: none;
    }
    
    .admin-container {
        flex-direction: column;
    }
    
    .admin-content {
        padding: 0;
    }
    
    .stat-card,
    .admin-section,
    .user-info-card,
    .user-stats-card {
        border: 1px solid #000;
        box-shadow: none;
        break-inside: avoid;
        margin-bottom: 10px;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .admin-page {
        background-color: #1a1a1a;
    }
    
    .stat-card,
    .admin-section,
    .user-info-card,
    .user-stats-card,
    .user-villages,
    .user-activities,
    .admin-tools,
    .server-info {
        background-color: #2d2d2d;
        border-color: #444;
        color: #fff;
    }
    
    .admin-table {
        background-color: #2d2d2d;
        color: #fff;
    }
    
    .admin-table th {
        background: linear-gradient(135deg, #3d3d3d 0%, #4d4d4d 100%);
        color: #fff;
        border-color: #555;
    }
    
    .admin-table td {
        border-color: #444;
        color: #fff;
    }
    
    .admin-table tr:hover {
        background-color: #3d3d3d;
    }
    
    .search-form input,
    .search-form select {
        background-color: #3d3d3d;
        border-color: #555;
        color: #fff;
    }
    
    .event-item:hover,
    .activity-item:hover {
        background-color: #3d3d3d;
    }
    
    .stat-item,
    .server-stat {
        background-color: #3d3d3d;
        border-color: #555;
        color: #fff;
    }
}
