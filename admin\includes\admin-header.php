<?php
/**
 * هيدر لوحة الإدارة
 * Admin Panel Header
 */
?>
<header class="admin-header-bar">
    <div class="header-container">
        <div class="header-left">
            <div class="site-logo">
                <a href="../index.php">
                    <img src="../images/logo.png" alt="<?php echo SITE_NAME; ?>" class="logo-img">
                    <span class="logo-text"><?php echo SITE_NAME; ?></span>
                </a>
            </div>
        </div>
        
        <div class="header-center">
            <h2 class="admin-title">لوحة تحكم الإدارة</h2>
        </div>
        
        <div class="header-right">
            <div class="admin-user-menu">
                <div class="user-info">
                    <span class="user-name"><?php echo htmlspecialchars($_SESSION['username']); ?></span>
                    <span class="user-role">مدير</span>
                </div>
                
                <div class="user-dropdown">
                    <button class="dropdown-toggle" onclick="toggleDropdown()">
                        <img src="../images/avatars/admin.png" alt="صورة المدير" class="user-avatar">
                        <span class="dropdown-arrow">▼</span>
                    </button>
                    
                    <div class="dropdown-menu" id="userDropdown">
                        <a href="../profile.php" class="dropdown-item">
                            <span class="item-icon">👤</span>
                            الملف الشخصي
                        </a>
                        <a href="../settings.php" class="dropdown-item">
                            <span class="item-icon">⚙️</span>
                            الإعدادات
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="../index.php" class="dropdown-item">
                            <span class="item-icon">🏠</span>
                            العودة للعبة
                        </a>
                        <a href="../logout.php" class="dropdown-item logout">
                            <span class="item-icon">🚪</span>
                            تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="header-actions">
                <button class="action-btn" onclick="toggleSidebar()" title="إخفاء/إظهار القائمة الجانبية">
                    <span class="btn-icon">☰</span>
                </button>
                
                <button class="action-btn" onclick="refreshPage()" title="تحديث الصفحة">
                    <span class="btn-icon">🔄</span>
                </button>
                
                <div class="notifications-btn">
                    <button class="action-btn" onclick="toggleNotifications()" title="الإشعارات">
                        <span class="btn-icon">🔔</span>
                        <span class="notification-badge">3</span>
                    </button>
                    
                    <div class="notifications-dropdown" id="notificationsDropdown">
                        <div class="notifications-header">
                            <h4>الإشعارات</h4>
                            <button class="mark-all-read" onclick="markAllRead()">تحديد الكل كمقروء</button>
                        </div>
                        
                        <div class="notifications-list">
                            <div class="notification-item unread">
                                <div class="notification-icon">👤</div>
                                <div class="notification-content">
                                    <div class="notification-text">مستخدم جديد انضم للعبة</div>
                                    <div class="notification-time">منذ 5 دقائق</div>
                                </div>
                            </div>
                            
                            <div class="notification-item unread">
                                <div class="notification-icon">⚠️</div>
                                <div class="notification-content">
                                    <div class="notification-text">تقرير مخالفة جديد</div>
                                    <div class="notification-time">منذ 15 دقيقة</div>
                                </div>
                            </div>
                            
                            <div class="notification-item">
                                <div class="notification-icon">📊</div>
                                <div class="notification-content">
                                    <div class="notification-text">تم إنشاء تقرير يومي</div>
                                    <div class="notification-time">منذ ساعة</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="notifications-footer">
                            <a href="notifications.php" class="view-all-link">عرض جميع الإشعارات</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<style>
/* هيدر لوحة الإدارة */
.admin-header-bar {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    max-width: 100%;
}

/* الشعار */
.header-left .site-logo a {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    color: white;
}

.logo-img {
    width: 40px;
    height: 40px;
    border-radius: 6px;
}

.logo-text {
    font-size: 1.2rem;
    font-weight: bold;
}

/* العنوان */
.header-center .admin-title {
    margin: 0;
    font-size: 1.3rem;
    color: white;
    text-align: center;
}

/* قائمة المستخدم */
.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-user-menu {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.75rem;
    color: rgba(255,255,255,0.7);
}

.user-dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 5px;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.dropdown-toggle:hover {
    background-color: rgba(255,255,255,0.1);
}

.user-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: 2px solid rgba(255,255,255,0.3);
}

.dropdown-arrow {
    font-size: 0.7rem;
    transition: transform 0.3s ease;
}

.dropdown-toggle.active .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    color: #495057;
    text-decoration: none;
    transition: background-color 0.3s ease;
    border-bottom: 1px solid #e9ecef;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item.logout {
    color: #dc3545;
}

.dropdown-item.logout:hover {
    background-color: #f8d7da;
}

.dropdown-divider {
    height: 1px;
    background-color: #e9ecef;
    margin: 5px 0;
}

.item-icon {
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

/* أزرار الإجراءات */
.header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.action-btn {
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.action-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-1px);
}

.btn-icon {
    font-size: 1.1rem;
}

/* الإشعارات */
.notifications-btn {
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -5px;
    left: -5px;
    background: #dc3545;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.notifications-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    width: 300px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.notifications-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.notifications-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
}

.notifications-header h4 {
    margin: 0;
    color: #495057;
    font-size: 1rem;
}

.mark-all-read {
    background: none;
    border: none;
    color: #007bff;
    font-size: 0.8rem;
    cursor: pointer;
    text-decoration: underline;
}

.notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.3s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background-color: #e7f3ff;
    border-right: 3px solid #007bff;
}

.notification-icon {
    font-size: 1.2rem;
    width: 30px;
    text-align: center;
}

.notification-content {
    flex: 1;
}

.notification-text {
    color: #495057;
    font-size: 0.9rem;
    margin-bottom: 3px;
}

.notification-time {
    color: #6c757d;
    font-size: 0.75rem;
}

.notifications-footer {
    padding: 10px 15px;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.view-all-link {
    color: #007bff;
    text-decoration: none;
    font-size: 0.9rem;
}

.view-all-link:hover {
    text-decoration: underline;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .header-container {
        padding: 10px 15px;
    }
    
    .header-center .admin-title {
        display: none;
    }
    
    .user-info {
        display: none;
    }
    
    .header-actions {
        gap: 5px;
    }
    
    .action-btn {
        width: 35px;
        height: 35px;
    }
    
    .notifications-dropdown {
        width: 280px;
        right: 0;
        left: auto;
    }
}

@media (max-width: 480px) {
    .logo-text {
        display: none;
    }
    
    .notifications-dropdown {
        width: 250px;
    }
}
</style>

<script>
// وظائف JavaScript للهيدر
function toggleDropdown() {
    const dropdown = document.getElementById('userDropdown');
    const toggle = document.querySelector('.dropdown-toggle');
    
    dropdown.classList.toggle('show');
    toggle.classList.toggle('active');
}

function toggleNotifications() {
    const dropdown = document.getElementById('notificationsDropdown');
    dropdown.classList.toggle('show');
}

function toggleSidebar() {
    const sidebar = document.querySelector('.admin-sidebar');
    if (sidebar) {
        sidebar.classList.toggle('collapsed');
    }
}

function refreshPage() {
    location.reload();
}

function markAllRead() {
    const unreadItems = document.querySelectorAll('.notification-item.unread');
    unreadItems.forEach(item => {
        item.classList.remove('unread');
    });
    
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        badge.style.display = 'none';
    }
}

// إغلاق القوائم المنسدلة عند النقر خارجها
document.addEventListener('click', function(event) {
    const userDropdown = document.getElementById('userDropdown');
    const notificationsDropdown = document.getElementById('notificationsDropdown');
    const dropdownToggle = document.querySelector('.dropdown-toggle');
    const notificationsBtn = document.querySelector('.notifications-btn');
    
    if (!dropdownToggle.contains(event.target)) {
        userDropdown.classList.remove('show');
        dropdownToggle.classList.remove('active');
    }
    
    if (!notificationsBtn.contains(event.target)) {
        notificationsDropdown.classList.remove('show');
    }
});
</script>
