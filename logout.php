<?php
/**
 * صفحة تسجيل الخروج
 * Logout Page
 */

require_once 'config/config.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// حذف ملفات تعريف الارتباط للتذكر
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/', '', false, true);
}

// تدمير الجلسة
session_destroy();

// إعادة التوجيه إلى صفحة تسجيل الدخول
header('Location: login.php?logged_out=1');
exit();
?>
