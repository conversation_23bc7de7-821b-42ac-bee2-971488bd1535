<?php
class Farm {
    private $conn;
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    public function plantCrop($villageId, $cropType, $fieldX, $fieldY) {
        $harvestTime = date('Y-m-d H:i:s', strtotime('+2 hours'));
        
        $stmt = $this->conn->prepare("INSERT INTO crops (village_id, crop_type, field_x, field_y, harvest_time) VALUES (?, ?, ?, ?, ?)");
        return $stmt->execute([$villageId, $cropType, $fieldX, $fieldY, $harvestTime]);
    }
    
    public function harvestCrop($cropId) {
        $stmt = $this->conn->prepare("SELECT * FROM crops WHERE id = ? AND harvest_time <= NOW()");
        $stmt->execute([$cropId]);
        $crop = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($crop) {
            $yield = $this->calculateYield($crop['crop_type']);
            
            // حذف المحصول من الحقل
            $stmt = $this->conn->prepare("DELETE FROM crops WHERE id = ?");
            $stmt->execute([$cropId]);
            
            return $yield;
        }
        return 0;
    }
    
    private function calculateYield($cropType) {
        $yields = [
            'wheat' => rand(50, 100),
            'corn' => rand(40, 80),
            'rice' => rand(60, 120),
            'potato' => rand(30, 70)
        ];
        
        return $yields[$cropType] ?? 50;
    }
    
    public function getVillageCrops($villageId) {
        $stmt = $this->conn->prepare("SELECT * FROM crops WHERE village_id = ?");
        $stmt->execute([$villageId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>