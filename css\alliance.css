/**
 * تنسيق صفحة التحالفات
 * Alliance Page Styles
 */

.alliance-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.alliance-header {
    background: linear-gradient(135deg, #6f42c1 0%, #8e44ad 100%);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
}

.alliance-header h1 {
    margin: 0 0 20px 0;
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.alliance-nav {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.nav-link {
    padding: 10px 20px;
    background: rgba(255,255,255,0.2);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.alliance-section {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 20px;
}

.alliance-section h2 {
    color: #2c3e50;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 15px;
    margin-bottom: 25px;
    font-size: 1.8rem;
}

.alliance-section h3 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.4rem;
}

.alliance-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.info-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #6f42c1;
}

.info-item strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 5px;
}

.alliance-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.detail-item {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.detail-item strong {
    color: #6f42c1;
    display: block;
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.alliance-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 25px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    font-size: 14px;
}

.btn-primary {
    background: #6f42c1;
    color: white;
}

.btn-primary:hover {
    background: #5a2d91;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

.alliances-table,
.members-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.alliances-table th,
.alliances-table td,
.members-table th,
.members-table td {
    padding: 15px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.alliances-table th,
.members-table th {
    background: linear-gradient(135deg, #6f42c1 0%, #8e44ad 100%);
    color: white;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

.alliances-table tr:hover,
.members-table tr:hover {
    background: #f8f9fa;
}

.alliances-table td:first-child {
    font-weight: bold;
    color: #6f42c1;
}

.alliance-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #2c3e50;
    font-size: 1rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #6f42c1;
    box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.alert {
    padding: 15px 20px;
    margin-bottom: 20px;
    border-radius: 6px;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* تصميم خاص للإحصائيات */
.alliance-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 25px;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #dee2e6;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #6f42c1;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .alliance-container {
        padding: 10px;
    }
    
    .alliance-header {
        padding: 20px;
    }
    
    .alliance-header h1 {
        font-size: 2rem;
    }
    
    .alliance-nav {
        flex-direction: column;
        align-items: center;
    }
    
    .alliance-section {
        padding: 20px;
    }
    
    .alliance-info,
    .alliance-details {
        grid-template-columns: 1fr;
    }
    
    .alliance-actions {
        flex-direction: column;
    }
    
    .alliances-table,
    .members-table {
        font-size: 0.9rem;
    }
    
    .alliances-table th,
    .alliances-table td,
    .members-table th,
    .members-table td {
        padding: 10px 8px;
    }
    
    .alliance-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .alliance-header h1 {
        font-size: 1.8rem;
    }
    
    .alliance-section h2 {
        font-size: 1.5rem;
    }
    
    .alliance-stats {
        grid-template-columns: 1fr;
    }
    
    .stat-value {
        font-size: 2rem;
    }
    
    .alliances-table,
    .members-table {
        font-size: 0.8rem;
    }
}

/* تأثيرات خاصة */
.alliance-badge {
    display: inline-block;
    padding: 4px 8px;
    background: #6f42c1;
    color: white;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-right: 8px;
}

.rank-leader {
    color: #ffc107;
    font-weight: bold;
}

.rank-member {
    color: #6c757d;
}

/* أنيميشن للتحميل */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.alliance-section {
    animation: fadeIn 0.5s ease-out;
}

/* تنسيق خاص للجداول على الشاشات الصغيرة */
@media (max-width: 600px) {
    .alliances-table,
    .members-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
}
