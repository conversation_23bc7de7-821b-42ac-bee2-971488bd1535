<?php
/**
 * صفحة الجيش
 * Army Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];
$villageId = $_SESSION['current_village_id'];

// التحقق من القرية
$village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$villageId, $userId]);
if (!$village) {
    redirect('index.php');
}

// تحديث الموارد
updateVillageResources($villageId);

// الحصول على الوحدات العسكرية
$units = $db->select("SELECT * FROM units WHERE village_id = ?", [$villageId]);
$unitsArray = [];
foreach ($units as $unit) {
    $unitsArray[$unit['unit_type']] = $unit;
}

// الحصول على الهجمات الواردة
$incomingAttacks = $db->select("SELECT a.*, v.name as attacker_village_name, u.username as attacker_username 
                                FROM attacks a 
                                JOIN villages v ON a.attacker_village_id = v.id 
                                JOIN users u ON v.user_id = u.id 
                                WHERE a.target_village_id = ? AND a.is_completed = 0 
                                ORDER BY a.arrival_time ASC", [$villageId]);

// الحصول على الهجمات الصادرة
$outgoingAttacks = $db->select("SELECT a.*, v.name as target_village_name, u.username as target_username 
                                FROM attacks a 
                                JOIN villages v ON a.target_village_id = v.id 
                                JOIN users u ON v.user_id = u.id 
                                WHERE a.attacker_village_id = ? AND a.is_completed = 0 
                                ORDER BY a.arrival_time ASC", [$villageId]);

// الحصول على التجارات الواردة والصادرة
$incomingTrades = $db->select("SELECT t.*, v.name as sender_village_name, u.username as sender_username 
                               FROM trades t 
                               JOIN villages v ON t.sender_village_id = v.id 
                               JOIN users u ON v.user_id = u.id 
                               WHERE t.receiver_village_id = ? AND t.is_completed = 0 
                               ORDER BY t.arrival_time ASC", [$villageId]);

$outgoingTrades = $db->select("SELECT t.*, v.name as receiver_village_name, u.username as receiver_username 
                               FROM trades t 
                               JOIN villages v ON t.receiver_village_id = v.id 
                               JOIN users u ON v.user_id = u.id 
                               WHERE t.sender_village_id = ? AND t.is_completed = 0 
                               ORDER BY t.arrival_time ASC", [$villageId]);

// حساب إجمالي القوة العسكرية
$totalAttackPower = 0;
$totalDefensePower = 0;
$totalUnits = 0;

foreach ($units as $unit) {
    $unitStats = getUnitStats($unit['unit_type']);
    $totalAttackPower += $unit['count'] * $unitStats['attack'];
    $totalDefensePower += $unit['count'] * $unitStats['defense'];
    $totalUnits += $unit['count'];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الجيش - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/army.css">
</head>
<body class="game-page">
    <?php include 'includes/header.php'; ?>
    
    <div class="main-content">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="content">
            <div class="page-header">
                <h1>الجيش</h1>
                <p>إدارة القوات العسكرية والهجمات</p>
            </div>
            
            <!-- ملخص الجيش -->
            <div class="army-summary">
                <div class="summary-grid">
                    <div class="summary-card">
                        <h3>إجمالي الوحدات</h3>
                        <div class="stat-value"><?php echo formatNumber($totalUnits); ?></div>
                    </div>
                    <div class="summary-card">
                        <h3>القوة الهجومية</h3>
                        <div class="stat-value attack"><?php echo formatNumber($totalAttackPower); ?></div>
                    </div>
                    <div class="summary-card">
                        <h3>القوة الدفاعية</h3>
                        <div class="stat-value defense"><?php echo formatNumber($totalDefensePower); ?></div>
                    </div>
                    <div class="summary-card">
                        <h3>الحالة</h3>
                        <div class="stat-value status">
                            <?php if ($totalUnits > 0): ?>
                                <span class="ready">جاهز</span>
                            <?php else: ?>
                                <span class="not-ready">بحاجة لتدريب</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- الوحدات العسكرية -->
            <div class="army-units">
                <h3>الوحدات العسكرية</h3>
                
                <?php if (!empty($units) && $totalUnits > 0): ?>
                    <div class="units-grid">
                        <?php foreach (UNIT_TYPES as $unitType => $unitName): ?>
                            <?php
                            $unit = $unitsArray[$unitType] ?? null;
                            $count = $unit ? $unit['count'] : 0;
                            if ($count > 0):
                                $unitStats = getUnitStats($unitType);
                            ?>
                                <div class="unit-card">
                                    <div class="unit-image">
                                        <img src="images/units/<?php echo $unitType; ?>.png" alt="<?php echo $unitName; ?>">
                                        <span class="unit-count"><?php echo formatNumber($count); ?></span>
                                    </div>
                                    <div class="unit-info">
                                        <h4><?php echo $unitName; ?></h4>
                                        <div class="unit-stats">
                                            <div class="stat">
                                                <span class="label">هجوم:</span>
                                                <span class="value"><?php echo $unitStats['attack']; ?></span>
                                            </div>
                                            <div class="stat">
                                                <span class="label">دفاع:</span>
                                                <span class="value"><?php echo $unitStats['defense']; ?></span>
                                            </div>
                                            <div class="stat">
                                                <span class="label">سرعة:</span>
                                                <span class="value"><?php echo $unitStats['speed']; ?></span>
                                            </div>
                                            <div class="stat">
                                                <span class="label">حمولة:</span>
                                                <span class="value"><?php echo $unitStats['carry']; ?></span>
                                            </div>
                                        </div>
                                        <div class="unit-total-power">
                                            <span>القوة الإجمالية: <?php echo formatNumber($count * $unitStats['attack']); ?></span>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="no-units">
                        <img src="images/icons/army.png" alt="لا توجد وحدات">
                        <h4>لا توجد وحدات عسكرية</h4>
                        <p>ابدأ بتدريب الوحدات في الثكنة أو الإسطبل</p>
                        <div class="training-links">
                            <a href="barracks.php" class="btn btn-primary">الثكنة</a>
                            <a href="stable.php" class="btn btn-primary">الإسطبل</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- الهجمات الواردة -->
            <?php if (!empty($incomingAttacks)): ?>
                <div class="incoming-attacks">
                    <h3>الهجمات الواردة</h3>
                    <div class="attacks-list">
                        <?php foreach ($incomingAttacks as $attack): ?>
                            <div class="attack-item incoming">
                                <div class="attack-info">
                                    <div class="attacker">
                                        <strong><?php echo htmlspecialchars($attack['attacker_username']); ?></strong>
                                        <span>من <?php echo htmlspecialchars($attack['attacker_village_name']); ?></span>
                                    </div>
                                    <div class="attack-type">
                                        <?php echo getAttackTypeName($attack['attack_type']); ?>
                                    </div>
                                    <div class="arrival-time">
                                        <span class="label">وقت الوصول:</span>
                                        <span class="timer" data-finish-time="<?php echo strtotime($attack['arrival_time']); ?>">
                                            <span class="time-remaining"></span>
                                        </span>
                                    </div>
                                </div>
                                <div class="attack-actions">
                                    <a href="attack-details.php?id=<?php echo $attack['id']; ?>" class="btn btn-small">التفاصيل</a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- الهجمات الصادرة -->
            <?php if (!empty($outgoingAttacks)): ?>
                <div class="outgoing-attacks">
                    <h3>الهجمات الصادرة</h3>
                    <div class="attacks-list">
                        <?php foreach ($outgoingAttacks as $attack): ?>
                            <div class="attack-item outgoing">
                                <div class="attack-info">
                                    <div class="target">
                                        <strong><?php echo htmlspecialchars($attack['target_username']); ?></strong>
                                        <span>إلى <?php echo htmlspecialchars($attack['target_village_name']); ?></span>
                                    </div>
                                    <div class="attack-type">
                                        <?php echo getAttackTypeName($attack['attack_type']); ?>
                                    </div>
                                    <div class="arrival-time">
                                        <span class="label">وقت الوصول:</span>
                                        <span class="timer" data-finish-time="<?php echo strtotime($attack['arrival_time']); ?>">
                                            <span class="time-remaining"></span>
                                        </span>
                                    </div>
                                </div>
                                <div class="attack-actions">
                                    <a href="attack-details.php?id=<?php echo $attack['id']; ?>" class="btn btn-small">التفاصيل</a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- التجارات -->
            <?php if (!empty($incomingTrades) || !empty($outgoingTrades)): ?>
                <div class="trades-section">
                    <h3>التجارات النشطة</h3>
                    
                    <?php if (!empty($incomingTrades)): ?>
                        <h4>التجارات الواردة</h4>
                        <div class="trades-list">
                            <?php foreach ($incomingTrades as $trade): ?>
                                <div class="trade-item incoming">
                                    <div class="trade-info">
                                        <div class="trader">
                                            <strong><?php echo htmlspecialchars($trade['sender_username']); ?></strong>
                                            <span>من <?php echo htmlspecialchars($trade['sender_village_name']); ?></span>
                                        </div>
                                        <div class="trade-resources">
                                            <?php if ($trade['wood_amount'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/wood.png" alt="خشب">
                                                    <?php echo formatNumber($trade['wood_amount']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($trade['clay_amount'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/clay.png" alt="طين">
                                                    <?php echo formatNumber($trade['clay_amount']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($trade['iron_amount'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/iron.png" alt="حديد">
                                                    <?php echo formatNumber($trade['iron_amount']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($trade['crop_amount'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/crop.png" alt="قمح">
                                                    <?php echo formatNumber($trade['crop_amount']); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="arrival-time">
                                            <span class="timer" data-finish-time="<?php echo strtotime($trade['arrival_time']); ?>">
                                                <span class="time-remaining"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($outgoingTrades)): ?>
                        <h4>التجارات الصادرة</h4>
                        <div class="trades-list">
                            <?php foreach ($outgoingTrades as $trade): ?>
                                <div class="trade-item outgoing">
                                    <div class="trade-info">
                                        <div class="trader">
                                            <strong><?php echo htmlspecialchars($trade['receiver_username']); ?></strong>
                                            <span>إلى <?php echo htmlspecialchars($trade['receiver_village_name']); ?></span>
                                        </div>
                                        <div class="trade-resources">
                                            <?php if ($trade['wood_amount'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/wood.png" alt="خشب">
                                                    <?php echo formatNumber($trade['wood_amount']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($trade['clay_amount'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/clay.png" alt="طين">
                                                    <?php echo formatNumber($trade['clay_amount']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($trade['iron_amount'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/iron.png" alt="حديد">
                                                    <?php echo formatNumber($trade['iron_amount']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($trade['crop_amount'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/crop.png" alt="قمح">
                                                    <?php echo formatNumber($trade['crop_amount']); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="arrival-time">
                                            <span class="timer" data-finish-time="<?php echo strtotime($trade['arrival_time']); ?>">
                                                <span class="time-remaining"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <!-- الإجراءات السريعة -->
            <div class="quick-actions">
                <h3>الإجراءات العسكرية</h3>
                <div class="actions-grid">
                    <a href="barracks.php" class="action-btn">
                        <img src="images/icons/barracks.png" alt="الثكنة">
                        <span>تدريب المشاة</span>
                    </a>
                    <a href="stable.php" class="action-btn">
                        <img src="images/icons/stable.png" alt="الإسطبل">
                        <span>تدريب الفرسان</span>
                    </a>
                    <a href="map.php" class="action-btn">
                        <img src="images/icons/map.png" alt="الخريطة">
                        <span>البحث عن أهداف</span>
                    </a>
                    <a href="reports.php" class="action-btn">
                        <img src="images/icons/reports.png" alt="التقارير">
                        <span>تقارير المعارك</span>
                    </a>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // تحديث المؤقتات
        function updateTimers() {
            document.querySelectorAll('.timer').forEach(timer => {
                const finishTime = parseInt(timer.dataset.finishTime);
                const now = Math.floor(Date.now() / 1000);
                const remaining = finishTime - now;
                
                const timeElement = timer.querySelector('.time-remaining');
                if (remaining <= 0) {
                    timeElement.textContent = 'وصل!';
                    timer.classList.add('completed');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    timeElement.textContent = formatDuration(remaining);
                }
            });
        }
        
        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            if (hours > 0) {
                return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
        }
        
        // تحديث المؤقتات كل ثانية
        setInterval(updateTimers, 1000);
        updateTimers();
    </script>
</body>
</html>

<?php
function getUnitStats($unitType) {
    $stats = [
        'phalanx' => ['attack' => 15, 'defense' => 40, 'speed' => 7, 'carry' => 25],
        'swordsman' => ['attack' => 65, 'defense' => 35, 'speed' => 6, 'carry' => 45],
        'pathfinder' => ['attack' => 0, 'defense' => 20, 'speed' => 17, 'carry' => 0],
        'theutates_thunder' => ['attack' => 90, 'defense' => 25, 'speed' => 19, 'carry' => 75],
        'druidrider' => ['attack' => 45, 'defense' => 115, 'speed' => 16, 'carry' => 35],
        'haeduan' => ['attack' => 140, 'defense' => 60, 'speed' => 13, 'carry' => 65],
        'ram' => ['attack' => 50, 'defense' => 30, 'speed' => 4, 'carry' => 0],
        'trebuchet' => ['attack' => 70, 'defense' => 45, 'speed' => 3, 'carry' => 0],
        'chieftain' => ['attack' => 40, 'defense' => 50, 'speed' => 5, 'carry' => 0],
        'settler' => ['attack' => 0, 'defense' => 80, 'speed' => 5, 'carry' => 3000]
    ];
    
    return $stats[$unitType] ?? ['attack' => 0, 'defense' => 0, 'speed' => 0, 'carry' => 0];
}

function getAttackTypeName($type) {
    $types = [
        'normal' => 'هجوم عادي',
        'raid' => 'غارة',
        'siege' => 'حصار'
    ];
    return $types[$type] ?? $type;
}
?>
