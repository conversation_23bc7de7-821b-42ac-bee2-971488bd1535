<?php
session_start();
require_once 'config/database.php';
require_once 'classes/Army.php';
require_once 'classes/User.php';

if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$db = new Database();
$army = new Army($db->getConnection());
$user = new User($db->getConnection());

$villageId = $_GET['village_id'] ?? 1;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['train_units'])) {
        $result = $army->trainUnits($villageId, $_POST['unit_type'], $_POST['quantity'], $_SESSION['user_id']);
        if ($result) {
            $success = "بدأ تدريب الوحدات!";
        } else {
            $error = "فشل في تدريب الوحدات";
        }
    }
}

$armyUnits = $army->getVillageArmy($villageId);
$userData = $user->getUserById($_SESSION['user_id']);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>الجيش - المزرعة الذهبية</title>
    <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
    <div class="game-container">
        <?php include 'templates/header.php'; ?>
        
        <div class="army-container">
            <h2>إدارة الجيش</h2>
            
            <div class="army-sections">
                <div class="current-army">
                    <h3>جيشك الحالي</h3>
                    <div class="army-units">
                        <?php foreach ($armyUnits as $unit): ?>
                            <div class="army-unit">
                                <img src="assets/images/units/<?php echo $unit['unit_type']; ?>.png" alt="<?php echo $unit['unit_type']; ?>">
                                <h4><?php echo $this->getUnitName($unit['unit_type']); ?></h4>
                                <p>العدد: <?php echo $unit['quantity']; ?></p>
                                <div class="unit-stats">
                                    <span>القوة: <?php echo $this->getUnitPower($unit['unit_type']); ?></span>
                                    <span>الدفاع: <?php echo $this->getUnitDefense($unit['unit_type']); ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="train-units">
                    <h3>تدريب وحدات جديدة</h3>
                    <div class="training-options">
                        <div class="unit-option">
                            <img src="assets/images/units/warrior.png" alt="محارب">
                            <h4>محارب</h4>
                            <p>التكلفة: 50 ذهب، 25 طعام</p>
                            <p>الوقت: 10 دقائق</p>
                            <form method="POST" class="train-form">
                                <input type="hidden" name="unit_type" value="warrior">
                                <input type="number" name="quantity" min="1" max="100" value="1">
                                <button type="submit" name="train_units">تدريب</button>
                            </form>
                        </div>
                        
                        <div class="unit-option">
                            <img src="assets/images/units/archer.png" alt="رامي">
                            <h4>رامي سهام</h4>
                            <p>التكلفة: 75 ذهب، 35 طعام، 10 خشب</p>
                            <p>الوقت: 15 دقيقة</p>
                            <form method="POST" class="train-form">
                                <input type="hidden" name="unit_type" value="archer">
                                <input type="number" name="quantity" min="1" max="50" value="1">
                                <button type="submit" name="train_units">تدريب</button>
                            </form>
                        </div>
                        
                        <div class="unit-option">
                            <img src="assets/images/units/cavalry.png" alt="فارس">
                            <h4>فارس</h4>
                            <p>التكلفة: 150 ذهب، 60 طعام، 25 خشب</p>
                            <p>الوقت: 30 دقيقة</p>
                            <form method="POST" class="train-form">
                                <input type="hidden" name="unit_type" value="cavalry">
                                <input type="number" name="quantity" min="1" max="20" value="1">
                                <button type="submit" name="train_units">تدريب</button>
                            </form>
                        </div>
                        
                        <div class="unit-option">
                            <img src="assets/images/units/siege.png" alt="آلة حصار">
                            <h4>آلة حصار</h4>
                            <p>التكلفة: 500 ذهب، 200 خشب، 100 حجر</p>
                            <p>الوقت: 2 ساعة</p>
                            <form method="POST" class="train-form">
                                <input type="hidden" name="unit_type" value="siege">
                                <input type="number" name="quantity" min="1" max="5" value="1">
                                <button type="submit" name="train_units">تدريب</button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="battle-section">
                    <h3>المعارك والغارات</h3>
                    <div class="battle-options">
                        <a href="attack.php" class="btn-attack">مهاجمة قرية</a>
                        <a href="defend.php" class="btn-defend">الدفاع عن القرية</a>
                        <a href="scout.php" class="btn-scout">استطلاع</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="assets/js/army.js"></script>
</body>
</html>