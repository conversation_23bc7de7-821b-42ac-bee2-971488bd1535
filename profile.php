<?php
/**
 * صفحة الملف الشخصي
 * User Profile Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];

// الحصول على بيانات المستخدم
$user = $db->selectOne("SELECT * FROM users WHERE id = ?", [$userId]);
if (!$user) {
    redirect('logout.php');
}

// الحصول على إحصائيات المستخدم
$userStats = [
    'total_villages' => $db->selectOne("SELECT COUNT(*) as count FROM villages WHERE user_id = ?", [$userId])['count'],
    'total_population' => $db->selectOne("SELECT SUM(population) as total FROM villages WHERE user_id = ?", [$userId])['total'] ?? 0,
    'total_attacks_sent' => $db->selectOne("SELECT COUNT(*) as count FROM attacks WHERE attacker_village_id IN (SELECT id FROM villages WHERE user_id = ?)", [$userId])['count'],
    'total_attacks_received' => $db->selectOne("SELECT COUNT(*) as count FROM attacks WHERE defender_village_id IN (SELECT id FROM villages WHERE user_id = ?)", [$userId])['count'],
    'total_trades' => $db->selectOne("SELECT COUNT(*) as count FROM trades WHERE sender_village_id IN (SELECT id FROM villages WHERE user_id = ?) OR receiver_village_id IN (SELECT id FROM villages WHERE user_id = ?)", [$userId, $userId])['count']
];

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $email = cleanInput($_POST['email']);
        
        // التحقق من صحة البريد الإلكتروني
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'البريد الإلكتروني غير صحيح';
        } else {
            // التحقق من عدم وجود البريد الإلكتروني
            $existing = $db->selectOne("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $userId]);
            if ($existing) {
                $error = 'البريد الإلكتروني مستخدم من قبل مستخدم آخر';
            } else {
                $db->update('users', ['email' => $email], ['id' => $userId]);
                $success = 'تم تحديث البريد الإلكتروني بنجاح';
                $user['email'] = $email;
            }
        }
    } elseif ($action === 'change_password') {
        $currentPassword = $_POST['current_password'];
        $newPassword = $_POST['new_password'];
        $confirmPassword = $_POST['confirm_password'];
        
        if (!verifyPassword($currentPassword, $user['password'])) {
            $error = 'كلمة المرور الحالية غير صحيحة';
        } elseif (strlen($newPassword) < 6) {
            $error = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
        } elseif ($newPassword !== $confirmPassword) {
            $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقين';
        } else {
            $hashedPassword = hashPassword($newPassword);
            $db->update('users', ['password' => $hashedPassword], ['id' => $userId]);
            $success = 'تم تغيير كلمة المرور بنجاح';
        }
    }
}

// الحصول على قرى المستخدم
$villages = $db->select("SELECT * FROM villages WHERE user_id = ? ORDER BY created_at ASC", [$userId]);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <style>
        .profile-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .profile-header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .profile-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .profile-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .profile-section h3 {
            color: #2c3e50;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .villages-list {
            display: grid;
            gap: 10px;
        }
        
        .village-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .village-name {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .village-coords {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .village-population {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .profile-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="profile-container">
        <?php if (isset($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <div class="profile-header">
            <h1>الملف الشخصي</h1>
            <p>مرحباً <?php echo htmlspecialchars($user['username']); ?>!</p>
            <p>عضو منذ: <?php echo date('d/m/Y', strtotime($user['created_at'])); ?></p>
        </div>
        
        <div class="profile-content">
            <!-- الإحصائيات -->
            <div class="profile-section">
                <h3>📊 الإحصائيات</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value"><?php echo $userStats['total_villages']; ?></div>
                        <div class="stat-label">القرى</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo formatNumber($userStats['total_population']); ?></div>
                        <div class="stat-label">إجمالي السكان</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo $userStats['total_attacks_sent']; ?></div>
                        <div class="stat-label">هجمات مرسلة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo $userStats['total_attacks_received']; ?></div>
                        <div class="stat-label">هجمات مستقبلة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo $userStats['total_trades']; ?></div>
                        <div class="stat-label">إجمالي التجارة</div>
                    </div>
                </div>
            </div>
            
            <!-- تحديث البريد الإلكتروني -->
            <div class="profile-section">
                <h3>✉️ تحديث البريد الإلكتروني</h3>
                <form method="POST">
                    <input type="hidden" name="action" value="update_profile">
                    <div class="form-group">
                        <label>البريد الإلكتروني:</label>
                        <input type="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                    </div>
                    <button type="submit" class="btn">تحديث البريد الإلكتروني</button>
                </form>
            </div>
            
            <!-- تغيير كلمة المرور -->
            <div class="profile-section">
                <h3>🔒 تغيير كلمة المرور</h3>
                <form method="POST">
                    <input type="hidden" name="action" value="change_password">
                    <div class="form-group">
                        <label>كلمة المرور الحالية:</label>
                        <input type="password" name="current_password" required>
                    </div>
                    <div class="form-group">
                        <label>كلمة المرور الجديدة:</label>
                        <input type="password" name="new_password" required>
                    </div>
                    <div class="form-group">
                        <label>تأكيد كلمة المرور الجديدة:</label>
                        <input type="password" name="confirm_password" required>
                    </div>
                    <button type="submit" class="btn">تغيير كلمة المرور</button>
                </form>
            </div>
            
            <!-- قائمة القرى -->
            <div class="profile-section">
                <h3>🏘️ قراي</h3>
                <?php if (!empty($villages)): ?>
                    <div class="villages-list">
                        <?php foreach ($villages as $village): ?>
                            <div class="village-item">
                                <div>
                                    <div class="village-name"><?php echo htmlspecialchars($village['name']); ?></div>
                                    <div class="village-coords">(<?php echo $village['x']; ?>|<?php echo $village['y']; ?>)</div>
                                </div>
                                <div class="village-population"><?php echo formatNumber($village['population']); ?> نسمة</div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p>لا توجد قرى بعد. <a href="map.php">ابحث عن موقع لقريتك الأولى</a></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <?php include 'includes/sidebar.php'; ?>
    
    <script src="js/game.js"></script>
</body>
</html>
