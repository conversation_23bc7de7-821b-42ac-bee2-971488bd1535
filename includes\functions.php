<?php
/**
 * ملف الدوال المساعدة العامة
 * General Helper Functions
 */

require_once '../config/config.php';

/**
 * دوال إدارة المستخدمين
 */

// تشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password . SALT, PASSWORD_DEFAULT);
}

// التحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password . SALT, $hash);
}

// إنشاء مستخدم جديد
function createUser($username, $email, $password, $tribe_id = 1) {
    $db = getDB();
    
    // التحقق من عدم وجود المستخدم
    $existing = $db->selectOne("SELECT id FROM users WHERE username = ? OR email = ?", [$username, $email]);
    if ($existing) {
        return false;
    }
    
    $hashedPassword = hashPassword($password);
    
    $db->beginTransaction();
    try {
        // إنشاء المستخدم
        $userId = $db->insert("INSERT INTO users (username, email, password, tribe_id) VALUES (?, ?, ?, ?)", 
                             [$username, $email, $hashedPassword, $tribe_id]);
        
        if (!$userId) {
            throw new Exception("فشل في إنشاء المستخدم");
        }
        
        // إنشاء القرية الأولى
        $villageId = createFirstVillage($userId);
        if (!$villageId) {
            throw new Exception("فشل في إنشاء القرية الأولى");
        }
        
        $db->commit();
        return $userId;
    } catch (Exception $e) {
        $db->rollback();
        error_log("Error creating user: " . $e->getMessage());
        return false;
    }
}

// إنشاء القرية الأولى للمستخدم
function createFirstVillage($userId) {
    $db = getDB();
    
    // العثور على إحداثيات فارغة
    $coordinates = findEmptyCoordinates();
    if (!$coordinates) {
        return false;
    }
    
    // إنشاء القرية
    $villageId = $db->insert("INSERT INTO villages (user_id, name, x_coordinate, y_coordinate, is_capital) VALUES (?, ?, ?, ?, ?)", 
                            [$userId, "قرية " . $userId, $coordinates['x'], $coordinates['y'], true]);
    
    if (!$villageId) {
        return false;
    }
    
    // إنشاء الموارد الابتدائية
    $db->insert("INSERT INTO resources (village_id, wood, clay, iron, crop) VALUES (?, ?, ?, ?, ?)", 
               [$villageId, STARTING_RESOURCES, STARTING_RESOURCES, STARTING_RESOURCES, STARTING_RESOURCES]);
    
    // إنشاء الحقول الابتدائية (18 حقل)
    for ($i = 1; $i <= 18; $i++) {
        $fieldType = ($i <= 4) ? 'wood' : (($i <= 8) ? 'clay' : (($i <= 12) ? 'iron' : 'crop'));
        $db->insert("INSERT INTO fields (village_id, field_type, position, level) VALUES (?, ?, ?, ?)", 
                   [$villageId, $fieldType, $i, 0]);
    }
    
    // إنشاء المباني الأساسية
    $basicBuildings = ['main_building', 'warehouse', 'granary'];
    foreach ($basicBuildings as $building) {
        $level = ($building == 'main_building') ? 1 : 1;
        $db->insert("INSERT INTO buildings (village_id, building_type, level) VALUES (?, ?, ?)", 
                   [$villageId, $building, $level]);
    }
    
    return $villageId;
}

// العثور على إحداثيات فارغة
function findEmptyCoordinates() {
    $db = getDB();
    
    for ($attempts = 0; $attempts < 100; $attempts++) {
        $x = rand(-50, 50);
        $y = rand(-50, 50);
        
        $existing = $db->selectOne("SELECT id FROM villages WHERE x_coordinate = ? AND y_coordinate = ?", [$x, $y]);
        if (!$existing) {
            return ['x' => $x, 'y' => $y];
        }
    }
    
    return false;
}

/**
 * دوال إدارة الموارد
 */

// حساب إنتاج الموارد
function calculateResourceProduction($villageId) {
    $db = getDB();
    
    $production = [
        'wood' => 0,
        'clay' => 0,
        'iron' => 0,
        'crop' => 0
    ];
    
    // حساب إنتاج الحقول
    $fields = $db->select("SELECT field_type, level FROM fields WHERE village_id = ?", [$villageId]);
    
    foreach ($fields as $field) {
        $baseProduction = BASE_PRODUCTION_PER_HOUR;
        $levelMultiplier = 1 + ($field['level'] * 0.5);
        $production[$field['field_type']] += $baseProduction * $levelMultiplier;
    }
    
    // تطبيق مضاعف سرعة اللعبة
    foreach ($production as &$value) {
        $value *= GAME_SPEED;
    }
    
    return $production;
}

// تحديث موارد القرية
function updateVillageResources($villageId) {
    $db = getDB();
    
    $village = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);
    if (!$village) {
        return false;
    }
    
    $lastUpdate = strtotime($village['last_update']);
    $currentTime = time();
    $timeDiff = ($currentTime - $lastUpdate) / 3600; // بالساعات
    
    if ($timeDiff <= 0) {
        return true;
    }
    
    $production = calculateResourceProduction($villageId);
    $capacity = getStorageCapacity($villageId);
    
    // حساب الموارد الجديدة
    $newResources = [
        'wood' => min($village['wood'] + ($production['wood'] * $timeDiff), $capacity['warehouse']),
        'clay' => min($village['clay'] + ($production['clay'] * $timeDiff), $capacity['warehouse']),
        'iron' => min($village['iron'] + ($production['iron'] * $timeDiff), $capacity['warehouse']),
        'crop' => min($village['crop'] + ($production['crop'] * $timeDiff), $capacity['granary'])
    ];
    
    // تحديث قاعدة البيانات
    return $db->update("UPDATE resources SET wood = ?, clay = ?, iron = ?, crop = ?, last_update = NOW() WHERE village_id = ?", 
                      [$newResources['wood'], $newResources['clay'], $newResources['iron'], $newResources['crop'], $villageId]);
}

// الحصول على سعة التخزين
function getStorageCapacity($villageId) {
    $db = getDB();
    
    $warehouse = $db->selectOne("SELECT level FROM buildings WHERE village_id = ? AND building_type = 'warehouse'", [$villageId]);
    $granary = $db->selectOne("SELECT level FROM buildings WHERE village_id = ? AND building_type = 'granary'", [$villageId]);
    
    $warehouseLevel = $warehouse ? $warehouse['level'] : 1;
    $granaryLevel = $granary ? $granary['level'] : 1;
    
    return [
        'warehouse' => WAREHOUSE_CAPACITY_BASE * pow(1.2, $warehouseLevel),
        'granary' => GRANARY_CAPACITY_BASE * pow(1.2, $granaryLevel)
    ];
}

/**
 * دوال إدارة المباني
 */

// حساب تكلفة ترقية المبنى
function getBuildingUpgradeCost($buildingType, $currentLevel) {
    $baseCosts = [
        'main_building' => ['wood' => 70, 'clay' => 40, 'iron' => 60, 'crop' => 20],
        'warehouse' => ['wood' => 130, 'clay' => 160, 'iron' => 90, 'crop' => 40],
        'granary' => ['wood' => 80, 'clay' => 100, 'iron' => 70, 'crop' => 20],
        'barracks' => ['wood' => 210, 'clay' => 140, 'iron' => 260, 'crop' => 120],
        'stable' => ['wood' => 260, 'clay' => 140, 'iron' => 220, 'crop' => 100]
    ];
    
    if (!isset($baseCosts[$buildingType])) {
        return false;
    }
    
    $cost = [];
    $multiplier = pow(1.28, $currentLevel);
    
    foreach ($baseCosts[$buildingType] as $resource => $amount) {
        $cost[$resource] = ceil($amount * $multiplier);
    }
    
    return $cost;
}

// حساب وقت البناء
function getBuildingTime($buildingType, $currentLevel, $mainBuildingLevel = 1) {
    $baseTimes = [
        'main_building' => 1080,
        'warehouse' => 1200,
        'granary' => 1200,
        'barracks' => 1800,
        'stable' => 1980
    ];
    
    if (!isset($baseTimes[$buildingType])) {
        return 3600; // ساعة واحدة افتراضياً
    }
    
    $baseTime = $baseTimes[$buildingType];
    $levelMultiplier = pow(1.18, $currentLevel);
    $mainBuildingBonus = 1 - ($mainBuildingLevel * 0.05);
    
    return ceil($baseTime * $levelMultiplier * $mainBuildingBonus * BUILDING_TIME_FACTOR);
}

/**
 * دوال إدارة الوقت
 */

// تحويل الثواني إلى تنسيق قابل للقراءة
function formatDuration($seconds) {
    if ($seconds <= 0) return "00:00:00";
    
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $seconds = $seconds % 60;
    
    return sprintf("%02d:%02d:%02d", $hours, $minutes, $seconds);
}

// حساب الوقت المتبقي
function getTimeRemaining($endTime) {
    $remaining = strtotime($endTime) - time();
    return max(0, $remaining);
}

/**
 * دوال الأمان
 */

// التحقق من صحة البريد الإلكتروني
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// التحقق من قوة كلمة المرور
function isStrongPassword($password) {
    return strlen($password) >= 6 && 
           preg_match('/[A-Za-z]/', $password) && 
           preg_match('/[0-9]/', $password);
}

// تنظيف المدخلات
function cleanInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// التحقق من رمز CSRF
function validateCSRF($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// إنشاء رمز CSRF
function generateCSRF() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}
?>
