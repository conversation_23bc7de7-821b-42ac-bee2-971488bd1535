<?php
/**
 * الشريط الجانبي للوحة الإدارة
 * Admin Panel Sidebar
 */

// تحديد الصفحة الحالية
$currentPage = basename($_SERVER['PHP_SELF']);
?>
<aside class="admin-sidebar" id="adminSidebar">
    <div class="sidebar-header">
        <h3>لوحة التحكم</h3>
        <p>إدارة شاملة للعبة</p>
    </div>
    
    <nav class="sidebar-nav">
        <ul class="admin-nav">
            <!-- الرئيسية -->
            <li>
                <a href="index.php" class="nav-link <?php echo $currentPage === 'index.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">🏠</span>
                    <span class="nav-text">الرئيسية</span>
                </a>
            </li>
            
            <!-- إدارة المستخدمين -->
            <li class="nav-section">
                <div class="section-title">إدارة المستخدمين</div>
            </li>
            
            <li>
                <a href="users.php" class="nav-link <?php echo $currentPage === 'users.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">👥</span>
                    <span class="nav-text">المستخدمون</span>
                    <span class="nav-badge">جديد</span>
                </a>
            </li>
            
            <li>
                <a href="banned-users.php" class="nav-link <?php echo $currentPage === 'banned-users.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">🚫</span>
                    <span class="nav-text">المستخدمون المحظورون</span>
                </a>
            </li>
            
            <li>
                <a href="user-reports.php" class="nav-link <?php echo $currentPage === 'user-reports.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">📋</span>
                    <span class="nav-text">تقارير المستخدمين</span>
                </a>
            </li>
            
            <!-- إدارة اللعبة -->
            <li class="nav-section">
                <div class="section-title">إدارة اللعبة</div>
            </li>
            
            <li>
                <a href="villages.php" class="nav-link <?php echo $currentPage === 'villages.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">🏘️</span>
                    <span class="nav-text">القرى</span>
                </a>
            </li>
            
            <li>
                <a href="attacks.php" class="nav-link <?php echo $currentPage === 'attacks.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">⚔️</span>
                    <span class="nav-text">الهجمات</span>
                </a>
            </li>
            
            <li>
                <a href="trades.php" class="nav-link <?php echo $currentPage === 'trades.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">🚚</span>
                    <span class="nav-text">التجارة</span>
                </a>
            </li>
            
            <li>
                <a href="alliances.php" class="nav-link <?php echo $currentPage === 'alliances.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">🤝</span>
                    <span class="nav-text">التحالفات</span>
                </a>
            </li>
            
            <!-- الإحصائيات والتقارير -->
            <li class="nav-section">
                <div class="section-title">الإحصائيات والتقارير</div>
            </li>
            
            <li>
                <a href="statistics.php" class="nav-link <?php echo $currentPage === 'statistics.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">📊</span>
                    <span class="nav-text">الإحصائيات العامة</span>
                </a>
            </li>
            
            <li>
                <a href="activity-logs.php" class="nav-link <?php echo $currentPage === 'activity-logs.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">📝</span>
                    <span class="nav-text">سجل الأنشطة</span>
                </a>
            </li>
            
            <li>
                <a href="reports.php" class="nav-link <?php echo $currentPage === 'reports.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">📈</span>
                    <span class="nav-text">التقارير</span>
                </a>
            </li>
            
            <!-- إعدادات النظام -->
            <li class="nav-section">
                <div class="section-title">إعدادات النظام</div>
            </li>
            
            <li>
                <a href="game-settings.php" class="nav-link <?php echo $currentPage === 'game-settings.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">⚙️</span>
                    <span class="nav-text">إعدادات اللعبة</span>
                </a>
            </li>
            
            <li>
                <a href="server-settings.php" class="nav-link <?php echo $currentPage === 'server-settings.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">🖥️</span>
                    <span class="nav-text">إعدادات الخادم</span>
                </a>
            </li>
            
            <li>
                <a href="maintenance.php" class="nav-link <?php echo $currentPage === 'maintenance.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">🔧</span>
                    <span class="nav-text">الصيانة</span>
                </a>
            </li>
            
            <!-- الأدوات -->
            <li class="nav-section">
                <div class="section-title">الأدوات</div>
            </li>
            
            <li>
                <a href="backup.php" class="nav-link <?php echo $currentPage === 'backup.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">💾</span>
                    <span class="nav-text">النسخ الاحتياطي</span>
                </a>
            </li>
            
            <li>
                <a href="database.php" class="nav-link <?php echo $currentPage === 'database.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">🗄️</span>
                    <span class="nav-text">إدارة قاعدة البيانات</span>
                </a>
            </li>
            
            <li>
                <a href="logs.php" class="nav-link <?php echo $currentPage === 'logs.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">📄</span>
                    <span class="nav-text">ملفات السجل</span>
                </a>
            </li>
            
            <!-- الاتصالات -->
            <li class="nav-section">
                <div class="section-title">الاتصالات</div>
            </li>
            
            <li>
                <a href="announcements.php" class="nav-link <?php echo $currentPage === 'announcements.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">📢</span>
                    <span class="nav-text">الإعلانات</span>
                </a>
            </li>
            
            <li>
                <a href="messages.php" class="nav-link <?php echo $currentPage === 'messages.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">💬</span>
                    <span class="nav-text">الرسائل</span>
                </a>
            </li>
            
            <li>
                <a href="notifications.php" class="nav-link <?php echo $currentPage === 'notifications.php' ? 'active' : ''; ?>">
                    <span class="nav-icon">🔔</span>
                    <span class="nav-text">الإشعارات</span>
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- معلومات سريعة -->
    <div class="sidebar-footer">
        <div class="quick-stats">
            <h4>إحصائيات سريعة</h4>
            <div class="quick-stat">
                <span class="stat-label">المستخدمون النشطون:</span>
                <span class="stat-value" id="activeUsers">-</span>
            </div>
            <div class="quick-stat">
                <span class="stat-label">الهجمات اليوم:</span>
                <span class="stat-value" id="todayAttacks">-</span>
            </div>
            <div class="quick-stat">
                <span class="stat-label">حمولة الخادم:</span>
                <span class="stat-value" id="serverLoad">-</span>
            </div>
        </div>
        
        <div class="sidebar-actions">
            <button class="sidebar-btn" onclick="clearCache()" title="مسح التخزين المؤقت">
                <span class="btn-icon">🗑️</span>
                <span class="btn-text">مسح الكاش</span>
            </button>
            
            <button class="sidebar-btn" onclick="restartServer()" title="إعادة تشغيل الخادم">
                <span class="btn-icon">🔄</span>
                <span class="btn-text">إعادة التشغيل</span>
            </button>
        </div>
    </div>
    
    <!-- زر طي/فتح الشريط الجانبي -->
    <button class="sidebar-toggle" onclick="toggleSidebar()" title="طي/فتح القائمة">
        <span class="toggle-icon">◀</span>
    </button>
</aside>

<style>
/* الشريط الجانبي للإدارة */
.admin-sidebar {
    width: 280px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    height: 100vh;
    overflow-y: auto;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.admin-sidebar.collapsed {
    width: 70px;
}

.admin-sidebar.collapsed .nav-text,
.admin-sidebar.collapsed .section-title,
.admin-sidebar.collapsed .sidebar-header p,
.admin-sidebar.collapsed .sidebar-footer,
.admin-sidebar.collapsed .nav-badge {
    display: none;
}

.admin-sidebar.collapsed .sidebar-header h3 {
    font-size: 0.8rem;
    text-align: center;
}

/* رأس الشريط الجانبي */
.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
}

.sidebar-header h3 {
    margin: 0 0 5px;
    font-size: 1.3rem;
    color: white;
}

.sidebar-header p {
    margin: 0;
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
}

/* التنقل */
.sidebar-nav {
    padding: 20px 0;
}

.admin-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-nav li {
    margin-bottom: 2px;
}

/* عناوين الأقسام */
.nav-section .section-title {
    padding: 15px 20px 8px;
    font-size: 0.8rem;
    font-weight: 600;
    color: rgba(255,255,255,0.6);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    margin-bottom: 5px;
}

/* روابط التنقل */
.nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
    position: relative;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
    border-right-color: #3498db;
}

.nav-link.active {
    background-color: rgba(52, 152, 219, 0.2);
    color: white;
    border-right-color: #3498db;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: #3498db;
}

.nav-icon {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.nav-text {
    flex: 1;
    font-size: 0.9rem;
    font-weight: 500;
}

.nav-badge {
    background-color: #e74c3c;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
}

/* ذيل الشريط الجانبي */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.2);
    padding: 15px;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.quick-stats h4 {
    margin: 0 0 10px;
    font-size: 0.9rem;
    color: rgba(255,255,255,0.8);
}

.quick-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    font-size: 0.8rem;
}

.stat-label {
    color: rgba(255,255,255,0.7);
}

.stat-value {
    color: #3498db;
    font-weight: 600;
}

.sidebar-actions {
    display: flex;
    gap: 8px;
    margin-top: 15px;
}

.sidebar-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 8px;
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.sidebar-btn:hover {
    background: rgba(255,255,255,0.2);
}

.btn-icon {
    font-size: 0.9rem;
}

.btn-text {
    font-size: 0.75rem;
}

/* زر طي/فتح الشريط الجانبي */
.sidebar-toggle {
    position: absolute;
    top: 50%;
    left: -15px;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    background: #3498db;
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 1001;
}

.sidebar-toggle:hover {
    background: #2980b9;
    transform: translateY(-50%) scale(1.1);
}

.toggle-icon {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.admin-sidebar.collapsed .toggle-icon {
    transform: rotate(180deg);
}

/* تخصيص شريط التمرير */
.admin-sidebar::-webkit-scrollbar {
    width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
}

.admin-sidebar::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 1024px) {
    .admin-sidebar {
        position: fixed;
        left: -280px;
        z-index: 1000;
        height: 100vh;
    }
    
    .admin-sidebar.show {
        left: 0;
    }
    
    .admin-sidebar.collapsed {
        left: -70px;
    }
    
    .admin-sidebar.collapsed.show {
        left: 0;
        width: 70px;
    }
}

@media (max-width: 768px) {
    .admin-sidebar {
        width: 250px;
        left: -250px;
    }
    
    .admin-sidebar.collapsed {
        width: 60px;
        left: -60px;
    }
    
    .sidebar-footer {
        padding: 10px;
    }
    
    .sidebar-actions {
        flex-direction: column;
        gap: 5px;
    }
    
    .sidebar-btn {
        padding: 6px;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .admin-sidebar {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    }
    
    .sidebar-footer {
        background: rgba(0,0,0,0.4);
    }
}
</style>

<script>
// وظائف JavaScript للشريط الجانبي
function toggleSidebar() {
    const sidebar = document.getElementById('adminSidebar');
    sidebar.classList.toggle('collapsed');
    
    // حفظ حالة الشريط الجانبي
    const isCollapsed = sidebar.classList.contains('collapsed');
    localStorage.setItem('adminSidebarCollapsed', isCollapsed);
}

function clearCache() {
    if (confirm('هل أنت متأكد من مسح التخزين المؤقت؟')) {
        // إرسال طلب مسح الكاش
        fetch('ajax/clear-cache.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم مسح التخزين المؤقت بنجاح');
            } else {
                alert('حدث خطأ أثناء مسح التخزين المؤقت');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء مسح التخزين المؤقت');
        });
    }
}

function restartServer() {
    if (confirm('هل أنت متأكد من إعادة تشغيل الخادم؟ سيؤدي هذا إلى قطع الاتصال مؤقتاً.')) {
        // إرسال طلب إعادة التشغيل
        fetch('ajax/restart-server.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال طلب إعادة التشغيل');
            } else {
                alert('حدث خطأ أثناء إعادة التشغيل');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إعادة التشغيل');
        });
    }
}

// تحديث الإحصائيات السريعة
function updateQuickStats() {
    fetch('ajax/quick-stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('activeUsers').textContent = data.activeUsers || '-';
                document.getElementById('todayAttacks').textContent = data.todayAttacks || '-';
                document.getElementById('serverLoad').textContent = data.serverLoad || '-';
            }
        })
        .catch(error => {
            console.error('Error updating quick stats:', error);
        });
}

// استعادة حالة الشريط الجانبي
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('adminSidebar');
    const isCollapsed = localStorage.getItem('adminSidebarCollapsed') === 'true';
    
    if (isCollapsed) {
        sidebar.classList.add('collapsed');
    }
    
    // تحديث الإحصائيات السريعة كل 30 ثانية
    updateQuickStats();
    setInterval(updateQuickStats, 30000);
});

// إغلاق الشريط الجانبي في الشاشات الصغيرة عند النقر خارجه
document.addEventListener('click', function(event) {
    const sidebar = document.getElementById('adminSidebar');
    const toggleBtn = document.querySelector('.sidebar-toggle');
    
    if (window.innerWidth <= 1024) {
        if (!sidebar.contains(event.target) && !toggleBtn.contains(event.target)) {
            sidebar.classList.remove('show');
        }
    }
});
</script>
