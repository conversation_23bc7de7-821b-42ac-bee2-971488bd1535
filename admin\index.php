<?php
/**
 * لوحة تحكم الإدارة الرئيسية
 * Main Admin Dashboard
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
requireAdmin();

$db = getDB();

// إحصائيات عامة
$stats = [
    'total_users' => $db->selectOne("SELECT COUNT(*) as count FROM users")['count'],
    'active_users' => $db->selectOne("SELECT COUNT(*) as count FROM users WHERE last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)")['count'],
    'total_villages' => $db->selectOne("SELECT COUNT(*) as count FROM villages")['count'],
    'total_attacks' => $db->selectOne("SELECT COUNT(*) as count FROM attacks WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)")['count'],
    'total_trades' => $db->selectOne("SELECT COUNT(*) as count FROM trades WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)")['count'],
    'total_reports' => $db->selectOne("SELECT COUNT(*) as count FROM reports WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)")['count']
];

// المستخدمون الجدد (آخر 7 أيام)
$newUsers = $db->select("
    SELECT username, email, created_at, last_login 
    FROM users 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
    ORDER BY created_at DESC 
    LIMIT 10
");

// أكثر المستخدمين نشاطاً
$activeUsers = $db->select("
    SELECT u.username, u.email, u.last_login, COUNT(v.id) as village_count
    FROM users u
    LEFT JOIN villages v ON u.id = v.user_id
    WHERE u.last_login >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    GROUP BY u.id
    ORDER BY u.last_login DESC
    LIMIT 10
");

// إحصائيات الخادم
$serverStats = [
    'server_load' => sys_getloadavg()[0] ?? 0,
    'memory_usage' => memory_get_usage(true),
    'memory_peak' => memory_get_peak_usage(true),
    'disk_space' => disk_free_space('.'),
    'disk_total' => disk_total_space('.')
];

// الأحداث الأخيرة
$recentEvents = $db->select("
    SELECT 'user_registration' as event_type, username as details, created_at as event_time
    FROM users 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    
    UNION ALL
    
    SELECT 'attack' as event_type, 
           CONCAT('هجوم من قرية ', av.name, ' إلى قرية ', dv.name) as details,
           a.created_at as event_time
    FROM attacks a
    JOIN villages av ON a.attacker_village_id = av.id
    JOIN villages dv ON a.defender_village_id = dv.id
    WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    
    ORDER BY event_time DESC
    LIMIT 20
");
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الإدارة - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body class="admin-page">
    <?php include 'includes/admin-header.php'; ?>
    
    <div class="admin-container">
        <?php include 'includes/admin-sidebar.php'; ?>
        
        <main class="admin-content">
            <div class="admin-header">
                <h1>لوحة تحكم الإدارة</h1>
                <p>مرحباً بك في لوحة تحكم إدارة اللعبة</p>
            </div>
            
            <!-- الإحصائيات العامة -->
            <div class="stats-grid">
                <div class="stat-card users">
                    <div class="stat-icon">👥</div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo formatNumber($stats['total_users']); ?></div>
                        <div class="stat-label">إجمالي المستخدمين</div>
                        <div class="stat-change">+<?php echo formatNumber($stats['active_users']); ?> نشط</div>
                    </div>
                </div>
                
                <div class="stat-card villages">
                    <div class="stat-icon">🏘️</div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo formatNumber($stats['total_villages']); ?></div>
                        <div class="stat-label">إجمالي القرى</div>
                        <div class="stat-change">متوسط <?php echo round($stats['total_villages'] / max(1, $stats['total_users']), 1); ?> قرية/مستخدم</div>
                    </div>
                </div>
                
                <div class="stat-card attacks">
                    <div class="stat-icon">⚔️</div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo formatNumber($stats['total_attacks']); ?></div>
                        <div class="stat-label">هجمات اليوم</div>
                        <div class="stat-change">آخر 24 ساعة</div>
                    </div>
                </div>
                
                <div class="stat-card trades">
                    <div class="stat-icon">🚚</div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo formatNumber($stats['total_trades']); ?></div>
                        <div class="stat-label">تجارة اليوم</div>
                        <div class="stat-change">آخر 24 ساعة</div>
                    </div>
                </div>
                
                <div class="stat-card reports">
                    <div class="stat-icon">📊</div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo formatNumber($stats['total_reports']); ?></div>
                        <div class="stat-label">تقارير اليوم</div>
                        <div class="stat-change">آخر 24 ساعة</div>
                    </div>
                </div>
                
                <div class="stat-card server">
                    <div class="stat-icon">🖥️</div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo number_format($serverStats['server_load'], 2); ?></div>
                        <div class="stat-label">حمولة الخادم</div>
                        <div class="stat-change">
                            <?php echo formatBytes($serverStats['memory_usage']); ?> ذاكرة
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- المحتوى الرئيسي -->
            <div class="admin-main-content">
                <!-- المستخدمون الجدد -->
                <div class="admin-section">
                    <h3>المستخدمون الجدد (آخر 7 أيام)</h3>
                    <div class="table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>اسم المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>آخر دخول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($newUsers)): ?>
                                    <?php foreach ($newUsers as $user): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($user['username']); ?></td>
                                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></td>
                                            <td>
                                                <?php if ($user['last_login']): ?>
                                                    <?php echo date('d/m/Y H:i', strtotime($user['last_login'])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">لم يدخل بعد</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="users.php?action=view&id=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary">عرض</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="text-center">لا توجد مستخدمون جدد</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- المستخدمون النشطون -->
                <div class="admin-section">
                    <h3>المستخدمون النشطون (آخر 24 ساعة)</h3>
                    <div class="table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>اسم المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>عدد القرى</th>
                                    <th>آخر دخول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($activeUsers)): ?>
                                    <?php foreach ($activeUsers as $user): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($user['username']); ?></td>
                                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                                            <td><?php echo $user['village_count']; ?></td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($user['last_login'])); ?></td>
                                            <td>
                                                <a href="users.php?action=view&id=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary">عرض</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="text-center">لا توجد مستخدمون نشطون</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- الأحداث الأخيرة -->
                <div class="admin-section">
                    <h3>الأحداث الأخيرة (آخر 24 ساعة)</h3>
                    <div class="events-list">
                        <?php if (!empty($recentEvents)): ?>
                            <?php foreach ($recentEvents as $event): ?>
                                <div class="event-item <?php echo $event['event_type']; ?>">
                                    <div class="event-icon">
                                        <?php if ($event['event_type'] === 'user_registration'): ?>
                                            👤
                                        <?php elseif ($event['event_type'] === 'attack'): ?>
                                            ⚔️
                                        <?php else: ?>
                                            📝
                                        <?php endif; ?>
                                    </div>
                                    <div class="event-details">
                                        <div class="event-description">
                                            <?php if ($event['event_type'] === 'user_registration'): ?>
                                                تسجيل مستخدم جديد: <?php echo htmlspecialchars($event['details']); ?>
                                            <?php else: ?>
                                                <?php echo htmlspecialchars($event['details']); ?>
                                            <?php endif; ?>
                                        </div>
                                        <div class="event-time">
                                            <?php echo formatTimeAgo($event['event_time']); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="no-events">
                                <p>لا توجد أحداث حديثة</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- معلومات الخادم -->
            <div class="server-info">
                <h3>معلومات الخادم</h3>
                <div class="server-stats">
                    <div class="server-stat">
                        <span class="label">حمولة الخادم:</span>
                        <span class="value"><?php echo number_format($serverStats['server_load'], 2); ?></span>
                    </div>
                    <div class="server-stat">
                        <span class="label">استخدام الذاكرة:</span>
                        <span class="value"><?php echo formatBytes($serverStats['memory_usage']); ?></span>
                    </div>
                    <div class="server-stat">
                        <span class="label">ذروة الذاكرة:</span>
                        <span class="value"><?php echo formatBytes($serverStats['memory_peak']); ?></span>
                    </div>
                    <div class="server-stat">
                        <span class="label">مساحة القرص المتاحة:</span>
                        <span class="value"><?php echo formatBytes($serverStats['disk_space']); ?></span>
                    </div>
                    <div class="server-stat">
                        <span class="label">إجمالي مساحة القرص:</span>
                        <span class="value"><?php echo formatBytes($serverStats['disk_total']); ?></span>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="../js/admin.js"></script>
</body>
</html>

<?php
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

function formatTimeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return 'منذ لحظات';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return "منذ {$minutes} دقيقة";
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return "منذ {$hours} ساعة";
    } else {
        $days = floor($time / 86400);
        return "منذ {$days} يوم";
    }
}

function requireAdmin() {
    if (!isLoggedIn() || !isAdmin()) {
        redirect('../login.php');
    }
}

function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}
?>
