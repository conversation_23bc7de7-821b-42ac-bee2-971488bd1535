# 🌾 اللعبة الزراعية - Agricultural Strategy Game

لعبة استراتيجية زراعية متكاملة مشابهة لترافيان، مطورة بـ PHP و MySQL مع واجهة عربية كاملة.

## 📋 نظرة عامة

هذه لعبة استراتيجية متعددة اللاعبين تركز على بناء وإدارة القرى الزراعية، تطوير الموارد، بناء الجيوش، والتجارة مع اللاعبين الآخرين. اللعبة مصممة لتكون منافسة قوية لألعاب مثل ترافيان.

## ✨ المميزات الرئيسية

### 🏘️ إدارة القرى
- بناء وترقية المباني المختلفة
- إدارة الموارد الأربعة (خشب، طين، حديد، قمح)
- نظام سكان متطور
- خريطة تفاعلية للقرى

### ⚔️ النظام العسكري
- تدريب وحدات عسكرية متنوعة
- نظام هجوم ودفاع متقدم
- تقارير معارك مفصلة
- استراتيجيات قتال متنوعة

### 💰 التجارة والاقتصاد
- سوق لتبادل الموارد
- نظام تجارة مباشر بين اللاعبين
- حساب أسعار ديناميكي
- إحصائيات تجارية شاملة

### 🤝 التحالفات
- إنشاء وإدارة التحالفات
- نظام رتب ومناصب
- تبادل الموارد بين الأعضاء
- خطط عسكرية مشتركة

### 📊 الإحصائيات والتقارير
- إحصائيات شخصية مفصلة
- تقارير المعارك والتجارة
- ترتيب اللاعبين والتحالفات
- رسوم بيانية تفاعلية

## 🛠️ التقنيات المستخدمة

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript
- **Framework**: Custom MVC Pattern
- **Security**: PDO, CSRF Protection, Input Validation
- **UI**: Responsive Design, RTL Support

## 📁 هيكل المشروع

```
agricultural-game/
├── admin/                  # لوحة تحكم الإدارة
│   ├── includes/          # ملفات مشتركة للإدارة
│   ├── index.php         # لوحة الإدارة الرئيسية
│   ├── users.php         # إدارة المستخدمين
│   └── villages.php      # إدارة القرى
├── ajax/                  # ملفات AJAX
│   ├── update-resources.php
│   ├── process-trades.php
│   └── mark-report-read.php
├── config/                # ملفات الإعدادات
│   ├── config.php        # الإعدادات الرئيسية
│   └── database.sql      # هيكل قاعدة البيانات
├── css/                   # ملفات التنسيق
│   ├── style.css         # التنسيق الرئيسي
│   ├── game.css          # تنسيق اللعبة
│   ├── village.css       # تنسيق القرية
│   ├── buildings.css     # تنسيق المباني
│   ├── army.css          # تنسيق الجيش
│   ├── marketplace.css   # تنسيق السوق
│   ├── trade.css         # تنسيق التجارة
│   ├── map.css           # تنسيق الخريطة
│   ├── reports.css       # تنسيق التقارير
│   ├── alliance.css      # تنسيق التحالفات
│   ├── help.css          # تنسيق المساعدة
│   └── admin.css         # تنسيق الإدارة
├── images/                # الصور والأيقونات
│   ├── buildings/        # صور المباني
│   ├── units/            # صور الوحدات
│   ├── resources/        # صور الموارد
│   ├── icons/            # الأيقونات
│   ├── avatars/          # صور المستخدمين
│   ├── backgrounds/      # الخلفيات
│   └── README.md         # دليل الصور
├── includes/              # ملفات مشتركة
│   ├── functions.php     # الدوال المساعدة
│   ├── header.php        # رأس الصفحة
│   ├── footer.php        # تذييل الصفحة
│   └── sidebar.php       # الشريط الجانبي
├── js/                    # ملفات JavaScript
│   ├── game.js           # سكريبت اللعبة الرئيسي
│   └── admin.js          # سكريبت الإدارة
├── uploads/               # ملفات المستخدمين
├── logs/                  # ملفات السجلات
├── cache/                 # ملفات التخزين المؤقت
├── index.php             # الصفحة الرئيسية
├── login.php             # تسجيل الدخول
├── register.php          # التسجيل
├── village.php           # صفحة القرية
├── buildings.php         # صفحة المباني
├── army.php              # صفحة الجيش
├── barracks.php          # صفحة الثكنة
├── marketplace.php       # صفحة السوق
├── trade.php             # صفحة التجارة
├── map.php               # صفحة الخريطة
├── reports.php           # صفحة التقارير
├── profile.php           # الملف الشخصي
├── alliance.php          # صفحة التحالف
├── help.php              # صفحة المساعدة
├── logout.php            # تسجيل الخروج
├── test-integration.php  # اختبار التكامل
└── README.md             # هذا الملف
```

## 🚀 التثبيت والإعداد

### المتطلبات الأساسية

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx Web Server
- مساحة تخزين 500MB على الأقل

### خطوات التثبيت

1. **تحميل الملفات**
   ```bash
   git clone https://github.com/your-repo/agricultural-game.git
   cd agricultural-game
   ```

2. **إعداد قاعدة البيانات**
   - أنشئ قاعدة بيانات جديدة في MySQL
   - استورد ملف `config/database.sql`
   ```sql
   CREATE DATABASE agricultural_game;
   USE agricultural_game;
   SOURCE config/database.sql;
   ```

3. **تحديث إعدادات الاتصال**
   - افتح ملف `config/config.php`
   - حدث بيانات الاتصال بقاعدة البيانات:
   ```php
   private $host = 'localhost';
   private $dbname = 'agricultural_game';
   private $username = 'your_username';
   private $password = 'your_password';
   ```

4. **ضبط الأذونات**
   ```bash
   chmod 755 uploads/
   chmod 755 logs/
   chmod 755 cache/
   ```

5. **اختبار التثبيت**
   - افتح `test-integration.php` في المتصفح
   - تأكد من نجاح جميع الاختبارات

## 🎮 كيفية اللعب

### البداية
1. سجل حساب جديد من صفحة التسجيل
2. اختر اسم قريتك وموقعها على الخريطة
3. ابدأ ببناء المباني الأساسية لإنتاج الموارد

### تطوير القرية
1. **المباني الأساسية**:
   - قاطع الأخشاب (إنتاج الخشب)
   - حفرة الطين (إنتاج الطين)
   - منجم الحديد (إنتاج الحديد)
   - الأراضي الزراعية (إنتاج القمح)

2. **المباني العسكرية**:
   - الثكنة (تدريب المشاة)
   - الإسطبل (تدريب الفرسان)
   - الورشة (بناء آلات الحصار)

3. **المباني التجارية**:
   - السوق (تجارة الموارد)
   - مكتب التجارة (تحسين التجارة)

### الاستراتيجية العسكرية
1. درب جيشك بحكمة
2. استكشف القرى المجاورة
3. خطط لهجماتك بعناية
4. احم قريتك بالدفاعات

### التجارة والاقتصاد
1. راقب أسعار السوق
2. تاجر مع اللاعبين الآخرين
3. استثمر في تطوير التجارة
4. احتفظ بمخزون استراتيجي

## 👥 إدارة اللعبة

### لوحة تحكم الإدارة
- الوصول: `/admin/index.php`
- المميزات:
  - إحصائيات شاملة للخادم
  - إدارة المستخدمين
  - مراقبة القرى
  - إدارة التقارير
  - إعدادات النظام

### صلاحيات المدير
- حظر/إلغاء حظر المستخدمين
- حذف/تعديل القرى
- مراقبة النشاطات المشبوهة
- إدارة التحالفات
- تحديث إعدادات اللعبة

## 🔧 التخصيص والتطوير

### إضافة مباني جديدة
1. أضف نوع المبنى في جدول `building_types`
2. أضف صورة المبنى في `images/buildings/`
3. حدث ملف `buildings.php` لعرض المبنى الجديد

### إضافة وحدات عسكرية
1. أضف نوع الوحدة في جدول `unit_types`
2. أضف صورة الوحدة في `images/units/`
3. حدث ملف `army.php` و `barracks.php`

### تخصيص التصميم
- عدل ملفات CSS في مجلد `css/`
- أضف صور جديدة في مجلد `images/`
- استخدم متغيرات CSS للألوان الأساسية

## 📱 الاستجابة والتوافق

اللعبة مصممة لتعمل على:
- أجهزة الكمبيوتر المكتبية
- الأجهزة اللوحية
- الهواتف الذكية
- جميع المتصفحات الحديثة

## 🔒 الأمان

### الحماية المطبقة
- حماية من هجمات SQL Injection
- حماية CSRF
- تشفير كلمات المرور
- تنظيف المدخلات
- جلسات آمنة

### نصائح الأمان
- استخدم كلمات مرور قوية لقاعدة البيانات
- حدث PHP و MySQL بانتظام
- راقب ملفات السجلات
- استخدم HTTPS في الإنتاج

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من بيانات الاتصال في `config/config.php`
   - تأكد من تشغيل خدمة MySQL

2. **الصور لا تظهر**
   - تحقق من وجود مجلد `images/`
   - تأكد من الأذونات الصحيحة

3. **مشاكل في الأذونات**
   ```bash
   chmod -R 755 uploads/ logs/ cache/
   ```

4. **أخطاء JavaScript**
   - تحقق من وحدة تحكم المتصفح
   - تأكد من تحميل ملفات JS

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- اقرأ ملف المساعدة داخل اللعبة
- تحقق من ملف `test-integration.php`
- راجع ملفات السجلات في `logs/`

### الإبلاغ عن الأخطاء
عند الإبلاغ عن خطأ، يرجى تضمين:
- وصف مفصل للمشكلة
- خطوات إعادة إنتاج الخطأ
- رسائل الخطأ (إن وجدت)
- معلومات المتصفح والنظام

## 📈 خطط التطوير المستقبلية

- [ ] تطبيق جوال (Android/iOS)
- [ ] نظام إشعارات فورية
- [ ] أحداث موسمية
- [ ] نظام إنجازات
- [ ] دعم لغات إضافية
- [ ] API للمطورين
- [ ] نظام دفع متقدم
- [ ] ذكاء اصطناعي للأعداء

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للمزيد من التفاصيل.

## 🙏 شكر وتقدير

- فريق تطوير ترافيان للإلهام
- مجتمع PHP للأدوات والمكتبات
- المساهمين في المشروع

---

**تاريخ آخر تحديث**: 2024-01-20  
**الإصدار**: 1.0.0  
**المطور**: فريق تطوير اللعبة الزراعية

🌾 **استمتع باللعب وبناء إمبراطوريتك الزراعية!** 🌾
