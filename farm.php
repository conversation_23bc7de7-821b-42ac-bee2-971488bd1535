<?php
session_start();
require_once 'config/database.php';
require_once 'classes/Farm.php';

if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$db = new Database();
$farm = new Farm($db->getConnection());
$villageId = $_GET['village_id'] ?? 1;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $farm->plantCrop($villageId, $_POST['crop_type'], $_POST['field_x'], $_POST['field_y']);
}

$crops = $farm->getVillageCrops($villageId);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>المزرعة - المزرعة الذهبية</title>
    <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
    <div class="game-container">
        <?php include 'templates/header.php'; ?>
        
        <div class="farm-container">
            <h2>إدارة المزرعة</h2>
            
            <div class="farm-grid">
                <?php for ($x = 1; $x <= 10; $x++): ?>
                    <?php for ($y = 1; $y <= 10; $y++): ?>
                        <div class="farm-field" data-x="<?php echo $x; ?>" data-y="<?php echo $y; ?>">
                            <?php
                            $fieldCrop = null;
                            foreach ($crops as $crop) {
                                if ($crop['field_x'] == $x && $crop['field_y'] == $y) {
                                    $fieldCrop = $crop;
                                    break;
                                }
                            }
                            ?>
                            
                            <?php if ($fieldCrop): ?>
                                <div class="crop-planted">
                                    <img src="assets/images/crops/<?php echo $fieldCrop['crop_type']; ?>.png" alt="<?php echo $fieldCrop['crop_type']; ?>">
                                    <span><?php echo $fieldCrop['quantity']; ?></span>
                                </div>
                            <?php else: ?>
                                <div class="empty-field" onclick="showPlantDialog(<?php echo $x; ?>, <?php echo $y; ?>)">
                                    <img src="assets/images/empty_field.png" alt="حقل فارغ">
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endfor; ?>
                <?php endfor; ?>
            </div>
        </div>
        
        <!-- نافذة زراعة المحاصيل -->
        <div id="plantDialog" class="modal">
            <div class="modal-content">
                <h3>اختر المحصول للزراعة</h3>
                <form method="POST">
                    <input type="hidden" id="field_x" name="field_x">
                    <input type="hidden" id="field_y" name="field_y">
                    
                    <div class="crop-options">
                        <label>
                            <input type="radio" name="crop_type" value="wheat">
                            <img src="assets/images/crops/wheat.png" alt="قمح">
                            قمح
                        </label>
                        <label>
                            <input type="radio" name="crop_type" value="corn">
                            <img src="assets/images/crops/corn.png" alt="ذرة">
                            ذرة
                        </label>
                    </div>
                    
                    <button type="submit">زراعة</button>
                    <button type="button" onclick="closePlantDialog()">إلغاء</button>
                </form>
            </div>
        </div>
    </div>
    
    <script src="assets/js/farm.js"></script>
</body>
</html>