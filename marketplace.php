<?php
/**
 * صفحة السوق
 * Marketplace Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];
$villageId = $_SESSION['current_village_id'];

// التحقق من القرية
$village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$villageId, $userId]);
if (!$village) {
    redirect('index.php');
}

// التحقق من وجود السوق
$marketplace = $db->selectOne("SELECT * FROM buildings WHERE village_id = ? AND building_type = 'marketplace'", [$villageId]);
if (!$marketplace || $marketplace['level'] < 1) {
    showError('يجب بناء السوق أولاً');
    redirect('buildings.php');
}

// تحديث الموارد
updateVillageResources($villageId);

// الحصول على الموارد
$resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);

// الحصول على التجار المتاحين
$merchantCapacity = getMerchantCapacity($marketplace['level']);
$totalMerchants = getTotalMerchants($villageId);
$availableMerchants = $merchantCapacity - $totalMerchants;

// الحصول على عروض السوق
$marketOffers = $db->select("
    SELECT mo.*, v.name as village_name, u.username 
    FROM market_offers mo
    JOIN villages v ON mo.village_id = v.id
    JOIN users u ON v.user_id = u.id
    WHERE mo.village_id != ? AND mo.is_active = 1 AND mo.expires_at > NOW()
    ORDER BY mo.created_at DESC
    LIMIT 20
", [$villageId]);

// الحصول على عروضي
$myOffers = $db->select("
    SELECT * FROM market_offers 
    WHERE village_id = ? AND is_active = 1 
    ORDER BY created_at DESC
", [$villageId]);

// معالجة إنشاء عرض جديد
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_offer'])) {
    $offerWood = intval($_POST['offer_wood'] ?? 0);
    $offerClay = intval($_POST['offer_clay'] ?? 0);
    $offerIron = intval($_POST['offer_iron'] ?? 0);
    $offerCrop = intval($_POST['offer_crop'] ?? 0);
    
    $requestWood = intval($_POST['request_wood'] ?? 0);
    $requestClay = intval($_POST['request_clay'] ?? 0);
    $requestIron = intval($_POST['request_iron'] ?? 0);
    $requestCrop = intval($_POST['request_crop'] ?? 0);
    
    $csrf = $_POST['csrf_token'] ?? '';
    
    if (!validateCSRF($csrf)) {
        $error = 'رمز الأمان غير صحيح';
    } elseif ($offerWood + $offerClay + $offerIron + $offerCrop <= 0) {
        $error = 'يجب تحديد موارد للعرض';
    } elseif ($requestWood + $requestClay + $requestIron + $requestCrop <= 0) {
        $error = 'يجب تحديد موارد مطلوبة';
    } elseif ($resources['wood'] < $offerWood || 
              $resources['clay'] < $offerClay || 
              $resources['iron'] < $offerIron || 
              $resources['crop'] < $offerCrop) {
        $error = 'الموارد المتاحة غير كافية';
    } else {
        // إنشاء العرض
        $expiresAt = date('Y-m-d H:i:s', time() + (24 * 3600)); // ينتهي خلال 24 ساعة
        
        $db->beginTransaction();
        try {
            // خصم الموارد
            $db->update("UPDATE resources SET wood = wood - ?, clay = clay - ?, iron = iron - ?, crop = crop - ? WHERE village_id = ?", 
                       [$offerWood, $offerClay, $offerIron, $offerCrop, $villageId]);
            
            // إنشاء العرض
            $db->insert("INSERT INTO market_offers (village_id, offer_wood, offer_clay, offer_iron, offer_crop, request_wood, request_clay, request_iron, request_crop, expires_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", 
                       [$villageId, $offerWood, $offerClay, $offerIron, $offerCrop, $requestWood, $requestClay, $requestIron, $requestCrop, $expiresAt]);
            
            $db->commit();
            $success = 'تم إنشاء العرض بنجاح!';
            
            // تحديث البيانات
            $resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);
            $myOffers = $db->select("SELECT * FROM market_offers WHERE village_id = ? AND is_active = 1 ORDER BY created_at DESC", [$villageId]);
            
        } catch (Exception $e) {
            $db->rollback();
            $error = 'حدث خطأ أثناء إنشاء العرض';
        }
    }
}

// معالجة قبول عرض
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['accept_offer'])) {
    $offerId = intval($_POST['offer_id'] ?? 0);
    $csrf = $_POST['csrf_token'] ?? '';
    
    if (!validateCSRF($csrf)) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        $offer = $db->selectOne("SELECT * FROM market_offers WHERE id = ? AND is_active = 1 AND expires_at > NOW()", [$offerId]);
        
        if (!$offer) {
            $error = 'العرض غير متاح';
        } elseif ($offer['village_id'] == $villageId) {
            $error = 'لا يمكنك قبول عرضك الخاص';
        } elseif ($resources['wood'] < $offer['request_wood'] || 
                  $resources['clay'] < $offer['request_clay'] || 
                  $resources['iron'] < $offer['request_iron'] || 
                  $resources['crop'] < $offer['request_crop']) {
            $error = 'الموارد المطلوبة غير متاحة';
        } else {
            // تنفيذ التجارة
            $db->beginTransaction();
            try {
                // خصم الموارد من المشتري
                $db->update("UPDATE resources SET wood = wood - ?, clay = clay - ?, iron = iron - ?, crop = crop - ? WHERE village_id = ?", 
                           [$offer['request_wood'], $offer['request_clay'], $offer['request_iron'], $offer['request_crop'], $villageId]);
                
                // إضافة الموارد للمشتري
                $db->update("UPDATE resources SET wood = wood + ?, clay = clay + ?, iron = iron + ?, crop = crop + ? WHERE village_id = ?", 
                           [$offer['offer_wood'], $offer['offer_clay'], $offer['offer_iron'], $offer['offer_crop'], $villageId]);
                
                // إضافة الموارد للبائع
                $db->update("UPDATE resources SET wood = wood + ?, clay = clay + ?, iron = iron + ?, crop = crop + ? WHERE village_id = ?", 
                           [$offer['request_wood'], $offer['request_clay'], $offer['request_iron'], $offer['request_crop'], $offer['village_id']]);
                
                // إلغاء العرض
                $db->update("UPDATE market_offers SET is_active = 0 WHERE id = ?", [$offerId]);
                
                // إنشاء تقرير تجارة
                createTradeReport($villageId, $offer['village_id'], $offer, 'success');
                
                $db->commit();
                $success = 'تمت التجارة بنجاح!';
                
                // تحديث البيانات
                $resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);
                $marketOffers = $db->select("SELECT mo.*, v.name as village_name, u.username FROM market_offers mo JOIN villages v ON mo.village_id = v.id JOIN users u ON v.user_id = u.id WHERE mo.village_id != ? AND mo.is_active = 1 AND mo.expires_at > NOW() ORDER BY mo.created_at DESC LIMIT 20", [$villageId]);
                
            } catch (Exception $e) {
                $db->rollback();
                $error = 'حدث خطأ أثناء التجارة';
            }
        }
    }
}

$csrfToken = generateCSRF();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>السوق - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/marketplace.css">
</head>
<body class="game-page">
    <?php include 'includes/header.php'; ?>
    
    <div class="main-content">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="content">
            <div class="page-header">
                <h1>السوق</h1>
                <p>تجارة الموارد مع اللاعبين الآخرين - المستوى <?php echo $marketplace['level']; ?></p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <!-- معلومات السوق -->
            <div class="marketplace-info">
                <div class="info-grid">
                    <div class="info-card">
                        <h3>🏪 معلومات السوق</h3>
                        <div class="info-item">
                            <span class="label">مستوى السوق:</span>
                            <span class="value"><?php echo $marketplace['level']; ?></span>
                        </div>
                        <div class="info-item">
                            <span class="label">سعة التجار:</span>
                            <span class="value"><?php echo $merchantCapacity; ?></span>
                        </div>
                        <div class="info-item">
                            <span class="label">التجار المتاحون:</span>
                            <span class="value <?php echo $availableMerchants > 0 ? 'available' : 'unavailable'; ?>">
                                <?php echo $availableMerchants; ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <h3>💰 مواردي</h3>
                        <div class="resources-display">
                            <div class="resource-item">
                                <img src="images/resources/wood.png" alt="خشب">
                                <span><?php echo formatNumber($resources['wood']); ?></span>
                            </div>
                            <div class="resource-item">
                                <img src="images/resources/clay.png" alt="طين">
                                <span><?php echo formatNumber($resources['clay']); ?></span>
                            </div>
                            <div class="resource-item">
                                <img src="images/resources/iron.png" alt="حديد">
                                <span><?php echo formatNumber($resources['iron']); ?></span>
                            </div>
                            <div class="resource-item">
                                <img src="images/resources/crop.png" alt="قمح">
                                <span><?php echo formatNumber($resources['crop']); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إنشاء عرض جديد -->
            <div class="create-offer">
                <h3>إنشاء عرض جديد</h3>
                <form method="POST" class="offer-form">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    
                    <div class="offer-sections">
                        <div class="offer-section">
                            <h4>أعرض:</h4>
                            <div class="resources-input">
                                <div class="resource-input">
                                    <img src="images/resources/wood.png" alt="خشب">
                                    <input type="number" name="offer_wood" min="0" max="<?php echo $resources['wood']; ?>" value="0">
                                </div>
                                <div class="resource-input">
                                    <img src="images/resources/clay.png" alt="طين">
                                    <input type="number" name="offer_clay" min="0" max="<?php echo $resources['clay']; ?>" value="0">
                                </div>
                                <div class="resource-input">
                                    <img src="images/resources/iron.png" alt="حديد">
                                    <input type="number" name="offer_iron" min="0" max="<?php echo $resources['iron']; ?>" value="0">
                                </div>
                                <div class="resource-input">
                                    <img src="images/resources/crop.png" alt="قمح">
                                    <input type="number" name="offer_crop" min="0" max="<?php echo $resources['crop']; ?>" value="0">
                                </div>
                            </div>
                        </div>
                        
                        <div class="exchange-arrow">⇄</div>
                        
                        <div class="request-section">
                            <h4>مقابل:</h4>
                            <div class="resources-input">
                                <div class="resource-input">
                                    <img src="images/resources/wood.png" alt="خشب">
                                    <input type="number" name="request_wood" min="0" value="0">
                                </div>
                                <div class="resource-input">
                                    <img src="images/resources/clay.png" alt="طين">
                                    <input type="number" name="request_clay" min="0" value="0">
                                </div>
                                <div class="resource-input">
                                    <img src="images/resources/iron.png" alt="حديد">
                                    <input type="number" name="request_iron" min="0" value="0">
                                </div>
                                <div class="resource-input">
                                    <img src="images/resources/crop.png" alt="قمح">
                                    <input type="number" name="request_crop" min="0" value="0">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="offer-actions">
                        <button type="submit" name="create_offer" class="btn btn-primary">إنشاء العرض</button>
                        <button type="button" onclick="clearForm()" class="btn btn-secondary">مسح</button>
                    </div>
                </form>
            </div>
            
            <!-- عروض السوق -->
            <div class="market-offers">
                <h3>عروض السوق</h3>
                
                <?php if (!empty($marketOffers)): ?>
                    <div class="offers-grid">
                        <?php foreach ($marketOffers as $offer): ?>
                            <div class="offer-card">
                                <div class="offer-header">
                                    <div class="trader-info">
                                        <strong><?php echo htmlspecialchars($offer['username']); ?></strong>
                                        <span><?php echo htmlspecialchars($offer['village_name']); ?></span>
                                    </div>
                                    <div class="offer-time">
                                        <?php echo formatTimeAgo($offer['created_at']); ?>
                                    </div>
                                </div>
                                
                                <div class="offer-content">
                                    <div class="offer-resources">
                                        <h5>يعرض:</h5>
                                        <div class="resources-list">
                                            <?php if ($offer['offer_wood'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/wood.png" alt="خشب">
                                                    <?php echo formatNumber($offer['offer_wood']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($offer['offer_clay'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/clay.png" alt="طين">
                                                    <?php echo formatNumber($offer['offer_clay']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($offer['offer_iron'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/iron.png" alt="حديد">
                                                    <?php echo formatNumber($offer['offer_iron']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($offer['offer_crop'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/crop.png" alt="قمح">
                                                    <?php echo formatNumber($offer['offer_crop']); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="exchange-symbol">⇄</div>
                                    
                                    <div class="request-resources">
                                        <h5>يطلب:</h5>
                                        <div class="resources-list">
                                            <?php if ($offer['request_wood'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/wood.png" alt="خشب">
                                                    <?php echo formatNumber($offer['request_wood']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($offer['request_clay'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/clay.png" alt="طين">
                                                    <?php echo formatNumber($offer['request_clay']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($offer['request_iron'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/iron.png" alt="حديد">
                                                    <?php echo formatNumber($offer['request_iron']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($offer['request_crop'] > 0): ?>
                                                <span class="resource">
                                                    <img src="images/resources/crop.png" alt="قمح">
                                                    <?php echo formatNumber($offer['request_crop']); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="offer-actions">
                                    <?php
                                    $canAccept = $resources['wood'] >= $offer['request_wood'] && 
                                                $resources['clay'] >= $offer['request_clay'] && 
                                                $resources['iron'] >= $offer['request_iron'] && 
                                                $resources['crop'] >= $offer['request_crop'];
                                    ?>
                                    
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                        <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                                        <button type="submit" name="accept_offer" 
                                                class="btn btn-success" 
                                                <?php echo $canAccept ? '' : 'disabled'; ?>>
                                            قبول العرض
                                        </button>
                                    </form>
                                    
                                    <?php if (!$canAccept): ?>
                                        <small class="insufficient-resources">موارد غير كافية</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="no-offers">
                        <p>لا توجد عروض متاحة حالياً</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- عروضي -->
            <?php if (!empty($myOffers)): ?>
                <div class="my-offers">
                    <h3>عروضي</h3>
                    <div class="offers-list">
                        <?php foreach ($myOffers as $offer): ?>
                            <div class="my-offer-item">
                                <div class="offer-summary">
                                    <div class="offer-details">
                                        <span class="offering">
                                            أعرض: 
                                            <?php if ($offer['offer_wood'] > 0) echo formatNumber($offer['offer_wood']) . ' خشب '; ?>
                                            <?php if ($offer['offer_clay'] > 0) echo formatNumber($offer['offer_clay']) . ' طين '; ?>
                                            <?php if ($offer['offer_iron'] > 0) echo formatNumber($offer['offer_iron']) . ' حديد '; ?>
                                            <?php if ($offer['offer_crop'] > 0) echo formatNumber($offer['offer_crop']) . ' قمح '; ?>
                                        </span>
                                        <span class="requesting">
                                            مقابل: 
                                            <?php if ($offer['request_wood'] > 0) echo formatNumber($offer['request_wood']) . ' خشب '; ?>
                                            <?php if ($offer['request_clay'] > 0) echo formatNumber($offer['request_clay']) . ' طين '; ?>
                                            <?php if ($offer['request_iron'] > 0) echo formatNumber($offer['request_iron']) . ' حديد '; ?>
                                            <?php if ($offer['request_crop'] > 0) echo formatNumber($offer['request_crop']) . ' قمح '; ?>
                                        </span>
                                    </div>
                                    <div class="offer-status">
                                        <span class="expires">ينتهي: <?php echo date('d/m/Y H:i', strtotime($offer['expires_at'])); ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </main>
    </div>
    
    <script>
        function clearForm() {
            document.querySelectorAll('.offer-form input[type="number"]').forEach(input => {
                input.value = 0;
            });
        }
        
        // تحديث الموارد كل دقيقة
        setInterval(updateResources, 60000);
        
        function updateResources() {
            fetch('ajax/update-resources.php?village_id=<?php echo $villageId; ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateResourceDisplay(data.resources);
                    }
                })
                .catch(error => console.error('Error updating resources:', error));
        }
        
        function updateResourceDisplay(resources) {
            const resourceTypes = ['wood', 'clay', 'iron', 'crop'];
            resourceTypes.forEach(type => {
                const elements = document.querySelectorAll(`.resource-item img[alt="${getResourceAlt(type)}"] + span`);
                elements.forEach(element => {
                    element.textContent = formatNumber(resources[type]);
                });
            });
        }
        
        function getResourceAlt(type) {
            const alts = {
                'wood': 'خشب',
                'clay': 'طين', 
                'iron': 'حديد',
                'crop': 'قمح'
            };
            return alts[type] || type;
        }
        
        function formatNumber(num) {
            return new Intl.NumberFormat('ar-SA').format(num);
        }
    </script>
</body>
</html>

<?php
function getMerchantCapacity($marketplaceLevel) {
    return $marketplaceLevel * 2; // 2 تاجر لكل مستوى
}

function getTotalMerchants($villageId) {
    // حساب عدد التجار المستخدمين حالياً
    // هذا مثال مبسط - في التطبيق الحقيقي ستحتاج لحساب التجار في الطريق
    return 0;
}

function createTradeReport($buyerVillageId, $sellerVillageId, $offer, $result) {
    $db = getDB();
    
    $reportData = [
        'resources_sent' => [
            'wood' => $offer['request_wood'],
            'clay' => $offer['request_clay'],
            'iron' => $offer['request_iron'],
            'crop' => $offer['request_crop']
        ],
        'resources_received' => [
            'wood' => $offer['offer_wood'],
            'clay' => $offer['offer_clay'],
            'iron' => $offer['offer_iron'],
            'crop' => $offer['offer_crop']
        ]
    ];
    
    // إنشاء تقرير للمشتري
    $db->insert("INSERT INTO reports (attacker_village_id, defender_village_id, report_type, result, report_data) VALUES (?, ?, ?, ?, ?)", 
               [$buyerVillageId, $sellerVillageId, 'trade', $result, json_encode($reportData)]);
    
    // إنشاء تقرير للبائع
    $db->insert("INSERT INTO reports (attacker_village_id, defender_village_id, report_type, result, report_data) VALUES (?, ?, ?, ?, ?)", 
               [$sellerVillageId, $buyerVillageId, 'trade', $result, json_encode($reportData)]);
}

function formatTimeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return 'منذ لحظات';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return "منذ {$minutes} دقيقة";
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return "منذ {$hours} ساعة";
    } else {
        $days = floor($time / 86400);
        return "منذ {$days} يوم";
    }
}
?>
