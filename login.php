<?php
/**
 * صفحة تسجيل الدخول
 * Login Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// إعادة توجيه المستخدمين المسجلين
if (isLoggedIn()) {
    redirect('index.php');
}

$error = '';
$loginAttempts = $_SESSION['login_attempts'] ?? 0;
$lastAttempt = $_SESSION['last_login_attempt'] ?? 0;

// التحقق من محاولات تسجيل الدخول
if ($loginAttempts >= MAX_LOGIN_ATTEMPTS && (time() - $lastAttempt) < LOGIN_TIMEOUT) {
    $remainingTime = LOGIN_TIMEOUT - (time() - $lastAttempt);
    $error = "تم تجاوز عدد المحاولات المسموحة. حاول مرة أخرى بعد " . formatTime($remainingTime);
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && empty($error)) {
    $username = cleanInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    $csrf = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!validateCSRF($csrf)) {
        $error = 'رمز الأمان غير صحيح';
    }
    // التحقق من المدخلات
    elseif (empty($username) || empty($password)) {
        $error = 'اسم المستخدم وكلمة المرور مطلوبان';
    }
    else {
        $db = getDB();
        
        // البحث عن المستخدم
        $user = $db->selectOne("SELECT * FROM users WHERE username = ? OR email = ?", [$username, $username]);
        
        if ($user && verifyPassword($password, $user['password'])) {
            // تسجيل دخول ناجح
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['is_admin'] = $user['is_admin'];
            $_SESSION['login_time'] = time();
            
            // إعادة تعيين محاولات تسجيل الدخول
            unset($_SESSION['login_attempts']);
            unset($_SESSION['last_login_attempt']);
            
            // تحديث آخر تسجيل دخول
            $db->update("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);
            
            // تذكر المستخدم إذا طلب ذلك
            if ($remember) {
                $token = generateToken();
                setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true); // 30 يوم
                // يمكن حفظ الرمز في قاعدة البيانات للتحقق لاحقاً
            }
            
            // إعادة التوجيه
            $redirectTo = $_GET['redirect'] ?? 'index.php';
            redirect($redirectTo);
        } else {
            // تسجيل دخول فاشل
            $_SESSION['login_attempts'] = $loginAttempts + 1;
            $_SESSION['last_login_attempt'] = time();
            
            $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            
            // تسجيل محاولة تسجيل دخول فاشلة
            error_log("Failed login attempt for: $username from IP: " . $_SERVER['REMOTE_ADDR']);
        }
    }
}

$csrfToken = generateCSRF();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-header">
            <h1><?php echo SITE_NAME; ?></h1>
            <p>ادخل إلى عالم الزراعة والاستراتيجية</p>
        </div>
        
        <div class="auth-form">
            <h2>تسجيل الدخول</h2>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if (isset($_GET['registered'])): ?>
                <div class="alert alert-success">تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول</div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                
                <div class="form-group">
                    <label for="username">اسم المستخدم أو البريد الإلكتروني</label>
                    <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" name="remember" value="1">
                        تذكرني لمدة 30 يوم
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary" <?php echo ($loginAttempts >= MAX_LOGIN_ATTEMPTS && (time() - $lastAttempt) < LOGIN_TIMEOUT) ? 'disabled' : ''; ?>>
                    تسجيل الدخول
                </button>
            </form>
            
            <div class="auth-links">
                <p><a href="forgot-password.php">نسيت كلمة المرور؟</a></p>
                <p>ليس لديك حساب؟ <a href="register.php">إنشاء حساب جديد</a></p>
            </div>
        </div>
        
        <div class="game-features">
            <h3>مميزات اللعبة</h3>
            <div class="features-grid">
                <div class="feature">
                    <img src="images/icons/village.png" alt="القرى" class="feature-icon">
                    <h4>بناء القرى</h4>
                    <p>ابن وطور قراك لتصبح إمبراطورية قوية</p>
                </div>
                <div class="feature">
                    <img src="images/icons/army.png" alt="الجيوش" class="feature-icon">
                    <h4>تدريب الجيوش</h4>
                    <p>درب جيوش قوية للدفاع والهجوم</p>
                </div>
                <div class="feature">
                    <img src="images/icons/trade.png" alt="التجارة" class="feature-icon">
                    <h4>التجارة</h4>
                    <p>تاجر مع اللاعبين الآخرين وطور اقتصادك</p>
                </div>
                <div class="feature">
                    <img src="images/icons/alliance.png" alt="التحالفات" class="feature-icon">
                    <h4>التحالفات</h4>
                    <p>انضم إلى تحالف قوي وحارب مع الأصدقاء</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // التحقق من حالة Caps Lock
        document.getElementById('password').addEventListener('keyup', function(e) {
            const capsLockWarning = document.getElementById('caps-warning') || document.createElement('div');
            capsLockWarning.id = 'caps-warning';
            capsLockWarning.className = 'caps-warning';
            
            if (e.getModifierState && e.getModifierState('CapsLock')) {
                capsLockWarning.textContent = 'تحذير: Caps Lock مفعل';
                if (!document.getElementById('caps-warning')) {
                    this.parentNode.appendChild(capsLockWarning);
                }
            } else {
                if (document.getElementById('caps-warning')) {
                    capsLockWarning.remove();
                }
            }
        });
        
        // تركيز تلقائي على حقل اسم المستخدم
        document.getElementById('username').focus();
        
        // إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleBtn = document.getElementById('toggle-password');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleBtn.textContent = 'إخفاء';
            } else {
                passwordField.type = 'password';
                toggleBtn.textContent = 'إظهار';
            }
        }
        
        // إضافة زر إظهار/إخفاء كلمة المرور
        const passwordGroup = document.querySelector('input[name="password"]').parentNode;
        const toggleButton = document.createElement('button');
        toggleButton.type = 'button';
        toggleButton.id = 'toggle-password';
        toggleButton.className = 'toggle-password';
        toggleButton.textContent = 'إظهار';
        toggleButton.onclick = togglePassword;
        passwordGroup.appendChild(toggleButton);
    </script>
</body>
</html>
