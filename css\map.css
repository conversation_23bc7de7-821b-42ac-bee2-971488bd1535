/* ملف CSS للخريطة */
/* Map CSS */

/* أدوات البحث */
.search-tools {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 2px solid #e9ecef;
}

.search-form {
    margin-bottom: 15px;
}

.search-inputs {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.coordinate-input,
.radius-input {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.coordinate-input label,
.radius-input label {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.coordinate-input input,
.radius-input select {
    padding: 8px 12px;
    border: 2px solid #ced4da;
    border-radius: 6px;
    font-size: 1rem;
    width: 120px;
}

.coordinate-input input:focus,
.radius-input select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.quick-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.quick-actions .btn {
    padding: 8px 15px;
    font-size: 0.9rem;
}

/* معلومات المنطقة */
.area-info {
    background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 2px solid #007bff;
}

.area-info h3 {
    color: #0056b3;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.area-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.area-stats .stat {
    color: #495057;
    font-size: 0.9rem;
}

.area-stats .stat strong {
    color: #0056b3;
}

/* حاوية الخريطة */
.map-container {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    border: 2px solid #dee2e6;
    overflow-x: auto;
}

.map-grid {
    display: grid;
    gap: 2px;
    min-width: 800px;
    margin: 0 auto;
}

/* خلايا الخريطة */
.map-cell {
    aspect-ratio: 1;
    min-height: 80px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 0.7rem;
    text-align: center;
    overflow: hidden;
}

.map-cell:hover {
    transform: scale(1.05);
    z-index: 10;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* أنواع الخلايا */
.map-cell.empty {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #28a745;
}

.map-cell.current-village {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffc107;
    border-width: 3px;
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

.map-cell.player-village {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-color: #17a2b8;
    border-width: 2px;
}

.map-cell.other-village {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #dc3545;
}

/* الإحداثيات */
.coordinates {
    position: absolute;
    top: 2px;
    left: 2px;
    font-size: 0.6rem;
    color: #6c757d;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 1px 3px;
    border-radius: 2px;
}

/* معلومات القرية */
.village-info {
    display: flex;
    flex-direction: column;
    gap: 1px;
    text-align: center;
    padding: 2px;
}

.village-name {
    font-weight: bold;
    color: #495057;
    font-size: 0.7rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.village-owner {
    color: #6c757d;
    font-size: 0.6rem;
}

.village-population {
    color: #007bff;
    font-size: 0.6rem;
    font-weight: 500;
}

.village-tribe {
    font-size: 0.5rem;
    padding: 1px 3px;
    border-radius: 2px;
    color: white;
    font-weight: bold;
}

.village-tribe.tribe-gauls {
    background-color: #28a745;
}

.village-tribe.tribe-romans {
    background-color: #dc3545;
}

.village-tribe.tribe-teutons {
    background-color: #007bff;
}

/* علامات القرى */
.village-marker {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 1rem;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.village-marker.current {
    background-color: #ffc107;
    animation: pulse 2s infinite;
}

.village-marker.player {
    background-color: #17a2b8;
}

.village-marker.other {
    background-color: #dc3545;
}

/* الأرض الفارغة */
.empty-land {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    opacity: 0.7;
}

.empty-icon {
    font-size: 1.2rem;
}

.empty-text {
    font-size: 0.6rem;
    color: #6c757d;
}

/* قائمة القرى */
.villages-list {
    margin-bottom: 30px;
}

.villages-list h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.villages-table-container {
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.villages-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    min-width: 800px;
}

.villages-table th,
.villages-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.villages-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 5;
}

.villages-table tbody tr {
    transition: background-color 0.3s ease;
}

.villages-table tbody tr:hover {
    background-color: #f8f9fa;
}

.villages-table .player-village-row {
    background-color: #e7f3ff;
}

.villages-table .player-village-row:hover {
    background-color: #cce7ff;
}

/* خلايا الجدول */
.coordinate-btn {
    background: none;
    border: 1px solid #007bff;
    color: #007bff;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.coordinate-btn:hover {
    background-color: #007bff;
    color: white;
}

.current-badge,
.player-badge {
    background-color: #ffc107;
    color: #212529;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: bold;
    margin-right: 5px;
}

.player-badge {
    background-color: #17a2b8;
    color: white;
}

.tribe-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    color: white;
}

.tribe-badge.tribe-gauls {
    background-color: #28a745;
}

.tribe-badge.tribe-romans {
    background-color: #dc3545;
}

.tribe-badge.tribe-teutons {
    background-color: #007bff;
}

.actions-cell {
    white-space: nowrap;
}

.actions-cell .btn {
    margin-left: 5px;
    padding: 4px 8px;
    font-size: 0.8rem;
}

/* قرى اللاعب */
.player-villages {
    margin-bottom: 30px;
}

.player-villages h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.villages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.village-card {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.village-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.village-card.current {
    border-color: #ffc107;
    background-color: #fff3cd;
}

.village-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.village-header h4 {
    color: #495057;
    margin: 0;
    font-size: 1.1rem;
}

.capital-badge {
    background-color: #ffc107;
    color: #212529;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
}

.village-details {
    margin-bottom: 15px;
}

.village-details .detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.village-details .label {
    color: #6c757d;
    font-size: 0.9rem;
}

.village-details .value {
    color: #495057;
    font-weight: 500;
    font-size: 0.9rem;
}

.village-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.village-actions .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* النافذة المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
}

.modal-header .close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-header .close:hover {
    color: #495057;
}

.modal-body {
    padding: 20px;
}

.village-info-content .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.village-info-content .info-row:last-of-type {
    border-bottom: none;
}

.village-info-content .label {
    color: #6c757d;
    font-weight: 500;
}

.village-info-content .value {
    color: #495057;
    font-weight: bold;
}

.action-buttons {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
}

.empty-land-info {
    text-align: center;
}

.empty-land-info p {
    color: #6c757d;
    margin-bottom: 20px;
}

.no-villages {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

/* الرسوم المتحركة */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .search-inputs {
        flex-direction: column;
        align-items: stretch;
    }
    
    .coordinate-input input,
    .radius-input select {
        width: 100%;
    }
    
    .map-grid {
        min-width: 600px;
    }
    
    .map-cell {
        min-height: 60px;
        font-size: 0.6rem;
    }
    
    .village-marker {
        width: 16px;
        height: 16px;
        font-size: 0.8rem;
    }
    
    .villages-table {
        min-width: 600px;
    }
    
    .villages-table th,
    .villages-table td {
        padding: 8px 10px;
        font-size: 0.9rem;
    }
    
    .villages-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .map-grid {
        min-width: 400px;
    }
    
    .map-cell {
        min-height: 40px;
        font-size: 0.5rem;
    }
    
    .coordinates {
        font-size: 0.4rem;
    }
    
    .village-name {
        font-size: 0.5rem;
    }
    
    .village-owner,
    .village-population {
        font-size: 0.4rem;
    }
    
    .village-tribe {
        display: none;
    }
    
    .village-marker {
        width: 12px;
        height: 12px;
        font-size: 0.6rem;
    }
    
    .modal-content {
        width: 95%;
    }
    
    .modal-header,
    .modal-body {
        padding: 15px;
    }
}
