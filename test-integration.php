<?php
/**
 * ملف اختبار تكامل النظام
 * System Integration Test File
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// إعداد متغيرات الاختبار
$testResults = [];
$totalTests = 0;
$passedTests = 0;

// دالة إضافة نتيجة اختبار
function addTestResult($testName, $result, $message = '') {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    if ($result) {
        $passedTests++;
    }
    
    $testResults[] = [
        'name' => $testName,
        'result' => $result,
        'message' => $message
    ];
}

// اختبار الاتصال بقاعدة البيانات
try {
    $db = getDB();
    addTestResult('اتصال قاعدة البيانات', true, 'تم الاتصال بنجاح');
} catch (Exception $e) {
    addTestResult('اتصال قاعدة البيانات', false, 'فشل الاتصال: ' . $e->getMessage());
}

// اختبار وجود الجداول الأساسية
$requiredTables = [
    'users', 'villages', 'village_buildings', 'village_units',
    'attacks', 'trades', 'reports', 'alliances', 'building_types', 'unit_types'
];

foreach ($requiredTables as $table) {
    try {
        $result = $db->selectOne("SHOW TABLES LIKE '{$table}'");
        addTestResult("جدول {$table}", !empty($result), empty($result) ? 'الجدول غير موجود' : 'الجدول موجود');
    } catch (Exception $e) {
        addTestResult("جدول {$table}", false, 'خطأ في الاستعلام: ' . $e->getMessage());
    }
}

// اختبار وجود الملفات الأساسية
$requiredFiles = [
    'index.php', 'login.php', 'register.php', 'village.php', 'buildings.php',
    'army.php', 'barracks.php', 'marketplace.php', 'trade.php', 'map.php',
    'reports.php', 'profile.php', 'alliance.php', 'help.php'
];

foreach ($requiredFiles as $file) {
    $exists = file_exists($file);
    addTestResult("ملف {$file}", $exists, $exists ? 'الملف موجود' : 'الملف مفقود');
}

// اختبار وجود ملفات CSS
$cssFiles = [
    'css/style.css', 'css/game.css', 'css/village.css', 'css/buildings.css',
    'css/army.css', 'css/marketplace.css', 'css/trade.css', 'css/map.css',
    'css/reports.css', 'css/alliance.css', 'css/help.css', 'css/admin.css'
];

foreach ($cssFiles as $file) {
    $exists = file_exists($file);
    addTestResult("ملف {$file}", $exists, $exists ? 'الملف موجود' : 'الملف مفقود');
}

// اختبار وجود ملفات JavaScript
$jsFiles = [
    'js/game.js', 'js/admin.js'
];

foreach ($jsFiles as $file) {
    $exists = file_exists($file);
    addTestResult("ملف {$file}", $exists, $exists ? 'الملف موجود' : 'الملف مفقود');
}

// اختبار وجود مجلدات الصور
$imageFolders = [
    'images', 'images/buildings', 'images/units', 'images/resources',
    'images/icons', 'images/avatars', 'images/backgrounds'
];

foreach ($imageFolders as $folder) {
    $exists = is_dir($folder);
    addTestResult("مجلد {$folder}", $exists, $exists ? 'المجلد موجود' : 'المجلد مفقود');
}

// اختبار وجود ملفات الإدارة
$adminFiles = [
    'admin/index.php', 'admin/users.php', 'admin/villages.php',
    'admin/includes/admin-header.php', 'admin/includes/admin-sidebar.php'
];

foreach ($adminFiles as $file) {
    $exists = file_exists($file);
    addTestResult("ملف {$file}", $exists, $exists ? 'الملف موجود' : 'الملف مفقود');
}

// اختبار إعدادات PHP
$phpTests = [
    'session_start' => function_exists('session_start'),
    'PDO' => class_exists('PDO'),
    'JSON' => function_exists('json_encode'),
    'mbstring' => extension_loaded('mbstring'),
    'mysqli' => extension_loaded('mysqli')
];

foreach ($phpTests as $test => $result) {
    addTestResult("PHP {$test}", $result, $result ? 'متوفر' : 'غير متوفر');
}

// اختبار الأذونات
$writableFolders = [
    'uploads', 'logs', 'cache'
];

foreach ($writableFolders as $folder) {
    if (!is_dir($folder)) {
        @mkdir($folder, 0755, true);
    }
    $writable = is_writable($folder);
    addTestResult("أذونات {$folder}", $writable, $writable ? 'قابل للكتابة' : 'غير قابل للكتابة');
}

// حساب النسبة المئوية للنجاح
$successRate = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 2) : 0;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تكامل النظام - <?php echo SITE_NAME; ?></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #4CAF50;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .success-rate {
            font-size: 3rem;
            color: <?php echo $successRate >= 80 ? '#4CAF50' : ($successRate >= 60 ? '#FF9800' : '#F44336'); ?>;
        }
        
        .tests-container {
            padding: 30px;
        }
        
        .test-category {
            margin-bottom: 30px;
        }
        
        .test-category h3 {
            color: #2c3e50;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid transparent;
        }
        
        .test-item.passed {
            border-left-color: #4CAF50;
            background: #e8f5e8;
        }
        
        .test-item.failed {
            border-left-color: #F44336;
            background: #ffeaea;
        }
        
        .test-icon {
            font-size: 1.5rem;
            margin-left: 15px;
            width: 30px;
            text-align: center;
        }
        
        .test-details {
            flex: 1;
        }
        
        .test-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 3px;
        }
        
        .test-message {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .actions {
            padding: 30px;
            background: #f8f9fa;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: <?php echo $successRate; ?>%;
            transition: width 0.5s ease;
        }
        
        @media (max-width: 768px) {
            .stats {
                grid-template-columns: 1fr;
            }
            
            .test-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار تكامل النظام</h1>
            <p>فحص شامل لجميع مكونات اللعبة الزراعية</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value success-rate"><?php echo $successRate; ?>%</div>
                <div class="stat-label">معدل النجاح</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo $totalTests; ?></div>
                <div class="stat-label">إجمالي الاختبارات</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" style="color: #4CAF50;"><?php echo $passedTests; ?></div>
                <div class="stat-label">اختبارات ناجحة</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" style="color: #F44336;"><?php echo $totalTests - $passedTests; ?></div>
                <div class="stat-label">اختبارات فاشلة</div>
            </div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
        
        <div class="tests-container">
            <?php
            // تجميع النتائج حسب الفئة
            $categories = [
                'قاعدة البيانات' => [],
                'الملفات الأساسية' => [],
                'ملفات CSS' => [],
                'ملفات JavaScript' => [],
                'مجلدات الصور' => [],
                'ملفات الإدارة' => [],
                'إعدادات PHP' => [],
                'الأذونات' => []
            ];
            
            foreach ($testResults as $test) {
                if (strpos($test['name'], 'جدول') !== false || strpos($test['name'], 'قاعدة البيانات') !== false) {
                    $categories['قاعدة البيانات'][] = $test;
                } elseif (strpos($test['name'], 'css/') !== false) {
                    $categories['ملفات CSS'][] = $test;
                } elseif (strpos($test['name'], 'js/') !== false) {
                    $categories['ملفات JavaScript'][] = $test;
                } elseif (strpos($test['name'], 'images') !== false || strpos($test['name'], 'مجلد') !== false) {
                    $categories['مجلدات الصور'][] = $test;
                } elseif (strpos($test['name'], 'admin/') !== false) {
                    $categories['ملفات الإدارة'][] = $test;
                } elseif (strpos($test['name'], 'PHP') !== false) {
                    $categories['إعدادات PHP'][] = $test;
                } elseif (strpos($test['name'], 'أذونات') !== false) {
                    $categories['الأذونات'][] = $test;
                } else {
                    $categories['الملفات الأساسية'][] = $test;
                }
            }
            
            foreach ($categories as $categoryName => $tests) {
                if (empty($tests)) continue;
                ?>
                <div class="test-category">
                    <h3><?php echo $categoryName; ?></h3>
                    <div class="test-grid">
                        <?php foreach ($tests as $test): ?>
                            <div class="test-item <?php echo $test['result'] ? 'passed' : 'failed'; ?>">
                                <div class="test-icon">
                                    <?php echo $test['result'] ? '✅' : '❌'; ?>
                                </div>
                                <div class="test-details">
                                    <div class="test-name"><?php echo htmlspecialchars($test['name']); ?></div>
                                    <div class="test-message"><?php echo htmlspecialchars($test['message']); ?></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
        
        <div class="actions">
            <a href="index.php" class="btn">🏠 الذهاب للعبة</a>
            <a href="admin/index.php" class="btn btn-secondary">⚙️ لوحة الإدارة</a>
            <a href="?refresh=1" class="btn btn-secondary">🔄 إعادة الاختبار</a>
        </div>
    </div>
    
    <script>
        // تأثير تحريك شريط التقدم
        window.addEventListener('load', function() {
            const progressFill = document.querySelector('.progress-fill');
            progressFill.style.width = '0%';
            setTimeout(() => {
                progressFill.style.width = '<?php echo $successRate; ?>%';
            }, 500);
        });
        
        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.test-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(-5px)';
                this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
                this.style.boxShadow = 'none';
            });
        });
    </script>
</body>
</html>
