<?php
/**
 * ملف إعداد قاعدة البيانات
 * Database Setup File
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'agricultural_game';

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء قاعدة البيانات إذا لم تكن موجودة
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء قاعدة البيانات بنجاح<br>";
    
    // الاتصال بقاعدة البيانات المحددة
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // قراءة ملف SQL
    $sqlFile = 'database/database.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف SQL غير موجود: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // تقسيم الاستعلامات
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement)) continue;
        
        try {
            $pdo->exec($statement);
            $successCount++;
        } catch (PDOException $e) {
            $errorCount++;
            echo "❌ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "✅ تم تنفيذ $successCount استعلام بنجاح<br>";
    if ($errorCount > 0) {
        echo "❌ فشل في تنفيذ $errorCount استعلام<br>";
    }
    
    // إدراج بيانات أساسية
    insertBasicData($pdo);
    
    echo "<br>🎉 <strong>تم إعداد قاعدة البيانات بنجاح!</strong><br>";
    echo "<a href='index.php' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px;'>🏠 الذهاب للعبة</a>";
    echo "<a href='register.php' style='background: #2196F3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px;'>📝 تسجيل حساب جديد</a>";
    
} catch (Exception $e) {
    echo "❌ <strong>خطأ في إعداد قاعدة البيانات:</strong> " . $e->getMessage();
}

function insertBasicData($pdo) {
    try {
        // إدراج أنواع المباني
        $buildingTypes = [
            ['main_building', 'المبنى الرئيسي', 'مركز إدارة القرية', 100, 80, 60, 40],
            ['barracks', 'الثكنة', 'تدريب وحدات المشاة', 200, 170, 90, 40],
            ['stable', 'الإسطبل', 'تدريب وحدات الفرسان', 260, 140, 220, 60],
            ['workshop', 'الورشة', 'بناء آلات الحصار', 300, 240, 260, 20],
            ['academy', 'الأكاديمية', 'بحث التقنيات الجديدة', 220, 160, 90, 40],
            ['smithy', 'الحداد', 'تحسين الأسلحة والدروع', 170, 200, 380, 130],
            ['rally_point', 'نقطة التجمع', 'تجميع الجيوش', 110, 160, 90, 70],
            ['marketplace', 'السوق', 'تجارة الموارد', 80, 70, 120, 70],
            ['embassy', 'السفارة', 'إدارة التحالفات', 180, 130, 150, 80],
            ['residence', 'المقر', 'زيادة النفوذ الثقافي', 580, 460, 350, 180],
            ['palace', 'القصر', 'مقر الحاكم', 550, 800, 750, 250],
            ['treasury', 'الخزانة', 'حماية الموارد', 280, 330, 280, 90],
            ['trade_office', 'مكتب التجارة', 'تحسين التجارة', 140, 160, 90, 40],
            ['great_barracks', 'الثكنة الكبرى', 'تدريب سريع للوحدات', 630, 420, 780, 360],
            ['great_stable', 'الإسطبل الكبير', 'تدريب سريع للفرسان', 780, 420, 660, 240],
            ['city_wall', 'سور المدينة', 'دفاع قوي للقرية', 70, 90, 170, 70],
            ['earth_wall', 'السور الترابي', 'دفاع أساسي', 50, 80, 30, 10],
            ['palisade', 'السياج', 'دفاع خفيف', 160, 100, 80, 60],
            ['stonemason', 'البناء', 'تقوية الجدران', 155, 130, 125, 70],
            ['brewery', 'المخمرة', 'تسريع التدريب', 1400, 1330, 1200, 600],
            ['granary', 'المخزن', 'تخزين القمح', 80, 100, 70, 20],
            ['warehouse', 'المستودع', 'تخزين الموارد', 130, 160, 90, 40],
            ['cranny', 'المخبأ', 'إخفاء الموارد', 40, 50, 30, 10],
            ['trapper', 'الفخاخ', 'فخاخ دفاعية', 100, 100, 100, 100],
            ['hero_mansion', 'قصر البطل', 'مقر البطل', 700, 670, 700, 240],
            ['wonder', 'عجائب الدنيا', 'انتصار نهائي', 66700, 69050, 72200, 13200]
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO building_types (type, name, description, wood_cost, clay_cost, iron_cost, crop_cost) VALUES (?, ?, ?, ?, ?, ?, ?)");
        foreach ($buildingTypes as $building) {
            $stmt->execute($building);
        }
        echo "✅ تم إدراج أنواع المباني<br>";
        
        // إدراج أنواع الوحدات
        $unitTypes = [
            ['phalanx', 'الكتيبة', 'وحدة دفاعية قوية', 100, 130, 160, 70, 15, 40, 50, 1, 6],
            ['swordsman', 'المحارب', 'وحدة هجومية متوازنة', 140, 150, 185, 60, 65, 35, 60, 1, 6],
            ['pathfinder', 'الكشاف', 'وحدة استطلاع سريعة', 170, 150, 20, 40, 0, 20, 10, 5, 16],
            ['theutates_thunder', 'رعد ثيوتاتس', 'فرسان هجوميون', 350, 450, 230, 60, 90, 25, 40, 2, 19],
            ['druidrider', 'فارس الكاهن', 'فرسان دفاعيون', 360, 330, 280, 120, 45, 115, 55, 2, 16],
            ['haeduan', 'الهايدوان', 'فرسان نخبة', 500, 620, 675, 300, 140, 60, 165, 3, 13],
            ['ram', 'الكبش', 'آلة حصار الجدران', 1000, 1300, 1200, 400, 50, 30, 105, 3, 4],
            ['trebuchet', 'المنجنيق', 'آلة حصار المباني', 960, 1450, 630, 90, 70, 45, 10, 6, 3],
            ['chieftain', 'الزعيم', 'قائد لاحتلال القرى', 30750, 45400, 31000, 37500, 40, 50, 50, 4, 5],
            ['settler', 'المستوطن', 'بناء قرى جديدة', 5800, 4400, 4600, 5200, 10, 80, 80, 3, 5]
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO unit_types (type, name, description, wood_cost, clay_cost, iron_cost, crop_cost, attack, defense_infantry, defense_cavalry, speed, carry_capacity) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        foreach ($unitTypes as $unit) {
            $stmt->execute($unit);
        }
        echo "✅ تم إدراج أنواع الوحدات<br>";
        
        // إنشاء مستخدم إداري
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, email, password, is_admin, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute(['admin', '<EMAIL>', $adminPassword, 1]);
        echo "✅ تم إنشاء حساب المدير (admin / admin123)<br>";
        
    } catch (Exception $e) {
        echo "❌ خطأ في إدراج البيانات الأساسية: " . $e->getMessage() . "<br>";
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .result {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
            margin: 20px 0;
            font-family: monospace;
            line-height: 1.6;
        }
        a {
            display: inline-block;
            margin: 10px 5px;
            text-decoration: none;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ إعداد قاعدة البيانات</h1>
        <div class="result">
