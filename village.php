<?php
/**
 * صفحة القرية
 * Village Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$db = getDB();
$userId = $_SESSION['user_id'];
$villageId = $_SESSION['current_village_id'];

// التحقق من القرية
$village = $db->selectOne("SELECT * FROM villages WHERE id = ? AND user_id = ?", [$villageId, $userId]);
if (!$village) {
    redirect('index.php');
}

// تحديث الموارد
updateVillageResources($villageId);

// الحصول على الموارد
$resources = $db->selectOne("SELECT * FROM resources WHERE village_id = ?", [$villageId]);

// الحصول على المباني
$buildings = $db->select("SELECT * FROM buildings WHERE village_id = ?", [$villageId]);
$buildingsArray = [];
foreach ($buildings as $building) {
    $buildingsArray[$building['building_type']] = $building;
}

// الحصول على الحقول
$fields = $db->select("SELECT * FROM fields WHERE village_id = ? ORDER BY position", [$villageId]);

// حساب الإنتاج
$production = calculateResourceProduction($villageId);

// حساب سعة التخزين
$storageCapacity = getStorageCapacity($villageId);

// معالجة تغيير اسم القرية
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['change_name'])) {
    $newName = cleanInput($_POST['village_name'] ?? '');
    $csrf = $_POST['csrf_token'] ?? '';
    
    if (!validateCSRF($csrf)) {
        $error = 'رمز الأمان غير صحيح';
    } elseif (empty($newName)) {
        $error = 'اسم القرية مطلوب';
    } elseif (strlen($newName) < 3 || strlen($newName) > 50) {
        $error = 'اسم القرية يجب أن يكون بين 3 و 50 حرف';
    } else {
        $updated = $db->update("UPDATE villages SET name = ? WHERE id = ? AND user_id = ?", 
                              [$newName, $villageId, $userId]);
        
        if ($updated) {
            $success = 'تم تغيير اسم القرية بنجاح';
            $village['name'] = $newName;
        } else {
            $error = 'حدث خطأ أثناء تغيير الاسم';
        }
    }
}

$csrfToken = generateCSRF();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>القرية - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/village.css">
</head>
<body class="game-page">
    <?php include 'includes/header.php'; ?>
    
    <div class="main-content">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="content">
            <div class="page-header">
                <h1><?php echo htmlspecialchars($village['name']); ?></h1>
                <p>الإحداثيات: (<?php echo $village['x_coordinate']; ?>|<?php echo $village['y_coordinate']; ?>)</p>
                <?php if ($village['is_capital']): ?>
                    <span class="capital-badge">العاصمة</span>
                <?php endif; ?>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <!-- معلومات القرية -->
            <div class="village-info">
                <div class="info-grid">
                    <div class="info-card">
                        <h3>معلومات عامة</h3>
                        <div class="info-item">
                            <span class="label">اسم القرية:</span>
                            <span class="value"><?php echo htmlspecialchars($village['name']); ?></span>
                            <button class="btn btn-small" onclick="showNameChangeForm()">تغيير</button>
                        </div>
                        <div class="info-item">
                            <span class="label">الإحداثيات:</span>
                            <span class="value">(<?php echo $village['x_coordinate']; ?>|<?php echo $village['y_coordinate']; ?>)</span>
                        </div>
                        <div class="info-item">
                            <span class="label">عدد السكان:</span>
                            <span class="value"><?php echo formatNumber($village['population']); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="label">الولاء:</span>
                            <span class="value"><?php echo $village['loyalty']; ?>%</span>
                        </div>
                        <div class="info-item">
                            <span class="label">تاريخ الإنشاء:</span>
                            <span class="value"><?php echo date('d/m/Y', strtotime($village['created_at'])); ?></span>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <h3>الموارد والإنتاج</h3>
                        <div class="resources-info">
                            <div class="resource-item">
                                <img src="images/resources/wood.png" alt="خشب">
                                <div class="resource-details">
                                    <span class="amount"><?php echo formatNumber($resources['wood']); ?></span>
                                    <span class="capacity">/ <?php echo formatNumber($storageCapacity['warehouse']); ?></span>
                                    <span class="production">+<?php echo formatNumber($production['wood']); ?>/ساعة</span>
                                </div>
                            </div>
                            <div class="resource-item">
                                <img src="images/resources/clay.png" alt="طين">
                                <div class="resource-details">
                                    <span class="amount"><?php echo formatNumber($resources['clay']); ?></span>
                                    <span class="capacity">/ <?php echo formatNumber($storageCapacity['warehouse']); ?></span>
                                    <span class="production">+<?php echo formatNumber($production['clay']); ?>/ساعة</span>
                                </div>
                            </div>
                            <div class="resource-item">
                                <img src="images/resources/iron.png" alt="حديد">
                                <div class="resource-details">
                                    <span class="amount"><?php echo formatNumber($resources['iron']); ?></span>
                                    <span class="capacity">/ <?php echo formatNumber($storageCapacity['warehouse']); ?></span>
                                    <span class="production">+<?php echo formatNumber($production['iron']); ?>/ساعة</span>
                                </div>
                            </div>
                            <div class="resource-item">
                                <img src="images/resources/crop.png" alt="قمح">
                                <div class="resource-details">
                                    <span class="amount"><?php echo formatNumber($resources['crop']); ?></span>
                                    <span class="capacity">/ <?php echo formatNumber($storageCapacity['granary']); ?></span>
                                    <span class="production">+<?php echo formatNumber($production['crop']); ?>/ساعة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <h3>المباني الرئيسية</h3>
                        <div class="main-buildings">
                            <?php
                            $mainBuildings = ['main_building', 'warehouse', 'granary', 'barracks', 'stable'];
                            foreach ($mainBuildings as $buildingType):
                                $building = $buildingsArray[$buildingType] ?? null;
                                $level = $building ? $building['level'] : 0;
                                $isUpgrading = $building && $building['is_upgrading'];
                            ?>
                                <div class="building-item <?php echo $isUpgrading ? 'upgrading' : ''; ?>">
                                    <img src="images/buildings/<?php echo $buildingType; ?>.png" alt="<?php echo BUILDING_TYPES[$buildingType]; ?>">
                                    <div class="building-details">
                                        <span class="name"><?php echo BUILDING_TYPES[$buildingType]; ?></span>
                                        <span class="level">المستوى <?php echo $level; ?></span>
                                        <?php if ($isUpgrading): ?>
                                            <span class="status">قيد الترقية</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <a href="buildings.php" class="btn btn-primary">إدارة المباني</a>
                    </div>
                </div>
            </div>
            
            <!-- خريطة القرية -->
            <div class="village-map">
                <h3>خريطة القرية</h3>
                <div class="map-container">
                    <!-- مركز القرية -->
                    <div class="village-center">
                        <img src="images/village-center.png" alt="مركز القرية">
                        <div class="center-info">
                            <h4><?php echo htmlspecialchars($village['name']); ?></h4>
                            <p>السكان: <?php echo formatNumber($village['population']); ?></p>
                        </div>
                    </div>
                    
                    <!-- الحقول حول القرية -->
                    <div class="fields-ring">
                        <?php foreach ($fields as $field): ?>
                            <div class="field-slot field-<?php echo $field['position']; ?>" 
                                 data-field-type="<?php echo $field['field_type']; ?>"
                                 data-field-level="<?php echo $field['level']; ?>">
                                <img src="images/fields/<?php echo $field['field_type']; ?>_<?php echo min($field['level'], 10); ?>.png" 
                                     alt="<?php echo getResourceName($field['field_type']); ?>">
                                <span class="field-level"><?php echo $field['level']; ?></span>
                                <?php if ($field['is_upgrading']): ?>
                                    <div class="upgrade-indicator">⚡</div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- المباني -->
                    <div class="buildings-area">
                        <?php foreach ($buildingsArray as $buildingType => $building): ?>
                            <?php if ($building['level'] > 0): ?>
                                <div class="building-slot building-<?php echo $buildingType; ?>" 
                                     data-building-type="<?php echo $buildingType; ?>"
                                     data-building-level="<?php echo $building['level']; ?>">
                                    <img src="images/buildings/<?php echo $buildingType; ?>.png" 
                                         alt="<?php echo BUILDING_TYPES[$buildingType]; ?>">
                                    <span class="building-level"><?php echo $building['level']; ?></span>
                                    <?php if ($building['is_upgrading']): ?>
                                        <div class="upgrade-indicator">⚡</div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <!-- الإجراءات السريعة -->
            <div class="quick-actions">
                <h3>الإجراءات السريعة</h3>
                <div class="actions-grid">
                    <a href="buildings.php" class="action-btn">
                        <img src="images/icons/buildings.png" alt="المباني">
                        <span>إدارة المباني</span>
                    </a>
                    <a href="fields.php" class="action-btn">
                        <img src="images/icons/fields.png" alt="الحقول">
                        <span>ترقية الحقول</span>
                    </a>
                    <a href="army.php" class="action-btn">
                        <img src="images/icons/army.png" alt="الجيش">
                        <span>إدارة الجيش</span>
                    </a>
                    <a href="marketplace.php" class="action-btn">
                        <img src="images/icons/marketplace.png" alt="السوق">
                        <span>السوق</span>
                    </a>
                    <a href="reports.php" class="action-btn">
                        <img src="images/icons/reports.png" alt="التقارير">
                        <span>التقارير</span>
                    </a>
                    <a href="map.php" class="action-btn">
                        <img src="images/icons/map.png" alt="الخريطة">
                        <span>الخريطة</span>
                    </a>
                </div>
            </div>
        </main>
    </div>
    
    <!-- نموذج تغيير اسم القرية -->
    <div id="name-change-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تغيير اسم القرية</h3>
                <button class="close" onclick="hideNameChangeForm()">&times;</button>
            </div>
            <form method="POST" class="modal-body">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                <div class="form-group">
                    <label for="village_name">الاسم الجديد:</label>
                    <input type="text" id="village_name" name="village_name" 
                           value="<?php echo htmlspecialchars($village['name']); ?>" 
                           maxlength="50" required>
                    <small>3-50 حرف</small>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="hideNameChangeForm()">إلغاء</button>
                    <button type="submit" name="change_name" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function showNameChangeForm() {
            document.getElementById('name-change-modal').style.display = 'flex';
            document.getElementById('village_name').focus();
        }
        
        function hideNameChangeForm() {
            document.getElementById('name-change-modal').style.display = 'none';
        }
        
        // إغلاق النموذج عند النقر خارجه
        document.getElementById('name-change-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideNameChangeForm();
            }
        });
        
        // تحديث الموارد كل دقيقة
        setInterval(updateResources, 60000);
        
        function updateResources() {
            fetch('ajax/update-resources.php?village_id=<?php echo $villageId; ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // تحديث عرض الموارد
                        updateResourceDisplay(data.resources);
                    }
                })
                .catch(error => console.error('Error updating resources:', error));
        }
        
        function updateResourceDisplay(resources) {
            const resourceTypes = ['wood', 'clay', 'iron', 'crop'];
            resourceTypes.forEach(type => {
                const amountElement = document.querySelector(`.resource-item img[alt="${getResourceAlt(type)}"] + .resource-details .amount`);
                if (amountElement) {
                    amountElement.textContent = formatNumber(resources[type]);
                }
            });
        }
        
        function getResourceAlt(type) {
            const alts = {
                'wood': 'خشب',
                'clay': 'طين', 
                'iron': 'حديد',
                'crop': 'قمح'
            };
            return alts[type] || type;
        }
        
        function formatNumber(num) {
            return new Intl.NumberFormat('ar-SA').format(num);
        }
    </script>
</body>
</html>

<?php
function getResourceName($type) {
    $names = [
        'wood' => 'خشب',
        'clay' => 'طين',
        'iron' => 'حديد',
        'crop' => 'قمح'
    ];
    return $names[$type] ?? $type;
}
?>
