<?php
/**
 * إعداد قاعدة بيانات SQLite
 * SQLite Database Setup
 */

require_once 'config/config.php';

echo "<h1>🗄️ إعداد قاعدة بيانات SQLite</h1>";

try {
    // إنشاء مجلد قاعدة البيانات
    $dbDir = dirname(DB_PATH);
    if (!is_dir($dbDir)) {
        mkdir($dbDir, 0755, true);
        echo "✅ تم إنشاء مجلد قاعدة البيانات<br>";
    }
    
    // الاتصال بـ SQLite
    $pdo = new PDO("sqlite:" . DB_PATH);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("PRAGMA foreign_keys = ON");
    
    echo "✅ تم إنشاء قاعدة بيانات SQLite بنجاح<br>";
    
    // إنشاء الجداول
    $tables = [
        // جدول المستخدمين
        "CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            tribe_id INTEGER DEFAULT 1,
            is_admin INTEGER DEFAULT 0,
            is_banned INTEGER DEFAULT 0,
            ban_reason TEXT,
            last_login DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        
        // جدول القرى
        "CREATE TABLE IF NOT EXISTS villages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            name VARCHAR(100) NOT NULL,
            x INTEGER NOT NULL,
            y INTEGER NOT NULL,
            population INTEGER DEFAULT 100,
            wood INTEGER DEFAULT 1000,
            clay INTEGER DEFAULT 1000,
            iron INTEGER DEFAULT 1000,
            crop INTEGER DEFAULT 1000,
            wood_production INTEGER DEFAULT 30,
            clay_production INTEGER DEFAULT 30,
            iron_production INTEGER DEFAULT 30,
            crop_production INTEGER DEFAULT 30,
            last_update DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )",
        
        // جدول أنواع المباني
        "CREATE TABLE IF NOT EXISTS building_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type VARCHAR(50) UNIQUE NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            wood_cost INTEGER DEFAULT 0,
            clay_cost INTEGER DEFAULT 0,
            iron_cost INTEGER DEFAULT 0,
            crop_cost INTEGER DEFAULT 0,
            max_level INTEGER DEFAULT 20,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        
        // جدول مباني القرى
        "CREATE TABLE IF NOT EXISTS village_buildings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            village_id INTEGER NOT NULL,
            building_type VARCHAR(50) NOT NULL,
            level INTEGER DEFAULT 1,
            upgrade_finish_time DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (village_id) REFERENCES villages(id) ON DELETE CASCADE
        )",
        
        // جدول أنواع الوحدات
        "CREATE TABLE IF NOT EXISTS unit_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type VARCHAR(50) UNIQUE NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            wood_cost INTEGER DEFAULT 0,
            clay_cost INTEGER DEFAULT 0,
            iron_cost INTEGER DEFAULT 0,
            crop_cost INTEGER DEFAULT 0,
            attack INTEGER DEFAULT 0,
            defense_infantry INTEGER DEFAULT 0,
            defense_cavalry INTEGER DEFAULT 0,
            speed INTEGER DEFAULT 1,
            carry_capacity INTEGER DEFAULT 0,
            training_time INTEGER DEFAULT 300,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        
        // جدول وحدات القرى
        "CREATE TABLE IF NOT EXISTS village_units (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            village_id INTEGER NOT NULL,
            unit_type VARCHAR(50) NOT NULL,
            count INTEGER DEFAULT 0,
            training_queue INTEGER DEFAULT 0,
            training_finish_time DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (village_id) REFERENCES villages(id) ON DELETE CASCADE
        )",
        
        // جدول الهجمات
        "CREATE TABLE IF NOT EXISTS attacks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            attacker_village_id INTEGER NOT NULL,
            defender_village_id INTEGER NOT NULL,
            attack_type VARCHAR(20) DEFAULT 'attack',
            units TEXT,
            launch_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            arrival_time DATETIME NOT NULL,
            status VARCHAR(20) DEFAULT 'traveling',
            result TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (attacker_village_id) REFERENCES villages(id) ON DELETE CASCADE,
            FOREIGN KEY (defender_village_id) REFERENCES villages(id) ON DELETE CASCADE
        )",
        
        // جدول التجارة
        "CREATE TABLE IF NOT EXISTS trades (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sender_village_id INTEGER NOT NULL,
            receiver_village_id INTEGER,
            offer_wood INTEGER DEFAULT 0,
            offer_clay INTEGER DEFAULT 0,
            offer_iron INTEGER DEFAULT 0,
            offer_crop INTEGER DEFAULT 0,
            request_wood INTEGER DEFAULT 0,
            request_clay INTEGER DEFAULT 0,
            request_iron INTEGER DEFAULT 0,
            request_crop INTEGER DEFAULT 0,
            status VARCHAR(20) DEFAULT 'pending',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            completed_at DATETIME,
            FOREIGN KEY (sender_village_id) REFERENCES villages(id) ON DELETE CASCADE,
            FOREIGN KEY (receiver_village_id) REFERENCES villages(id) ON DELETE CASCADE
        )",
        
        // جدول التقارير
        "CREATE TABLE IF NOT EXISTS reports (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            type VARCHAR(50) NOT NULL,
            title VARCHAR(200) NOT NULL,
            content TEXT NOT NULL,
            is_read INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )",
        
        // جدول التحالفات
        "CREATE TABLE IF NOT EXISTS alliances (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) UNIQUE NOT NULL,
            tag VARCHAR(10) UNIQUE NOT NULL,
            description TEXT,
            leader_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (leader_id) REFERENCES users(id) ON DELETE CASCADE
        )",
        
        // جدول أعضاء التحالفات
        "CREATE TABLE IF NOT EXISTS alliance_members (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            alliance_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            rank VARCHAR(50) DEFAULT 'member',
            joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (alliance_id) REFERENCES alliances(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )"
    ];
    
    $successCount = 0;
    foreach ($tables as $sql) {
        try {
            $pdo->exec($sql);
            $successCount++;
        } catch (PDOException $e) {
            echo "❌ خطأ في إنشاء جدول: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "✅ تم إنشاء $successCount جدول بنجاح<br>";
    
    // إدراج البيانات الأساسية
    insertBasicData($pdo);
    
    echo "<br>🎉 <strong>تم إعداد قاعدة البيانات بنجاح!</strong><br>";
    echo "<a href='index.php' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px;'>🏠 الذهاب للعبة</a>";
    echo "<a href='register.php' style='background: #2196F3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px;'>📝 تسجيل حساب جديد</a>";
    echo "<a href='debug.php' style='background: #FF9800; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px;'>🔧 تشخيص النظام</a>";
    
} catch (Exception $e) {
    echo "❌ <strong>خطأ في إعداد قاعدة البيانات:</strong> " . $e->getMessage();
}

function insertBasicData($pdo) {
    try {
        // إدراج أنواع المباني
        $buildingTypes = [
            ['main_building', 'المبنى الرئيسي', 'مركز إدارة القرية', 100, 80, 60, 40, 20],
            ['barracks', 'الثكنة', 'تدريب وحدات المشاة', 200, 170, 90, 40, 20],
            ['stable', 'الإسطبل', 'تدريب وحدات الفرسان', 260, 140, 220, 60, 20],
            ['workshop', 'الورشة', 'بناء آلات الحصار', 300, 240, 260, 20, 20],
            ['academy', 'الأكاديمية', 'بحث التقنيات الجديدة', 220, 160, 90, 40, 20],
            ['smithy', 'الحداد', 'تحسين الأسلحة والدروع', 170, 200, 380, 130, 20],
            ['rally_point', 'نقطة التجمع', 'تجميع الجيوش', 110, 160, 90, 70, 20],
            ['marketplace', 'السوق', 'تجارة الموارد', 80, 70, 120, 70, 20],
            ['embassy', 'السفارة', 'إدارة التحالفات', 180, 130, 150, 80, 20],
            ['residence', 'المقر', 'زيادة النفوذ الثقافي', 580, 460, 350, 180, 20],
            ['granary', 'المخزن', 'تخزين القمح', 80, 100, 70, 20, 20],
            ['warehouse', 'المستودع', 'تخزين الموارد', 130, 160, 90, 40, 20]
        ];
        
        $stmt = $pdo->prepare("INSERT OR IGNORE INTO building_types (type, name, description, wood_cost, clay_cost, iron_cost, crop_cost, max_level) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        foreach ($buildingTypes as $building) {
            $stmt->execute($building);
        }
        echo "✅ تم إدراج أنواع المباني<br>";
        
        // إدراج أنواع الوحدات
        $unitTypes = [
            ['phalanx', 'الكتيبة', 'وحدة دفاعية قوية', 100, 130, 160, 70, 15, 40, 50, 1, 6, 1800],
            ['swordsman', 'المحارب', 'وحدة هجومية متوازنة', 140, 150, 185, 60, 65, 35, 60, 1, 6, 1800],
            ['pathfinder', 'الكشاف', 'وحدة استطلاع سريعة', 170, 150, 20, 40, 0, 20, 10, 5, 16, 1200],
            ['theutates_thunder', 'رعد ثيوتاتس', 'فرسان هجوميون', 350, 450, 230, 60, 90, 25, 40, 2, 19, 2400],
            ['druidrider', 'فارس الكاهن', 'فرسان دفاعيون', 360, 330, 280, 120, 45, 115, 55, 2, 16, 2400],
            ['ram', 'الكبش', 'آلة حصار الجدران', 1000, 1300, 1200, 400, 50, 30, 105, 3, 4, 3600],
            ['trebuchet', 'المنجنيق', 'آلة حصار المباني', 960, 1450, 630, 90, 70, 45, 10, 6, 3, 3600],
            ['settler', 'المستوطن', 'بناء قرى جديدة', 5800, 4400, 4600, 5200, 10, 80, 80, 3, 5, 14400]
        ];
        
        $stmt = $pdo->prepare("INSERT OR IGNORE INTO unit_types (type, name, description, wood_cost, clay_cost, iron_cost, crop_cost, attack, defense_infantry, defense_cavalry, speed, carry_capacity, training_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        foreach ($unitTypes as $unit) {
            $stmt->execute($unit);
        }
        echo "✅ تم إدراج أنواع الوحدات<br>";
        
        // إنشاء مستخدم إداري
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT OR IGNORE INTO users (username, email, password, is_admin) VALUES (?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', $adminPassword, 1]);
        echo "✅ تم إنشاء حساب المدير (admin / admin123)<br>";
        
    } catch (Exception $e) {
        echo "❌ خطأ في إدراج البيانات الأساسية: " . $e->getMessage() . "<br>";
    }
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    min-height: 100vh;
    direction: rtl;
}
.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}
h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
}
a {
    display: inline-block;
    margin: 10px 5px;
    text-decoration: none;
    border-radius: 5px;
}
</style>
