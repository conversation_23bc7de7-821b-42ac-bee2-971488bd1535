<?php
class Army {
    private $conn;
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    public function trainUnits($villageId, $unitType, $quantity, $userId) {
        $costs = $this->getUnitCosts($unitType);
        $totalCosts = [
            'gold' => $costs['gold'] * $quantity,
            'wood' => $costs['wood'] * $quantity,
            'stone' => $costs['stone'] * $quantity,
            'food' => $costs['food'] * $quantity
        ];
        
        if (!$this->checkResources($userId, $totalCosts)) {
            return false;
        }
        
        $this->conn->beginTransaction();
        
        try {
            $this->deductResources($userId, $totalCosts);
            
            // إضافة الوحدات أو تحديث العدد
            $stmt = $this->conn->prepare("SELECT * FROM army WHERE village_id = ? AND unit_type = ?");
            $stmt->execute([$villageId, $unitType]);
            $existingUnit = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($existingUnit) {
                $stmt = $this->conn->prepare("UPDATE army SET quantity = quantity + ? WHERE village_id = ? AND unit_type = ?");
                $stmt->execute([$quantity, $villageId, $unitType]);
            } else {
                $stmt = $this->conn->prepare("INSERT INTO army (village_id, unit_type, quantity) VALUES (?, ?, ?)");
                $stmt->execute([$villageId, $unitType, $quantity]);
            }
            
            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }
    
    private function getUnitCosts($unitType) {
        $costs = [
            'warrior' => ['gold' => 50, 'wood' => 0, 'stone' => 0, 'food' => 25],
            'archer' => ['gold' => 75, 'wood' => 10, 'stone' => 0, 'food' => 35],
            'cavalry' => ['gold' => 150, 'wood' => 25, 'stone' => 0, 'food' => 60],
            'siege' => ['gold' => 500, 'wood' => 200, 'stone' => 100, 'food' => 100]
        ];
        
        return $costs[$unitType] ?? ['gold' => 0, 'wood' => 0, 'stone' => 0, 'food' => 0];
    }
    
    private function checkResources($userId, $costs) {
        $stmt = $this->conn->prepare("SELECT gold, wood, stone, food FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $user['gold'] >= $costs['gold'] && 
               $user['wood'] >= $costs['wood'] && 
               $user['stone'] >= $costs['stone'] &&
               $user['food'] >= $costs['food'];
    }
    
    private function deductResources($userId, $costs) {
        $stmt = $this->conn->prepare("UPDATE users SET gold = gold - ?, wood = wood - ?, stone = stone - ?, food = food - ? WHERE id = ?");
        $stmt->execute([$costs['gold'], $costs['wood'], $costs['stone'], $costs['food'], $userId]);
    }
    
    public function getVillageArmy($villageId) {
        $stmt = $this->conn->prepare("SELECT * FROM army WHERE village_id = ? AND quantity > 0");
        $stmt->execute([$villageId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function attackVillage($attackerVillageId, $defenderVillageId, $attackingUnits) {
        // منطق المعركة
        $attackPower = $this->calculateAttackPower($attackingUnits);
        $defensePower = $this->calculateDefensePower($defenderVillageId);
        
        $battleResult = $this->simulateBattle($attackPower, $defensePower);
        
        // تسجيل نتيجة المعركة
        $this->logBattleResult($attackerVillageId, $defenderVillageId, $battleResult);
        
        return $battleResult;
    }
    
    private function calculateAttackPower($units) {
        $unitPowers = [
            'warrior' => 10,
            'archer' => 15,
            'cavalry' => 25,
            'siege' => 50
        ];
        
        $totalPower = 0;
        foreach ($units as $unitType => $quantity) {
            $totalPower += $unitPowers[$unitType] * $quantity;
        }
        
        return $totalPower;
    }
    
    private function calculateDefensePower($villageId) {
        $defenseUnits = $this->getVillageArmy($villageId);
        $unitDefenses = [
            'warrior' => 8,
            'archer' => 12,
            'cavalry' => 20,
            'siege' => 5
        ];
        
        $totalDefense = 0;
        foreach ($defenseUnits as $unit) {
            $totalDefense += $unitDefenses[$unit['unit_type']] * $unit['quantity'];
        }
        
        return $totalDefense;
    }
}
?>